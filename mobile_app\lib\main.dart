import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/app.dart';
import 'core/constants/app_constants.dart';
import 'core/services/storage_service.dart';
import 'core/services/notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تهيئة Hive للتخزين المحلي
  await Hive.initFlutter();
  
  // تهيئة خدمات التطبيق
  await _initializeServices();
  
  // تعيين اتجاه الشاشة
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  // تعيين شريط الحالة
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: AppConstants.darkColor,
      systemNavigationBarIconBrightness: Brightness.light,
    ),
  );
  
  runApp(
    const ProviderScope(
      child: StreamingPlatformApp(),
    ),
  );
}

/// تهيئة خدمات التطبيق
Future<void> _initializeServices() async {
  try {
    // تهيئة خدمة التخزين
    await StorageService.init();
    
    // تهيئة خدمة الإشعارات
    await NotificationService.init();
    
    print('✅ تم تهيئة جميع الخدمات بنجاح');
  } catch (e) {
    print('❌ خطأ في تهيئة الخدمات: $e');
  }
}
