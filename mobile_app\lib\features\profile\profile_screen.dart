import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../core/constants/app_constants.dart';
import '../../core/app.dart';

/// 👤 شاشة الملف الشخصي
class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);
    final user = authState.userData;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // شريط التطبيق مع معلومات المستخدم
          _buildSliverAppBar(context, user),
          
          // إحصائيات المستخدم
          _buildUserStats(context),
          
          // قائمة الخيارات
          _buildMenuOptions(context, ref),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar(BuildContext context, Map<String, dynamic>? user) {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      backgroundColor: AppConstants.darkColor,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppConstants.primaryColor,
                AppConstants.darkColor,
              ],
            ),
          ),
          child: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // صورة المستخدم
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 3,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: CircleAvatar(
                    radius: 37,
                    backgroundColor: AppConstants.secondaryColor,
                    backgroundImage: user?['avatar'] != null
                        ? NetworkImage(user!['avatar'])
                        : null,
                    child: user?['avatar'] == null
                        ? Text(
                            user?['first_name']?.substring(0, 1).toUpperCase() ?? 'U',
                            style: const TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          )
                        : null,
                  ),
                ),
                
                const SizedBox(height: 12),
                
                // اسم المستخدم
                Text(
                  '${user?['first_name'] ?? ''} ${user?['last_name'] ?? ''}',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                
                const SizedBox(height: 4),
                
                // نوع الاشتراك
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getSubscriptionColor(user?['subscription_type']),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getSubscriptionLabel(user?['subscription_type']),
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.edit),
          onPressed: () {
            // تعديل الملف الشخصي
          },
        ),
      ],
    );
  }

  Widget _buildUserStats(BuildContext context) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppConstants.secondaryColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: AppConstants.cardShadow,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem(
              icon: Icons.movie,
              label: 'أفلام مشاهدة',
              value: '127',
              color: AppConstants.primaryColor,
            ),
            _buildStatItem(
              icon: Icons.tv,
              label: 'مسلسلات',
              value: '23',
              color: AppConstants.infoColor,
            ),
            _buildStatItem(
              icon: Icons.access_time,
              label: 'ساعات مشاهدة',
              value: '342',
              color: AppConstants.successColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(25),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppConstants.textColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppConstants.textMutedColor,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMenuOptions(BuildContext context, WidgetRef ref) {
    final menuItems = [
      MenuItemData(
        icon: Icons.favorite,
        title: 'المفضلة',
        subtitle: 'الأفلام والمسلسلات المفضلة',
        onTap: () => context.push('/favorites'),
      ),
      MenuItemData(
        icon: Icons.bookmark,
        title: 'قائمة المشاهدة',
        subtitle: 'المحتوى المحفوظ للمشاهدة لاحقاً',
        onTap: () => context.push('/watchlist'),
      ),
      MenuItemData(
        icon: Icons.download,
        title: 'التحميلات',
        subtitle: 'المحتوى المحمل للمشاهدة بدون إنترنت',
        onTap: () => context.push('/downloads'),
      ),
      MenuItemData(
        icon: Icons.history,
        title: 'سجل المشاهدة',
        subtitle: 'المحتوى الذي شاهدته مؤخراً',
        onTap: () => context.push('/history'),
      ),
      MenuItemData(
        icon: Icons.crown,
        title: 'الاشتراك',
        subtitle: 'إدارة اشتراكك والفواتير',
        onTap: () => context.push('/subscription'),
        trailing: const Icon(
          Icons.star,
          color: Colors.amber,
          size: 20,
        ),
      ),
      MenuItemData(
        icon: Icons.settings,
        title: 'الإعدادات',
        subtitle: 'إعدادات التطبيق والحساب',
        onTap: () => context.push('/settings'),
      ),
      MenuItemData(
        icon: Icons.help,
        title: 'المساعدة والدعم',
        subtitle: 'الأسئلة الشائعة والتواصل معنا',
        onTap: () => context.push('/help'),
      ),
      MenuItemData(
        icon: Icons.info,
        title: 'حول التطبيق',
        subtitle: 'معلومات التطبيق والإصدار',
        onTap: () => context.push('/about'),
      ),
      MenuItemData(
        icon: Icons.logout,
        title: 'تسجيل الخروج',
        subtitle: 'الخروج من الحساب',
        onTap: () => _showLogoutDialog(context, ref),
        isDestructive: true,
      ),
    ];

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final item = menuItems[index];
          return _buildMenuItem(context, item);
        },
        childCount: menuItems.length,
      ),
    );
  }

  Widget _buildMenuItem(BuildContext context, MenuItemData item) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: AppConstants.secondaryColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: item.isDestructive 
                ? AppConstants.dangerColor.withOpacity(0.1)
                : AppConstants.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            item.icon,
            color: item.isDestructive 
                ? AppConstants.dangerColor
                : AppConstants.primaryColor,
            size: 20,
          ),
        ),
        title: Text(
          item.title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: item.isDestructive 
                ? AppConstants.dangerColor
                : AppConstants.textColor,
          ),
        ),
        subtitle: Text(
          item.subtitle,
          style: const TextStyle(
            fontSize: 12,
            color: AppConstants.textMutedColor,
          ),
        ),
        trailing: item.trailing ?? const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: AppConstants.textMutedColor,
        ),
        onTap: item.onTap,
      ),
    );
  }

  Color _getSubscriptionColor(String? subscriptionType) {
    switch (subscriptionType) {
      case 'premium':
        return Colors.amber;
      case 'vip':
        return Colors.purple;
      case 'basic':
        return AppConstants.infoColor;
      default:
        return AppConstants.textMutedColor;
    }
  }

  String _getSubscriptionLabel(String? subscriptionType) {
    switch (subscriptionType) {
      case 'premium':
        return 'بريميوم';
      case 'vip':
        return 'VIP';
      case 'basic':
        return 'أساسي';
      default:
        return 'مجاني';
    }
  }

  void _showLogoutDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(authStateProvider.notifier).logout();
              context.go('/auth/login');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.dangerColor,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}

/// 📋 بيانات عنصر القائمة
class MenuItemData {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;
  final Widget? trailing;
  final bool isDestructive;

  const MenuItemData({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
    this.trailing,
    this.isDestructive = false,
  });
}
