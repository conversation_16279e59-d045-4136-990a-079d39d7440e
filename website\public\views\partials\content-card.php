<?php
/**
 * 🎬 بطاقة المحتوى
 * مكون قابل لإعادة الاستخدام لعرض الأفلام والمسلسلات
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}

// التأكد من وجود متغير المحتوى
if (!isset($content) || !$content) {
    return;
}

// تحديد متغيرات إضافية
$canAccess = canAccessContent($content);
$isFavorite = IS_LOGGED_IN ? isInFavorites($content['id'], $currentUser['id']) : false;
$isInWatchlist = IS_LOGGED_IN ? isInWatchlist($content['id'], $currentUser['id']) : false;
$showProgress = isset($showProgress) ? $showProgress : false;
$cardSize = isset($cardSize) ? $cardSize : 'normal'; // normal, small, large
?>

<div class="content-card <?php echo $cardSize; ?>-card" data-content-id="<?php echo $content['id']; ?>">
    <!-- Poster Container -->
    <div class="card-poster">
        <!-- Main Image -->
        <img src="<?php echo $content['poster']; ?>" 
             alt="<?php echo htmlspecialchars($content['title']); ?>"
             class="poster-image"
             loading="lazy"
             onerror="this.src='/assets/images/default-poster.jpg'">
        
        <!-- Overlay -->
        <div class="card-overlay">
            <!-- Play Button -->
            <?php if ($canAccess): ?>
            <a href="/player/<?php echo $content['id']; ?>" class="play-btn" title="مشاهدة الآن">
                <i class="fas fa-play"></i>
            </a>
            <?php else: ?>
            <a href="/subscription" class="play-btn subscription-required" title="اشترك للمشاهدة">
                <i class="fas fa-crown"></i>
            </a>
            <?php endif; ?>
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <?php if (IS_LOGGED_IN): ?>
                <!-- Add to Favorites -->
                <button class="quick-action-btn favorite-btn <?php echo $isFavorite ? 'active' : ''; ?>" 
                        data-content-id="<?php echo $content['id']; ?>"
                        title="<?php echo $isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة'; ?>">
                    <i class="<?php echo $isFavorite ? 'fas' : 'far'; ?> fa-heart"></i>
                </button>
                
                <!-- Add to Watchlist -->
                <button class="quick-action-btn watchlist-btn <?php echo $isInWatchlist ? 'active' : ''; ?>" 
                        data-content-id="<?php echo $content['id']; ?>"
                        title="<?php echo $isInWatchlist ? 'إزالة من قائمة المشاهدة' : 'إضافة لقائمة المشاهدة'; ?>">
                    <i class="fas fa-bookmark"></i>
                </button>
                
                <!-- More Options -->
                <div class="dropdown">
                    <button class="quick-action-btn dropdown-toggle" title="المزيد">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="dropdown-menu">
                        <a href="/content/<?php echo $content['id']; ?>" class="dropdown-item">
                            <i class="fas fa-info-circle"></i>
                            تفاصيل أكثر
                        </a>
                        
                        <?php if ($content['trailer_url']): ?>
                        <button class="dropdown-item trailer-btn" data-url="<?php echo $content['trailer_url']; ?>">
                            <i class="fas fa-video"></i>
                            المقطع الدعائي
                        </button>
                        <?php endif; ?>
                        
                        <button class="dropdown-item share-btn" data-content-id="<?php echo $content['id']; ?>">
                            <i class="fas fa-share-alt"></i>
                            مشاركة
                        </button>
                        
                        <?php if (hasSubscription('premium')): ?>
                        <button class="dropdown-item download-btn" data-content-id="<?php echo $content['id']; ?>">
                            <i class="fas fa-download"></i>
                            تحميل
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Badges -->
        <div class="card-badges">
            <?php if ($content['is_new']): ?>
            <span class="badge badge-new">جديد</span>
            <?php endif; ?>
            
            <?php if ($content['is_featured']): ?>
            <span class="badge badge-featured">مميز</span>
            <?php endif; ?>
            
            <?php if ($content['is_trending']): ?>
            <span class="badge badge-trending">رائج</span>
            <?php endif; ?>
            
            <?php if (!$canAccess): ?>
            <span class="badge badge-premium">
                <i class="fas fa-crown"></i>
                <?php echo ucfirst($content['subscription_required']); ?>
            </span>
            <?php endif; ?>
        </div>
        
        <!-- Quality Badge -->
        <?php if ($content['video_quality']): ?>
        <div class="quality-badge">
            <?php echo $content['video_quality']; ?>
        </div>
        <?php endif; ?>
        
        <!-- Progress Bar (for continue watching) -->
        <?php if ($showProgress && isset($content['progress_percentage'])): ?>
        <div class="progress-bar">
            <div class="progress-fill" 
                 style="width: <?php echo $content['progress_percentage']; ?>%"></div>
        </div>
        <?php endif; ?>
        
        <!-- Duration -->
        <?php if ($content['duration']): ?>
        <div class="duration-badge">
            <?php echo formatDuration($content['duration']); ?>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Card Info -->
    <div class="card-info">
        <!-- Title -->
        <h3 class="card-title">
            <a href="/content/<?php echo $content['id']; ?>" title="<?php echo htmlspecialchars($content['title']); ?>">
                <?php echo htmlspecialchars($content['title']); ?>
            </a>
        </h3>
        
        <!-- Meta Information -->
        <div class="card-meta">
            <!-- Type and Year -->
            <div class="meta-row">
                <span class="content-type">
                    <?php echo $content['type'] === 'movie' ? 'فيلم' : 'مسلسل'; ?>
                </span>
                
                <?php if ($content['release_date']): ?>
                <span class="release-year">
                    <?php echo date('Y', strtotime($content['release_date'])); ?>
                </span>
                <?php endif; ?>
            </div>
            
            <!-- Rating and Views -->
            <div class="meta-row">
                <?php if ($content['our_rating']): ?>
                <div class="rating">
                    <i class="fas fa-star"></i>
                    <span><?php echo $content['our_rating']; ?></span>
                </div>
                <?php endif; ?>
                
                <div class="view-count">
                    <i class="fas fa-eye"></i>
                    <span><?php echo formatNumber($content['view_count']); ?></span>
                </div>
            </div>
            
            <!-- Genre -->
            <?php if ($content['genre']): ?>
            <div class="genre-tags">
                <?php 
                $genres = explode(',', $content['genre']);
                $displayGenres = array_slice($genres, 0, 2); // عرض أول نوعين فقط
                foreach ($displayGenres as $genre): 
                ?>
                <span class="genre-tag"><?php echo trim($genre); ?></span>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
            
            <!-- Progress Text (for continue watching) -->
            <?php if ($showProgress && isset($content['progress_percentage'])): ?>
            <div class="progress-text">
                <?php echo round($content['progress_percentage']); ?>% مكتمل
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Description (for large cards) -->
        <?php if ($cardSize === 'large' && $content['description']): ?>
        <p class="card-description">
            <?php echo htmlspecialchars(substr($content['description'], 0, 120)) . '...'; ?>
        </p>
        <?php endif; ?>
        
        <!-- Action Buttons (for large cards) -->
        <?php if ($cardSize === 'large'): ?>
        <div class="card-actions">
            <?php if ($canAccess): ?>
            <a href="/player/<?php echo $content['id']; ?>" class="btn btn-primary btn-sm">
                <i class="fas fa-play"></i>
                مشاهدة
            </a>
            <?php else: ?>
            <a href="/subscription" class="btn btn-primary btn-sm">
                <i class="fas fa-crown"></i>
                اشترك
            </a>
            <?php endif; ?>
            
            <a href="/content/<?php echo $content['id']; ?>" class="btn btn-outline btn-sm">
                <i class="fas fa-info-circle"></i>
                تفاصيل
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
// تهيئة بطاقة المحتوى
document.addEventListener('DOMContentLoaded', function() {
    initContentCard();
});

function initContentCard() {
    // تهيئة أزرار المفضلة
    initFavoriteButtons();
    
    // تهيئة أزرار قائمة المشاهدة
    initWatchlistButtons();
    
    // تهيئة أزرار المشاركة
    initShareButtons();
    
    // تهيئة أزرار التحميل
    initDownloadButtons();
    
    // تهيئة القوائم المنسدلة
    initDropdowns();
    
    // تهيئة المقاطع الدعائية
    initTrailerButtons();
}

function initFavoriteButtons() {
    const favoriteButtons = document.querySelectorAll('.favorite-btn');
    
    favoriteButtons.forEach(button => {
        button.addEventListener('click', async function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            if (!APP.user.isLoggedIn) {
                APP.ui.showNotification('يرجى تسجيل الدخول أولاً', 'warning');
                return;
            }
            
            const contentId = this.dataset.contentId;
            const icon = this.querySelector('i');
            const isActive = this.classList.contains('active');
            
            try {
                const response = await APP.api.post('/api/favorites/toggle', {
                    content_id: contentId
                });
                
                if (response.success) {
                    if (response.is_favorite) {
                        this.classList.add('active');
                        icon.className = 'fas fa-heart';
                        this.title = 'إزالة من المفضلة';
                        APP.ui.showNotification('تم إضافة المحتوى للمفضلة', 'success');
                    } else {
                        this.classList.remove('active');
                        icon.className = 'far fa-heart';
                        this.title = 'إضافة للمفضلة';
                        APP.ui.showNotification('تم إزالة المحتوى من المفضلة', 'info');
                    }
                }
            } catch (error) {
                APP.ui.showNotification('خطأ في العملية', 'error');
            }
        });
    });
}

function initWatchlistButtons() {
    const watchlistButtons = document.querySelectorAll('.watchlist-btn');
    
    watchlistButtons.forEach(button => {
        button.addEventListener('click', async function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            if (!APP.user.isLoggedIn) {
                APP.ui.showNotification('يرجى تسجيل الدخول أولاً', 'warning');
                return;
            }
            
            const contentId = this.dataset.contentId;
            
            try {
                const response = await APP.api.post('/api/watchlist/toggle', {
                    content_id: contentId
                });
                
                if (response.success) {
                    if (response.in_watchlist) {
                        this.classList.add('active');
                        this.title = 'إزالة من قائمة المشاهدة';
                        APP.ui.showNotification('تم إضافة المحتوى لقائمة المشاهدة', 'success');
                    } else {
                        this.classList.remove('active');
                        this.title = 'إضافة لقائمة المشاهدة';
                        APP.ui.showNotification('تم إزالة المحتوى من قائمة المشاهدة', 'info');
                    }
                }
            } catch (error) {
                APP.ui.showNotification('خطأ في العملية', 'error');
            }
        });
    });
}

function initShareButtons() {
    const shareButtons = document.querySelectorAll('.share-btn');
    
    shareButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const contentId = this.dataset.contentId;
            const contentUrl = `${window.location.origin}/content/${contentId}`;
            
            if (navigator.share) {
                navigator.share({
                    title: 'شاهد هذا المحتوى',
                    url: contentUrl
                });
            } else {
                // نسخ الرابط للحافظة
                navigator.clipboard.writeText(contentUrl).then(() => {
                    APP.ui.showNotification('تم نسخ الرابط', 'success');
                });
            }
        });
    });
}

function initDownloadButtons() {
    const downloadButtons = document.querySelectorAll('.download-btn');
    
    downloadButtons.forEach(button => {
        button.addEventListener('click', async function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            if (!APP.user.isLoggedIn) {
                APP.ui.showNotification('يرجى تسجيل الدخول أولاً', 'warning');
                return;
            }
            
            const contentId = this.dataset.contentId;
            
            try {
                const response = await APP.api.post('/api/downloads/add', {
                    content_id: contentId
                });
                
                if (response.success) {
                    APP.ui.showNotification('تم إضافة المحتوى لقائمة التحميل', 'success');
                } else {
                    APP.ui.showNotification(response.error || 'فشل في إضافة التحميل', 'error');
                }
            } catch (error) {
                APP.ui.showNotification('خطأ في العملية', 'error');
            }
        });
    });
}

function initDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown');
    
    dropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        if (toggle && menu) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // إغلاق القوائم الأخرى
                document.querySelectorAll('.dropdown-menu.show').forEach(otherMenu => {
                    if (otherMenu !== menu) {
                        otherMenu.classList.remove('show');
                    }
                });
                
                // تبديل القائمة الحالية
                menu.classList.toggle('show');
            });
        }
    });
    
    // إغلاق القوائم عند النقر خارجها
    document.addEventListener('click', function() {
        document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
            menu.classList.remove('show');
        });
    });
}

function initTrailerButtons() {
    const trailerButtons = document.querySelectorAll('.trailer-btn');
    
    trailerButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const trailerUrl = this.dataset.url;
            if (trailerUrl) {
                // فتح المقطع الدعائي في نافذة منبثقة أو modal
                openTrailerModal(trailerUrl);
            }
        });
    });
}

function openTrailerModal(url) {
    // إنشاء modal للمقطع الدعائي
    const modal = document.createElement('div');
    modal.className = 'trailer-modal-overlay';
    modal.innerHTML = `
        <div class="trailer-modal">
            <div class="trailer-modal-header">
                <h3>المقطع الدعائي</h3>
                <button class="trailer-modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="trailer-modal-body">
                <iframe src="${url}" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
    
    // إغلاق المودال
    const closeBtn = modal.querySelector('.trailer-modal-close');
    closeBtn.addEventListener('click', function() {
        document.body.removeChild(modal);
        document.body.style.overflow = '';
    });
    
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeBtn.click();
        }
    });
}
</script>

<?php
/**
 * دوال مساعدة لبطاقة المحتوى
 */

function formatDuration($minutes) {
    if ($minutes < 60) {
        return $minutes . 'د';
    }
    
    $hours = floor($minutes / 60);
    $remainingMinutes = $minutes % 60;
    
    if ($remainingMinutes > 0) {
        return $hours . 'س ' . $remainingMinutes . 'د';
    } else {
        return $hours . 'س';
    }
}

function formatNumber($number) {
    if ($number < 1000) {
        return $number;
    } elseif ($number < 1000000) {
        return round($number / 1000, 1) . 'ك';
    } else {
        return round($number / 1000000, 1) . 'م';
    }
}
?>
