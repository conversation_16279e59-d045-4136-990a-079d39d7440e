/* 🎛️ لوحة التحكم الإدارية */

:root {
    --admin-primary: #2563eb;
    --admin-secondary: #64748b;
    --admin-success: #10b981;
    --admin-warning: #f59e0b;
    --admin-danger: #ef4444;
    --admin-dark: #1e293b;
    --admin-light: #f8fafc;
    --admin-border: #e2e8f0;
    --admin-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', sans-serif;
    background: var(--admin-light);
    color: var(--admin-dark);
    line-height: 1.6;
}

/* Layout */
.admin-layout {
    display: flex;
    min-height: 100vh;
}

.admin-sidebar {
    width: 280px;
    background: white;
    border-left: 1px solid var(--admin-border);
    box-shadow: var(--admin-shadow);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.admin-main {
    flex: 1;
    margin-right: 280px;
    padding: 0;
}

.admin-header {
    background: white;
    border-bottom: 1px solid var(--admin-border);
    padding: 1rem 2rem;
    box-shadow: var(--admin-shadow);
    position: sticky;
    top: 0;
    z-index: 999;
}

.admin-content {
    padding: 2rem;
}

/* Sidebar */
.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--admin-border);
    text-align: center;
}

.sidebar-logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--admin-primary);
    text-decoration: none;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-section {
    margin-bottom: 2rem;
}

.nav-section-title {
    padding: 0.5rem 1.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--admin-secondary);
    letter-spacing: 0.05em;
}

.nav-item {
    display: block;
    padding: 0.75rem 1.5rem;
    color: var(--admin-dark);
    text-decoration: none;
    transition: all 0.2s;
    border-right: 3px solid transparent;
}

.nav-item:hover {
    background: var(--admin-light);
    color: var(--admin-primary);
}

.nav-item.active {
    background: rgba(37, 99, 235, 0.1);
    color: var(--admin-primary);
    border-right-color: var(--admin-primary);
}

.nav-item i {
    width: 20px;
    margin-left: 0.75rem;
    text-align: center;
}

/* Header */
.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--admin-dark);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-search {
    position: relative;
}

.header-search input {
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    border: 1px solid var(--admin-border);
    border-radius: 0.5rem;
    background: var(--admin-light);
    width: 300px;
}

.header-search i {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--admin-secondary);
}

.header-user {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: background 0.2s;
}

.header-user:hover {
    background: var(--admin-light);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--admin-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

/* Cards */
.card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: var(--admin-shadow);
    border: 1px solid var(--admin-border);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--admin-border);
    background: var(--admin-light);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--admin-dark);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem 1.5rem;
    background: var(--admin-light);
    border-top: 1px solid var(--admin-border);
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--admin-shadow);
    border: 1px solid var(--admin-border);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: var(--stat-color, var(--admin-primary));
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.stat-title {
    font-size: 0.875rem;
    color: var(--admin-secondary);
    font-weight: 500;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(37, 99, 235, 0.1);
    color: var(--admin-primary);
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--admin-dark);
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stat-change.positive {
    color: var(--admin-success);
}

.stat-change.negative {
    color: var(--admin-danger);
}

/* Tables */
.table-container {
    background: white;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: var(--admin-shadow);
    border: 1px solid var(--admin-border);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--admin-border);
}

.table th {
    background: var(--admin-light);
    font-weight: 600;
    color: var(--admin-dark);
    font-size: 0.875rem;
}

.table tbody tr:hover {
    background: rgba(37, 99, 235, 0.05);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
}

.btn-primary {
    background: var(--admin-primary);
    color: white;
}

.btn-primary:hover {
    background: #1d4ed8;
}

.btn-secondary {
    background: var(--admin-secondary);
    color: white;
}

.btn-success {
    background: var(--admin-success);
    color: white;
}

.btn-warning {
    background: var(--admin-warning);
    color: white;
}

.btn-danger {
    background: var(--admin-danger);
    color: white;
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--admin-border);
    color: var(--admin-dark);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--admin-dark);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--admin-border);
    border-radius: 0.5rem;
    background: white;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-left: 2.5rem;
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-color: var(--admin-success);
    color: #065f46;
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: var(--admin-warning);
    color: #92400e;
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--admin-danger);
    color: #991b1b;
}

.alert-info {
    background: rgba(37, 99, 235, 0.1);
    border-color: var(--admin-primary);
    color: #1e40af;
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
}

.badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--admin-success);
}

.badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--admin-warning);
}

.badge-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--admin-danger);
}

.badge-primary {
    background: rgba(37, 99, 235, 0.1);
    color: var(--admin-primary);
}

/* Responsive */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s;
    }
    
    .admin-sidebar.open {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-right: 0;
    }
    
    .header-search input {
        width: 200px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Utilities */
.text-center { text-align: center; }
.text-left { text-align: right; }
.text-right { text-align: left; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 2rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 2rem; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }

.justify-content-between { justify-content: space-between; }
.justify-content-center { justify-content: center; }
.align-items-center { align-items: center; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }
