# ☸️ دليل Kubernetes - منصة البث العربية

## 🎯 **نظرة عامة**

هذا الدليل يوضح كيفية نشر منصة البث العربية على Kubernetes لتحقيق قابلية التوسع العالية والموثوقية والإدارة المتقدمة.

### **مزايا Kubernetes**
- **قابلية التوسع التلقائية**: تكييف الموارد حسب الحاجة
- **الشفاء الذاتي**: إعادة تشغيل الحاويات المعطلة تلقائياً
- **توزيع الأحمال**: توزيع الطلبات على عدة نسخ
- **التحديثات المتدرجة**: تحديث التطبيق بدون انقطاع
- **إدارة الأسرار**: حماية البيانات الحساسة

---

## 🏗️ **بنية Kubernetes**

### **Cluster Architecture**
```
┌─────────────────────────────────────────────────────────────┐
│                    Kubernetes Cluster                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  Master Node                            │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│  │  │ API Server  │ │   etcd      │ │ Scheduler   │       │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘       │ │
│  │  ┌─────────────┐ ┌─────────────┐                       │ │
│  │  │ Controller  │ │ Cloud Ctrl  │                       │ │
│  │  │  Manager    │ │  Manager    │                       │ │
│  │  └─────────────┘ └─────────────┘                       │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  Worker Nodes                           │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│  │  │   Node 1    │ │   Node 2    │ │   Node 3    │       │ │
│  │  │             │ │             │ │             │       │ │
│  │  │ ┌─────────┐ │ │ ┌─────────┐ │ │ ┌─────────┐ │       │ │
│  │  │ │  Pods   │ │ │ │  Pods   │ │ │ │  Pods   │ │       │ │
│  │  │ └─────────┘ │ │ └─────────┘ │ │ └─────────┘ │       │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 📦 **Namespace Configuration**

### **إنشاء Namespaces**
```yaml
# namespaces.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: streaming-platform
  labels:
    name: streaming-platform
    environment: production
---
apiVersion: v1
kind: Namespace
metadata:
  name: streaming-platform-staging
  labels:
    name: streaming-platform-staging
    environment: staging
---
apiVersion: v1
kind: Namespace
metadata:
  name: monitoring
  labels:
    name: monitoring
    purpose: monitoring
---
apiVersion: v1
kind: Namespace
metadata:
  name: ingress-nginx
  labels:
    name: ingress-nginx
    purpose: ingress
```

---

## 🔐 **Secrets and ConfigMaps**

### **Database Secrets**
```yaml
# secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: database-secrets
  namespace: streaming-platform
type: Opaque
data:
  mysql-root-password: <base64-encoded-password>
  mysql-user: <base64-encoded-username>
  mysql-password: <base64-encoded-password>
  mysql-database: <base64-encoded-database-name>
---
apiVersion: v1
kind: Secret
metadata:
  name: redis-secrets
  namespace: streaming-platform
type: Opaque
data:
  redis-password: <base64-encoded-password>
---
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  namespace: streaming-platform
type: Opaque
data:
  jwt-secret: <base64-encoded-jwt-secret>
  app-key: <base64-encoded-app-key>
  mail-password: <base64-encoded-mail-password>
```

### **Application ConfigMap**
```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: streaming-platform
data:
  APP_ENV: "production"
  APP_DEBUG: "false"
  APP_URL: "https://streaming-platform.com"
  DB_CONNECTION: "mysql"
  DB_HOST: "mysql-service"
  DB_PORT: "3306"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  CACHE_DRIVER: "redis"
  SESSION_DRIVER: "redis"
  QUEUE_CONNECTION: "redis"
  MAIL_MAILER: "smtp"
  MAIL_HOST: "smtp.gmail.com"
  MAIL_PORT: "587"
  MAIL_ENCRYPTION: "tls"
```

---

## 🗄️ **Database Deployments**

### **MySQL Deployment**
```yaml
# mysql-deployment.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mysql-pvc
  namespace: streaming-platform
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: fast-ssd
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql
  namespace: streaming-platform
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        ports:
        - containerPort: 3306
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: mysql-root-password
        - name: MYSQL_USER
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: mysql-user
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: mysql-password
        - name: MYSQL_DATABASE
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: mysql-database
        volumeMounts:
        - name: mysql-storage
          mountPath: /var/lib/mysql
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
      volumes:
      - name: mysql-storage
        persistentVolumeClaim:
          claimName: mysql-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: mysql-service
  namespace: streaming-platform
spec:
  selector:
    app: mysql
  ports:
  - port: 3306
    targetPort: 3306
  type: ClusterIP
```

### **Redis Deployment**
```yaml
# redis-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: streaming-platform
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command:
        - redis-server
        - --requirepass
        - $(REDIS_PASSWORD)
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secrets
              key: redis-password
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: streaming-platform
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
  type: ClusterIP
```

---

## 🌐 **Application Deployments**

### **Web Application Deployment**
```yaml
# web-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: web-app
  namespace: streaming-platform
  labels:
    app: web-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: web-app
  template:
    metadata:
      labels:
        app: web-app
    spec:
      containers:
      - name: web-app
        image: streaming-platform/web:latest
        ports:
        - containerPort: 80
        envFrom:
        - configMapRef:
            name: app-config
        env:
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: mysql-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: jwt-secret
        - name: APP_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: app-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: web-service
  namespace: streaming-platform
spec:
  selector:
    app: web-app
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
```

### **API Deployment**
```yaml
# api-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-app
  namespace: streaming-platform
  labels:
    app: api-app
spec:
  replicas: 5
  selector:
    matchLabels:
      app: api-app
  template:
    metadata:
      labels:
        app: api-app
    spec:
      containers:
      - name: api-app
        image: streaming-platform/api:latest
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: app-config
        env:
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: mysql-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: api-service
  namespace: streaming-platform
spec:
  selector:
    app: api-app
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP
```

---

## 🔄 **Auto Scaling**

### **Horizontal Pod Autoscaler**
```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: web-app-hpa
  namespace: streaming-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: web-app
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-app-hpa
  namespace: streaming-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-app
  minReplicas: 5
  maxReplicas: 50
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### **Vertical Pod Autoscaler**
```yaml
# vpa.yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: web-app-vpa
  namespace: streaming-platform
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: web-app
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: web-app
      maxAllowed:
        cpu: 2
        memory: 2Gi
      minAllowed:
        cpu: 100m
        memory: 128Mi
```

---

## 🌐 **Ingress Configuration**

### **NGINX Ingress Controller**
```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: streaming-platform-ingress
  namespace: streaming-platform
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - streaming-platform.com
    - api.streaming-platform.com
    secretName: streaming-platform-tls
  rules:
  - host: streaming-platform.com
    http:
      paths:
      - path: /(.*)
        pathType: Prefix
        backend:
          service:
            name: web-service
            port:
              number: 80
  - host: api.streaming-platform.com
    http:
      paths:
      - path: /(.*)
        pathType: Prefix
        backend:
          service:
            name: api-service
            port:
              number: 8080
```

---

## 📊 **Monitoring and Logging**

### **Prometheus Monitoring**
```yaml
# prometheus.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus:latest
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus
        - name: prometheus-data
          mountPath: /prometheus
        command:
        - /bin/prometheus
        - --config.file=/etc/prometheus/prometheus.yml
        - --storage.tsdb.path=/prometheus
        - --web.console.libraries=/etc/prometheus/console_libraries
        - --web.console.templates=/etc/prometheus/consoles
        - --storage.tsdb.retention.time=30d
        - --web.enable-lifecycle
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
      - name: prometheus-data
        persistentVolumeClaim:
          claimName: prometheus-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus-service
  namespace: monitoring
spec:
  selector:
    app: prometheus
  ports:
  - port: 9090
    targetPort: 9090
  type: ClusterIP
```

### **Grafana Dashboard**
```yaml
# grafana.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:latest
        ports:
        - containerPort: 3000
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          value: "admin123"
        volumeMounts:
        - name: grafana-data
          mountPath: /var/lib/grafana
      volumes:
      - name: grafana-data
        persistentVolumeClaim:
          claimName: grafana-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: grafana-service
  namespace: monitoring
spec:
  selector:
    app: grafana
  ports:
  - port: 3000
    targetPort: 3000
  type: ClusterIP
```

---

## 🔄 **CI/CD Pipeline**

### **GitLab CI/CD Configuration**
```yaml
# .gitlab-ci.yml
stages:
  - build
  - test
  - deploy-staging
  - deploy-production

variables:
  DOCKER_REGISTRY: registry.gitlab.com/streaming-platform
  KUBE_NAMESPACE: streaming-platform

build:
  stage: build
  script:
    - docker build -t $DOCKER_REGISTRY/web:$CI_COMMIT_SHA .
    - docker push $DOCKER_REGISTRY/web:$CI_COMMIT_SHA

test:
  stage: test
  script:
    - composer install
    - php artisan test
    - npm test

deploy-staging:
  stage: deploy-staging
  script:
    - kubectl set image deployment/web-app web-app=$DOCKER_REGISTRY/web:$CI_COMMIT_SHA -n streaming-platform-staging
    - kubectl rollout status deployment/web-app -n streaming-platform-staging
  environment:
    name: staging
    url: https://staging.streaming-platform.com
  only:
    - develop

deploy-production:
  stage: deploy-production
  script:
    - kubectl set image deployment/web-app web-app=$DOCKER_REGISTRY/web:$CI_COMMIT_SHA -n streaming-platform
    - kubectl rollout status deployment/web-app -n streaming-platform
  environment:
    name: production
    url: https://streaming-platform.com
  only:
    - main
  when: manual
```

---

## 🛠️ **Management Scripts**

### **Deployment Script**
```bash
#!/bin/bash
# deploy.sh

set -e

NAMESPACE="streaming-platform"
ENVIRONMENT=${1:-production}

echo "🚀 Deploying to $ENVIRONMENT environment..."

# Apply namespace
kubectl apply -f k8s/namespaces.yaml

# Apply secrets and configmaps
kubectl apply -f k8s/secrets.yaml
kubectl apply -f k8s/configmap.yaml

# Deploy databases
kubectl apply -f k8s/mysql-deployment.yaml
kubectl apply -f k8s/redis-deployment.yaml

# Wait for databases to be ready
echo "⏳ Waiting for databases to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/mysql -n $NAMESPACE
kubectl wait --for=condition=available --timeout=300s deployment/redis -n $NAMESPACE

# Deploy applications
kubectl apply -f k8s/web-deployment.yaml
kubectl apply -f k8s/api-deployment.yaml

# Wait for applications to be ready
echo "⏳ Waiting for applications to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/web-app -n $NAMESPACE
kubectl wait --for=condition=available --timeout=300s deployment/api-app -n $NAMESPACE

# Apply autoscaling
kubectl apply -f k8s/hpa.yaml

# Apply ingress
kubectl apply -f k8s/ingress.yaml

echo "✅ Deployment completed successfully!"
echo "🌐 Application URL: https://streaming-platform.com"
```

### **Monitoring Script**
```bash
#!/bin/bash
# monitor.sh

NAMESPACE="streaming-platform"

echo "📊 Kubernetes Cluster Status"
echo "============================"

echo "🔍 Nodes:"
kubectl get nodes

echo ""
echo "🏠 Namespaces:"
kubectl get namespaces

echo ""
echo "📦 Pods in $NAMESPACE:"
kubectl get pods -n $NAMESPACE

echo ""
echo "🔧 Services in $NAMESPACE:"
kubectl get services -n $NAMESPACE

echo ""
echo "📈 Resource Usage:"
kubectl top nodes
kubectl top pods -n $NAMESPACE

echo ""
echo "🔄 Recent Events:"
kubectl get events -n $NAMESPACE --sort-by='.lastTimestamp' | tail -10
```

---

## 🔧 **Troubleshooting Commands**

### **Common Debugging Commands**
```bash
# Check pod logs
kubectl logs -f deployment/web-app -n streaming-platform

# Describe pod for detailed information
kubectl describe pod <pod-name> -n streaming-platform

# Execute commands in pod
kubectl exec -it <pod-name> -n streaming-platform -- /bin/bash

# Port forward for local testing
kubectl port-forward service/web-service 8080:80 -n streaming-platform

# Check resource usage
kubectl top pods -n streaming-platform
kubectl top nodes

# Check cluster events
kubectl get events --sort-by='.lastTimestamp' -n streaming-platform

# Scale deployment manually
kubectl scale deployment web-app --replicas=5 -n streaming-platform

# Restart deployment
kubectl rollout restart deployment/web-app -n streaming-platform

# Check rollout status
kubectl rollout status deployment/web-app -n streaming-platform

# Rollback to previous version
kubectl rollout undo deployment/web-app -n streaming-platform
```

---

**☸️ دليل Kubernetes شامل ومتطور لمنصة البث العربية**

آخر تحديث: 15 يناير 2024
