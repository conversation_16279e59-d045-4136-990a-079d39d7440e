# 🔧 دليل استكشاف الأخطاء - منصة البث العربية

## 🎯 **نظرة عامة**

هذا الدليل يساعدك في تشخيص وحل المشاكل الشائعة في منصة البث العربية. يغطي جميع المكونات من الخادم إلى المتصفح.

---

## 🚨 **المشاكل الشائعة وحلولها**

### **1. مشاكل التثبيت والإعداد**

#### **خطأ: Composer dependencies conflict**
```bash
# المشكلة
composer install
# خطأ: Package conflicts detected

# الحل
composer clear-cache
composer update --no-dev
composer install --optimize-autoloader
```

#### **خطأ: Database connection failed**
```bash
# فحص الاتصال
php artisan tinker
>>> DB::connection()->getPdo();

# إصلاح الإعدادات
# تحقق من .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=streaming_platform
DB_USERNAME=your_username
DB_PASSWORD=your_password

# إعادة تشغيل الخدمات
sudo systemctl restart mysql
php artisan config:clear
php artisan cache:clear
```

#### **خطأ: Permission denied**
```bash
# إصلاح أذونات الملفات
sudo chown -R www-data:www-data /var/www/streaming-platform
sudo chmod -R 755 /var/www/streaming-platform
sudo chmod -R 775 /var/www/streaming-platform/storage
sudo chmod -R 775 /var/www/streaming-platform/bootstrap/cache

# إصلاح أذونات SELinux (إذا كان مفعلاً)
sudo setsebool -P httpd_can_network_connect 1
sudo chcon -R -t httpd_exec_t /var/www/streaming-platform
```

### **2. مشاكل قاعدة البيانات**

#### **خطأ: Table doesn't exist**
```bash
# تشغيل migrations
php artisan migrate

# إعادة إنشاء قاعدة البيانات
php artisan migrate:fresh --seed

# فحص حالة migrations
php artisan migrate:status
```

#### **خطأ: Slow query performance**
```sql
-- فحص الاستعلامات البطيئة
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- تفعيل slow query log
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1;

-- فحص الفهارس المفقودة
EXPLAIN SELECT * FROM movies WHERE title LIKE '%action%';

-- إضافة فهارس
CREATE INDEX idx_movies_title ON movies(title);
CREATE FULLTEXT INDEX idx_movies_search ON movies(title, description);
```

#### **خطأ: Database lock timeout**
```sql
-- فحص العمليات المعلقة
SHOW PROCESSLIST;

-- إنهاء العمليات المعلقة
KILL [process_id];

-- تحسين إعدادات InnoDB
SET GLOBAL innodb_lock_wait_timeout = 120;
SET GLOBAL innodb_deadlock_detect = ON;
```

### **3. مشاكل الخادم**

#### **خطأ: 500 Internal Server Error**
```bash
# فحص سجلات الأخطاء
tail -f /var/log/nginx/error.log
tail -f /var/www/streaming-platform/storage/logs/laravel.log

# فحص إعدادات PHP
php -m | grep required_extension
php --ini

# إعادة تشغيل الخدمات
sudo systemctl restart nginx
sudo systemctl restart php8.1-fpm
```

#### **خطأ: 502 Bad Gateway**
```bash
# فحص حالة PHP-FPM
sudo systemctl status php8.1-fpm

# فحص إعدادات Nginx
sudo nginx -t

# فحص الاتصال بين Nginx و PHP-FPM
sudo netstat -tuln | grep 9000

# إصلاح إعدادات PHP-FPM
# /etc/php/8.1/fpm/pool.d/www.conf
listen = /var/run/php/php8.1-fpm.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660
```

#### **خطأ: 504 Gateway Timeout**
```nginx
# تحسين إعدادات Nginx
# /etc/nginx/sites-available/streaming-platform
location ~ \.php$ {
    fastcgi_read_timeout 300;
    fastcgi_connect_timeout 300;
    fastcgi_send_timeout 300;
}
```

### **4. مشاكل الأداء**

#### **بطء تحميل الصفحات**
```bash
# فحص استخدام الموارد
htop
iotop
free -h
df -h

# تحسين OPcache
# /etc/php/8.1/fpm/conf.d/10-opcache.ini
opcache.enable=1
opcache.memory_consumption=256
opcache.max_accelerated_files=20000
opcache.validate_timestamps=0

# تحسين Redis
redis-cli info memory
redis-cli config get maxmemory-policy
redis-cli config set maxmemory-policy allkeys-lru
```

#### **استهلاك ذاكرة عالي**
```bash
# فحص استهلاك الذاكرة
ps aux --sort=-%mem | head -10

# تحسين إعدادات PHP
# /etc/php/8.1/fpm/php.ini
memory_limit = 256M
max_execution_time = 120

# تحسين MySQL
# /etc/mysql/mysql.conf.d/mysqld.cnf
innodb_buffer_pool_size = 1G
query_cache_size = 256M
```

### **5. مشاكل الفيديو والوسائط**

#### **فشل تشغيل الفيديو**
```javascript
// فحص دعم المتصفح للفيديو
const video = document.createElement('video');
console.log('MP4 support:', video.canPlayType('video/mp4'));
console.log('WebM support:', video.canPlayType('video/webm'));

// فحص أخطاء الفيديو
video.addEventListener('error', (e) => {
    console.error('Video error:', e.target.error);
    switch(e.target.error.code) {
        case e.target.error.MEDIA_ERR_ABORTED:
            console.log('Video playback aborted');
            break;
        case e.target.error.MEDIA_ERR_NETWORK:
            console.log('Network error');
            break;
        case e.target.error.MEDIA_ERR_DECODE:
            console.log('Video decode error');
            break;
        case e.target.error.MEDIA_ERR_SRC_NOT_SUPPORTED:
            console.log('Video format not supported');
            break;
    }
});
```

#### **بطء تحميل الفيديو**
```bash
# فحص سرعة الشبكة
curl -o /dev/null -s -w "%{time_total}\n" https://example.com/video.mp4

# تحسين إعدادات CDN
# CloudFlare settings
- Enable "Auto Minify"
- Set "Browser Cache TTL" to 1 month
- Enable "Always Online"

# تحسين ضغط الفيديو
ffmpeg -i input.mp4 -c:v libx264 -crf 23 -preset medium -c:a aac -b:a 128k output.mp4
```

### **6. مشاكل المصادقة والجلسات**

#### **انتهاء صلاحية الجلسة بسرعة**
```php
<?php
// config/session.php
'lifetime' => 120, // دقيقة
'expire_on_close' => false,
'encrypt' => true,
'http_only' => true,
'same_site' => 'lax',

// فحص إعدادات الجلسة
php artisan tinker
>>> config('session');
```

#### **فشل تسجيل الدخول**
```bash
# فحص سجلات المصادقة
tail -f storage/logs/laravel.log | grep -i auth

# فحص إعدادات JWT
php artisan jwt:secret

# تنظيف cache المصادقة
php artisan auth:clear-resets
php artisan cache:forget users
```

### **7. مشاكل API**

#### **خطأ: API rate limit exceeded**
```php
<?php
// تحسين إعدادات Rate Limiting
// app/Http/Kernel.php
'api' => [
    'throttle:api',
    \Illuminate\Routing\Middleware\SubstituteBindings::class,
],

// config/cache.php - تحسين cache للـ rate limiting
'throttle' => [
    'driver' => 'redis',
    'connection' => 'default',
],
```

#### **خطأ: CORS policy**
```php
<?php
// config/cors.php
return [
    'paths' => ['api/*', 'sanctum/csrf-cookie'],
    'allowed_methods' => ['*'],
    'allowed_origins' => ['http://localhost:3000', 'https://yourdomain.com'],
    'allowed_origins_patterns' => [],
    'allowed_headers' => ['*'],
    'exposed_headers' => [],
    'max_age' => 0,
    'supports_credentials' => true,
];
```

---

## 🔍 **أدوات التشخيص**

### **فحص صحة النظام**
```bash
#!/bin/bash
# health-check.sh

echo "=== فحص صحة النظام ==="

# فحص الخدمات
echo "1. فحص الخدمات:"
systemctl is-active nginx && echo "✓ Nginx يعمل" || echo "✗ Nginx متوقف"
systemctl is-active mysql && echo "✓ MySQL يعمل" || echo "✗ MySQL متوقف"
systemctl is-active redis && echo "✓ Redis يعمل" || echo "✗ Redis متوقف"

# فحص المنافذ
echo "2. فحص المنافذ:"
netstat -tuln | grep :80 && echo "✓ Port 80 مفتوح" || echo "✗ Port 80 مغلق"
netstat -tuln | grep :443 && echo "✓ Port 443 مفتوح" || echo "✗ Port 443 مغلق"
netstat -tuln | grep :3306 && echo "✓ MySQL Port مفتوح" || echo "✗ MySQL Port مغلق"

# فحص المساحة
echo "3. فحص المساحة:"
df -h | grep -E "(/$|/var)" | awk '{print $5 " " $6}' | while read line; do
    usage=$(echo $line | awk '{print $1}' | sed 's/%//')
    mount=$(echo $line | awk '{print $2}')
    if [ $usage -gt 80 ]; then
        echo "⚠ $mount استخدام عالي: $usage%"
    else
        echo "✓ $mount استخدام طبيعي: $usage%"
    fi
done

# فحص الذاكرة
echo "4. فحص الذاكرة:"
free -h | grep Mem | awk '{print "استخدام الذاكرة: " $3 "/" $2}'

# فحص قاعدة البيانات
echo "5. فحص قاعدة البيانات:"
php artisan tinker --execute="try { DB::connection()->getPdo(); echo 'قاعدة البيانات متصلة'; } catch(Exception \$e) { echo 'خطأ في قاعدة البيانات: ' . \$e->getMessage(); }"
```

### **مراقبة الأداء المباشر**
```bash
#!/bin/bash
# performance-monitor.sh

echo "=== مراقبة الأداء المباشر ==="

while true; do
    clear
    echo "$(date): مراقبة الأداء"
    echo "=========================="
    
    # CPU Usage
    echo "استخدام CPU:"
    top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//'
    
    # Memory Usage
    echo "استخدام الذاكرة:"
    free -h | grep Mem | awk '{print $3 "/" $2 " (" $3/$2*100 "%)"}'
    
    # Active Connections
    echo "الاتصالات النشطة:"
    netstat -an | grep :80 | wc -l
    
    # MySQL Processes
    echo "عمليات MySQL:"
    mysql -e "SHOW PROCESSLIST;" | wc -l
    
    # Redis Memory
    echo "ذاكرة Redis:"
    redis-cli info memory | grep used_memory_human
    
    sleep 5
done
```

---

## 📋 **قوائم فحص سريعة**

### **قائمة فحص ما بعد النشر**
- [ ] جميع الخدمات تعمل (Nginx, PHP-FPM, MySQL, Redis)
- [ ] قاعدة البيانات متصلة ومحدثة
- [ ] ملفات الإعدادات صحيحة (.env, config files)
- [ ] الأذونات صحيحة (755 للملفات، 775 للمجلدات)
- [ ] SSL certificates صالحة
- [ ] CDN يعمل بشكل صحيح
- [ ] النسخ الاحتياطية تعمل
- [ ] المراقبة والتنبيهات مفعلة

### **قائمة فحص الأداء**
- [ ] أوقات الاستجابة < 2 ثانية
- [ ] استخدام CPU < 70%
- [ ] استخدام الذاكرة < 80%
- [ ] استخدام القرص < 85%
- [ ] Cache hit ratio > 90%
- [ ] Database query time < 100ms
- [ ] Error rate < 1%

### **قائمة فحص الأمان**
- [ ] جميع التحديثات الأمنية مثبتة
- [ ] Firewall مفعل ومكون بشكل صحيح
- [ ] SSL/TLS certificates صالحة
- [ ] كلمات المرور قوية ومشفرة
- [ ] النسخ الاحتياطية مشفرة
- [ ] سجلات الأمان تُراجع بانتظام
- [ ] اختبارات الاختراق تُجرى دورياً

---

## 📞 **الحصول على المساعدة**

### **معلومات النظام للدعم**
```bash
#!/bin/bash
# system-info.sh

echo "=== معلومات النظام للدعم ==="
echo "نظام التشغيل: $(lsb_release -d | cut -f2)"
echo "إصدار PHP: $(php -v | head -n1)"
echo "إصدار MySQL: $(mysql --version)"
echo "إصدار Nginx: $(nginx -v 2>&1)"
echo "إصدار Redis: $(redis-server --version)"
echo "إصدار Laravel: $(php artisan --version)"
echo "الذاكرة المتاحة: $(free -h | grep Mem | awk '{print $2}')"
echo "مساحة القرص: $(df -h / | tail -1 | awk '{print $2}')"
echo "عدد المعالجات: $(nproc)"
```

### **قنوات الدعم**
- **GitHub Issues**: https://github.com/streaming-platform/issues
- **Discord**: #troubleshooting قناة في Discord
- **البريد الإلكتروني**: <EMAIL>
- **الوثائق**: https://docs.streaming-platform.com

### **معلومات مطلوبة عند طلب الدعم**
1. وصف مفصل للمشكلة
2. خطوات إعادة إنتاج المشكلة
3. رسائل الخطأ الكاملة
4. معلومات النظام (استخدم system-info.sh)
5. سجلات الأخطاء ذات الصلة
6. لقطات شاشة إن أمكن

---

## 🔄 **الصيانة الدورية**

### **مهام يومية**
```bash
# فحص السجلات
tail -100 /var/log/nginx/error.log
tail -100 /var/www/streaming-platform/storage/logs/laravel.log

# فحص استخدام الموارد
df -h
free -h
top -bn1 | head -20

# فحص النسخ الاحتياطية
ls -la /backups/ | tail -5
```

### **مهام أسبوعية**
```bash
# تنظيف السجلات القديمة
find /var/log -name "*.log" -mtime +7 -delete
php artisan log:clear

# تحسين قاعدة البيانات
mysql -e "OPTIMIZE TABLE movies, users, views;"

# تحديث الإحصائيات
php artisan analytics:update
```

### **مهام شهرية**
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade

# فحص الأمان
sudo lynis audit system

# مراجعة الأداء
php artisan performance:report
```

---

**🔧 دليل شامل لاستكشاف الأخطاء وحلها في منصة البث العربية**

آخر تحديث: 15 يناير 2024
