# 🔒 ملف .htaccess لمنصة البث - حماية وتحسين شامل
# تم تطويره خصيصاً لمنصة البث العربية

# ===== تفعيل إعادة الكتابة =====
RewriteEngine On

# ===== الحماية الأساسية =====

# منع الوصول للملفات الحساسة
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|sql)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول لمجلدات النظام
RedirectMatch 403 ^.*/\.(git|svn|hg)/.*$

# حماية ملفات PHP من التنفيذ المباشر
<Files "*.php">
    Order Allow,Deny
    Allow from all
</Files>

# منع عرض محتويات المجلدات
Options -Indexes

# ===== الحماية من الهجمات =====

# حماية من SQL Injection
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} base64_encode.*\(.*\) [NC,OR]
RewriteCond %{QUERY_STRING} base64_(en|de)code[^(]*\([^)]*\) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2})
RewriteRule ^(.*)$ - [F,L]

# حماية من XSS
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC]
RewriteRule ^(.*)$ - [F,L]

# حماية من File Inclusion
RewriteCond %{QUERY_STRING} \.\./\.\./\.\./etc/passwd [NC,OR]
RewriteCond %{QUERY_STRING} \.\./\.\./\.\./boot\.ini [NC,OR]
RewriteCond %{QUERY_STRING} \.\./\.\./etc/passwd [NC,OR]
RewriteCond %{QUERY_STRING} \.\./\.\./boot\.ini [NC]
RewriteRule ^(.*)$ - [F,L]

# ===== إعادة التوجيه الذكي =====

# إعادة توجيه الصفحة الرئيسية
RewriteRule ^$ simple-index.php [L]
RewriteRule ^index/?$ simple-index.php [L]
RewriteRule ^home/?$ simple-index.php [L]

# إعادة توجيه صفحات المحتوى
RewriteRule ^watch/([0-9]+)/?$ player.php?id=$1 [L,QSA]
RewriteRule ^movie/([0-9]+)/?$ player.php?id=$1&type=movie [L,QSA]
RewriteRule ^series/([0-9]+)/?$ player.php?id=$1&type=series [L,QSA]
RewriteRule ^episode/([0-9]+)/?$ player.php?id=$1&type=episode [L,QSA]

# إعادة توجيه صفحات البحث
RewriteRule ^search/?$ search.php [L,QSA]
RewriteRule ^search/([^/]+)/?$ search.php?q=$1 [L,QSA]

# إعادة توجيه التصنيفات
RewriteRule ^category/([^/]+)/?$ category.php?name=$1 [L,QSA]
RewriteRule ^genre/([^/]+)/?$ category.php?genre=$1 [L,QSA]

# إعادة توجيه صفحات المستخدم
RewriteRule ^profile/?$ profile.php [L]
RewriteRule ^favorites/?$ favorites.php [L]
RewriteRule ^watchlist/?$ watchlist.php [L]

# ===== تحسين الأداء =====

# تفعيل الضغط
<IfModule mod_deflate.c>
    # ضغط النصوص
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json

    # ضغط الخطوط
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE application/x-font-woff
    AddOutputFilterByType DEFLATE application/x-font-woff2
</IfModule>

# تفعيل التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On

    # الصور
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"

    # الفيديو والصوت
    ExpiresByType video/mp4 "access plus 1 month"
    ExpiresByType video/webm "access plus 1 month"
    ExpiresByType audio/mp3 "access plus 1 month"
    ExpiresByType audio/ogg "access plus 1 month"

    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
    ExpiresByType application/x-javascript "access plus 1 week"

    # الخطوط
    ExpiresByType application/x-font-ttf "access plus 1 month"
    ExpiresByType font/opentype "access plus 1 month"
    ExpiresByType application/x-font-woff "access plus 1 month"
    ExpiresByType application/x-font-woff2 "access plus 1 month"

    # HTML
    ExpiresByType text/html "access plus 1 hour"

    # XML و JSON
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType application/json "access plus 1 hour"
</IfModule>

# إعدادات Cache-Control
<IfModule mod_headers.c>
    # الصور
    <FilesMatch "\.(jpg|jpeg|png|gif|webp|svg)$">
        Header set Cache-Control "max-age=2592000, public"
    </FilesMatch>

    # CSS و JavaScript
    <FilesMatch "\.(css|js)$">
        Header set Cache-Control "max-age=604800, public"
    </FilesMatch>

    # الخطوط
    <FilesMatch "\.(ttf|otf|woff|woff2|eot)$">
        Header set Cache-Control "max-age=2592000, public"
    </FilesMatch>

    # الفيديو
    <FilesMatch "\.(mp4|webm|ogg)$">
        Header set Cache-Control "max-age=2592000, public"
    </FilesMatch>

    # إعدادات الأمان
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# ===== تحسين MIME Types =====
<IfModule mod_mime.c>
    # الفيديو
    AddType video/mp4 .mp4
    AddType video/webm .webm
    AddType video/ogg .ogv

    # الصوت
    AddType audio/mp3 .mp3
    AddType audio/ogg .ogg
    AddType audio/wav .wav

    # الخطوط
    AddType application/x-font-ttf .ttf
    AddType application/x-font-woff .woff
    AddType application/x-font-woff2 .woff2
    AddType font/opentype .otf

    # أخرى
    AddType application/json .json
    AddType application/manifest+json .webmanifest
</IfModule>

# ===== إعدادات PHP =====
<IfModule mod_php.c>
    # تحسين الذاكرة
    php_value memory_limit 256M
    php_value max_execution_time 300
    php_value max_input_time 300

    # رفع الملفات
    php_value upload_max_filesize 100M
    php_value post_max_size 100M
    php_value max_file_uploads 20

    # الجلسات
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1

    # إخفاء معلومات PHP
    php_flag expose_php off

    # تفعيل OPcache
    php_flag opcache.enable 1
    php_value opcache.memory_consumption 128
    php_value opcache.max_accelerated_files 4000
</IfModule>

# ===== معالجة الأخطاء =====

# صفحات الخطأ المخصصة
ErrorDocument 400 /error.php?code=400
ErrorDocument 401 /error.php?code=401
ErrorDocument 403 /error.php?code=403
ErrorDocument 404 /404.php
ErrorDocument 500 /500.php
ErrorDocument 502 /error.php?code=502
ErrorDocument 503 /error.php?code=503

# ===== إعدادات إضافية =====

# منع الوصول للملفات المخفية
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# تحسين الاتصال
<IfModule mod_headers.c>
    Header unset ETag
    Header unset Last-Modified
</IfModule>

FileETag None

# إعدادات CORS للـ API
<IfModule mod_headers.c>
    <FilesMatch "\.(php)$">
        Header set Access-Control-Allow-Origin "*"
        Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        Header set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
    </FilesMatch>
</IfModule>

# نهاية ملف .htaccess