<?php
/**
 * 🔌 API الرئيسي للمنصة
 * نقطة دخول موحدة لجميع طلبات API
 */

// تعريف ثابت المنصة
define('STREAMING_PLATFORM', true);

// تحميل ملف التهيئة
require_once dirname(__DIR__) . '/../includes/init.php';

// إعداد headers للـ API
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// معالجة طلبات OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

/**
 * فئة API الرئيسية
 */
class StreamingAPI {
    private $db;
    private $method;
    private $endpoint;
    private $params;
    private $user;
    
    public function __construct() {
        global $db, $currentUser;
        
        $this->db = $db;
        $this->user = $currentUser;
        $this->method = $_SERVER['REQUEST_METHOD'];
        
        // تحليل المسار
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $path = str_replace('/api/', '', $path);
        $pathParts = explode('/', trim($path, '/'));
        
        $this->endpoint = $pathParts[0] ?? '';
        $this->params = array_slice($pathParts, 1);
        
        // معالجة الطلب
        $this->handleRequest();
    }
    
    private function handleRequest() {
        try {
            switch ($this->endpoint) {
                case 'content':
                    $this->handleContent();
                    break;
                    
                case 'auth':
                    $this->handleAuth();
                    break;
                    
                case 'user':
                    $this->handleUser();
                    break;
                    
                case 'search':
                    $this->handleSearch();
                    break;
                    
                case 'stats':
                    $this->handleStats();
                    break;
                    
                default:
                    $this->sendResponse(['error' => 'Endpoint not found'], 404);
            }
        } catch (Exception $e) {
            $this->sendResponse(['error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * معالجة طلبات المحتوى
     */
    private function handleContent() {
        switch ($this->method) {
            case 'GET':
                if (empty($this->params)) {
                    // قائمة المحتوى
                    $this->getContentList();
                } else {
                    // محتوى محدد
                    $this->getContent($this->params[0]);
                }
                break;
                
            case 'POST':
                $this->requireAuth();
                $this->createContent();
                break;
                
            case 'PUT':
                $this->requireAuth();
                $this->updateContent($this->params[0]);
                break;
                
            case 'DELETE':
                $this->requireAuth();
                $this->deleteContent($this->params[0]);
                break;
                
            default:
                $this->sendResponse(['error' => 'Method not allowed'], 405);
        }
    }
    
    private function getContentList() {
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(50, max(1, intval($_GET['limit'] ?? 12)));
        $offset = ($page - 1) * $limit;
        
        $type = $_GET['type'] ?? '';
        $genre = $_GET['genre'] ?? '';
        $featured = $_GET['featured'] ?? '';
        $trending = $_GET['trending'] ?? '';
        
        // بناء الاستعلام
        $whereConditions = ["status = 'published'"];
        $params = [];
        
        if ($type) {
            $whereConditions[] = "type = ?";
            $params[] = $type;
        }
        
        if ($genre) {
            $whereConditions[] = "genre LIKE ?";
            $params[] = "%$genre%";
        }
        
        if ($featured) {
            $whereConditions[] = "is_featured = 1";
        }
        
        if ($trending) {
            $whereConditions[] = "is_trending = 1";
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // عدد النتائج الإجمالي
        $totalQuery = "SELECT COUNT(*) as total FROM content WHERE $whereClause";
        $totalResult = $this->db->selectOne($totalQuery, $params);
        $total = $totalResult['total'] ?? 0;
        
        // الحصول على المحتوى
        $contentQuery = "SELECT * FROM content WHERE $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
        $content = $this->db->select($contentQuery, $params);
        
        $this->sendResponse([
            'success' => true,
            'data' => $content,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
    }
    
    private function getContent($id) {
        $content = $this->db->selectOne(
            "SELECT * FROM content WHERE id = ? AND status = 'published'",
            [$id]
        );
        
        if (!$content) {
            $this->sendResponse(['error' => 'Content not found'], 404);
        }
        
        // زيادة عدد المشاهدات
        $this->db->query(
            "UPDATE content SET view_count = view_count + 1 WHERE id = ?",
            [$id]
        );
        
        $this->sendResponse([
            'success' => true,
            'data' => $content
        ]);
    }
    
    /**
     * معالجة طلبات المصادقة
     */
    private function handleAuth() {
        switch ($this->params[0] ?? '') {
            case 'login':
                $this->login();
                break;
                
            case 'register':
                $this->register();
                break;
                
            case 'logout':
                $this->logout();
                break;
                
            case 'refresh':
                $this->refreshToken();
                break;
                
            default:
                $this->sendResponse(['error' => 'Auth endpoint not found'], 404);
        }
    }
    
    private function login() {
        $input = $this->getJsonInput();
        
        if (empty($input['email']) || empty($input['password'])) {
            $this->sendResponse(['error' => 'Email and password required'], 400);
        }
        
        $user = $this->db->selectOne(
            "SELECT * FROM users WHERE email = ? AND status = 'active'",
            [$input['email']]
        );
        
        if (!$user || !password_verify($input['password'], $user['password'])) {
            $this->sendResponse(['error' => 'Invalid credentials'], 401);
        }
        
        // إنشاء جلسة جديدة
        $sessionToken = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', strtotime('+30 days'));
        
        $this->db->insert('user_sessions', [
            'user_id' => $user['id'],
            'session_token' => hash('sha256', $sessionToken),
            'ip_address' => getUserIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'expires_at' => $expiresAt,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        // تحديث آخر دخول
        $this->db->update('users', [
            'last_login_at' => date('Y-m-d H:i:s')
        ], ['id' => $user['id']]);
        
        unset($user['password']);
        
        $this->sendResponse([
            'success' => true,
            'data' => [
                'user' => $user,
                'token' => $sessionToken,
                'expires_at' => $expiresAt
            ]
        ]);
    }
    
    private function register() {
        $input = $this->getJsonInput();
        
        // التحقق من البيانات المطلوبة
        $required = ['first_name', 'last_name', 'email', 'password'];
        foreach ($required as $field) {
            if (empty($input[$field])) {
                $this->sendResponse(['error' => "Field $field is required"], 400);
            }
        }
        
        // التحقق من صحة البريد الإلكتروني
        if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
            $this->sendResponse(['error' => 'Invalid email format'], 400);
        }
        
        // التحقق من عدم تكرار البريد الإلكتروني
        $existingUser = $this->db->selectOne(
            "SELECT id FROM users WHERE email = ?",
            [$input['email']]
        );
        
        if ($existingUser) {
            $this->sendResponse(['error' => 'Email already exists'], 409);
        }
        
        // إنشاء المستخدم الجديد
        $userId = $this->db->insert('users', [
            'first_name' => $input['first_name'],
            'last_name' => $input['last_name'],
            'email' => $input['email'],
            'password' => password_hash($input['password'], PASSWORD_DEFAULT),
            'role' => 'user',
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($userId) {
            $this->sendResponse([
                'success' => true,
                'message' => 'User registered successfully',
                'data' => ['user_id' => $userId]
            ]);
        } else {
            $this->sendResponse(['error' => 'Registration failed'], 500);
        }
    }
    
    /**
     * معالجة طلبات البحث
     */
    private function handleSearch() {
        $query = $_GET['q'] ?? '';
        $type = $_GET['type'] ?? '';
        $limit = min(50, max(1, intval($_GET['limit'] ?? 20)));
        
        if (empty($query)) {
            $this->sendResponse(['error' => 'Search query required'], 400);
        }
        
        $whereConditions = ["status = 'published'", "title LIKE ?"];
        $params = ["%$query%"];
        
        if ($type) {
            $whereConditions[] = "type = ?";
            $params[] = $type;
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        $results = $this->db->select(
            "SELECT * FROM content WHERE $whereClause ORDER BY view_count DESC LIMIT $limit",
            $params
        );
        
        $this->sendResponse([
            'success' => true,
            'data' => $results,
            'query' => $query,
            'count' => count($results)
        ]);
    }
    
    /**
     * معالجة طلبات الإحصائيات
     */
    private function handleStats() {
        $this->requireAuth();
        
        $stats = [
            'users' => $this->db->selectOne("SELECT COUNT(*) as count FROM users")['count'] ?? 0,
            'content' => $this->db->selectOne("SELECT COUNT(*) as count FROM content WHERE status = 'published'")['count'] ?? 0,
            'views' => $this->db->selectOne("SELECT SUM(view_count) as total FROM content")['total'] ?? 0,
            'sessions' => $this->db->selectOne("SELECT COUNT(*) as count FROM user_sessions WHERE expires_at > NOW()")['count'] ?? 0
        ];
        
        $this->sendResponse([
            'success' => true,
            'data' => $stats
        ]);
    }
    
    /**
     * دوال مساعدة
     */
    private function requireAuth() {
        if (!$this->user) {
            $this->sendResponse(['error' => 'Authentication required'], 401);
        }
    }
    
    private function getJsonInput() {
        $input = file_get_contents('php://input');
        return json_decode($input, true) ?? [];
    }
    
    private function sendResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }
}

// تشغيل API
if (DB_CONNECTED) {
    new StreamingAPI();
} else {
    http_response_code(503);
    echo json_encode([
        'error' => 'Service unavailable',
        'message' => 'Database connection failed'
    ], JSON_UNESCAPED_UNICODE);
}
