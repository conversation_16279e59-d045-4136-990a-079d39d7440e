<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بدون إنترنت - منصة البث</title>
    
    <!-- الأيقونات -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    <link rel="apple-touch-icon" href="/assets/images/icons/icon-192x192.png">
    
    <!-- الخطوط -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            color: #f9fafb;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 20px;
        }
        
        .offline-container {
            max-width: 500px;
            width: 100%;
            padding: 40px;
            background: rgba(31, 41, 55, 0.8);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(75, 85, 99, 0.3);
        }
        
        .offline-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            background: linear-gradient(135deg, #dc2626, #ef4444);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }
        
        .offline-icon svg {
            width: 60px;
            height: 60px;
            fill: white;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.7);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 20px rgba(220, 38, 38, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
            }
        }
        
        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #dc2626, #ef4444);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #d1d5db;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .message {
            font-size: 1rem;
            color: #9ca3af;
            margin-bottom: 40px;
            line-height: 1.8;
        }
        
        .actions {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #dc2626, #ef4444);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(220, 38, 38, 0.3);
        }
        
        .btn-secondary {
            background: rgba(75, 85, 99, 0.5);
            color: #d1d5db;
            border: 1px solid rgba(75, 85, 99, 0.5);
        }
        
        .btn-secondary:hover {
            background: rgba(75, 85, 99, 0.7);
            transform: translateY(-2px);
        }
        
        .tips {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .tips h3 {
            color: #60a5fa;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }
        
        .tips ul {
            list-style: none;
            text-align: right;
        }
        
        .tips li {
            color: #d1d5db;
            margin-bottom: 8px;
            padding-right: 20px;
            position: relative;
            font-size: 0.9rem;
        }
        
        .tips li::before {
            content: "•";
            color: #60a5fa;
            position: absolute;
            right: 0;
            font-weight: bold;
        }
        
        .connection-status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .status-offline {
            background: rgba(220, 38, 38, 0.2);
            color: #fca5a5;
            border: 1px solid rgba(220, 38, 38, 0.3);
        }
        
        .status-online {
            background: rgba(34, 197, 94, 0.2);
            color: #86efac;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }
        
        @media (max-width: 480px) {
            .offline-container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .subtitle {
                font-size: 1.1rem;
            }
            
            .offline-icon {
                width: 100px;
                height: 100px;
            }
            
            .offline-icon svg {
                width: 50px;
                height: 50px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <!-- أيقونة بدون إنترنت -->
        <div class="offline-icon">
            <svg viewBox="0 0 24 24">
                <path d="M23.64 7c-.45-.34-4.93-4-11.64-4-1.5 0-2.89.19-4.15.48L18.18 13.8 23.64 7zm-6.6 8.22L3.27 1.44 2 2.72l2.05 2.06C1.91 5.76.59 6.82.36 7l11.63 14.49.01.01.01-.01L16.17 17l1.42 1.42 1.27-1.27z"/>
            </svg>
        </div>
        
        <!-- العنوان الرئيسي -->
        <h1>لا يوجد اتصال بالإنترنت</h1>
        
        <!-- العنوان الفرعي -->
        <p class="subtitle">يبدو أنك غير متصل بالإنترنت حالياً</p>
        
        <!-- الرسالة -->
        <p class="message">
            لا تقلق! يمكنك تصفح المحتوى المحفوظ محلياً أو المحاولة مرة أخرى عند عودة الاتصال.
        </p>
        
        <!-- الأزرار -->
        <div class="actions">
            <button class="btn btn-primary" onclick="checkConnection()">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                </svg>
                إعادة المحاولة
            </button>
            
            <a href="/" class="btn btn-secondary">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
                العودة للرئيسية
            </a>
        </div>
        
        <!-- حالة الاتصال -->
        <div id="connectionStatus" class="connection-status status-offline">
            🔴 غير متصل بالإنترنت
        </div>
        
        <!-- نصائح -->
        <div class="tips">
            <h3>💡 نصائح للحل:</h3>
            <ul>
                <li>تحقق من اتصال الواي فاي أو البيانات</li>
                <li>أعد تشغيل جهاز التوجيه (الراوتر)</li>
                <li>تأكد من عدم تفعيل وضع الطيران</li>
                <li>جرب إعادة تحميل الصفحة</li>
                <li>تحقق من إعدادات الشبكة</li>
            </ul>
        </div>
    </div>

    <script>
        // فحص حالة الاتصال
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                statusElement.textContent = '🟢 متصل بالإنترنت';
                statusElement.className = 'connection-status status-online';
            } else {
                statusElement.textContent = '🔴 غير متصل بالإنترنت';
                statusElement.className = 'connection-status status-offline';
            }
        }
        
        // فحص الاتصال عند النقر على زر إعادة المحاولة
        function checkConnection() {
            updateConnectionStatus();
            
            if (navigator.onLine) {
                // محاولة إعادة تحميل الصفحة
                window.location.reload();
            } else {
                // عرض رسالة
                alert('لا يزال لا يوجد اتصال بالإنترنت. يرجى التحقق من الاتصال والمحاولة مرة أخرى.');
            }
        }
        
        // مراقبة تغيير حالة الاتصال
        window.addEventListener('online', function() {
            updateConnectionStatus();
            setTimeout(() => {
                if (confirm('تم استعادة الاتصال بالإنترنت! هل تريد إعادة تحميل الصفحة؟')) {
                    window.location.reload();
                }
            }, 1000);
        });
        
        window.addEventListener('offline', function() {
            updateConnectionStatus();
        });
        
        // تحديث حالة الاتصال عند تحميل الصفحة
        updateConnectionStatus();
        
        // فحص دوري لحالة الاتصال
        setInterval(updateConnectionStatus, 5000);
        
        // تسجيل Service Worker إذا لم يكن مسجلاً
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('✅ Service Worker مسجل بنجاح');
                })
                .catch(error => {
                    console.log('❌ فشل في تسجيل Service Worker:', error);
                });
        }
    </script>
</body>
</html>
