<?php
/**
 * 🧪 اختبارات API المحتوى - منصة البث العربية
 */

namespace Tests\Api;

use PHPUnit\Framework\TestCase;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class ContentApiTest extends TestCase
{
    private $client;
    private $baseUrl;
    private $authToken;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->baseUrl = 'http://localhost/api';
        $this->client = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => 10,
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ]
        ]);
        
        // الحصول على رمز المصادقة
        $this->authToken = $this->getAuthToken();
    }

    protected function tearDown(): void
    {
        $this->client = null;
        parent::tearDown();
    }

    /**
     * الحصول على رمز المصادقة للاختبارات
     */
    private function getAuthToken(): string
    {
        try {
            $response = $this->client->post('/auth/login', [
                'json' => [
                    'email' => '<EMAIL>',
                    'password' => 'password'
                ]
            ]);

            $data = json_decode($response->getBody(), true);
            return $data['data']['token'] ?? '';
        } catch (RequestException $e) {
            $this->fail('فشل في الحصول على رمز المصادقة: ' . $e->getMessage());
        }
    }

    /**
     * إرسال طلب مع المصادقة
     */
    private function authenticatedRequest(string $method, string $uri, array $options = []): \Psr\Http\Message\ResponseInterface
    {
        $options['headers'] = array_merge(
            $options['headers'] ?? [],
            ['Authorization' => 'Bearer ' . $this->authToken]
        );

        return $this->client->request($method, $uri, $options);
    }

    /**
     * اختبار الحصول على قائمة المحتوى
     */
    public function testGetContentList()
    {
        $response = $this->client->get('/content');
        
        $this->assertEquals(200, $response->getStatusCode());
        
        $data = json_decode($response->getBody(), true);
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('data', $data);
        $this->assertIsArray($data['data']);
    }

    /**
     * اختبار الحصول على محتوى محدد
     */
    public function testGetSpecificContent()
    {
        // الحصول على قائمة المحتوى أولاً
        $listResponse = $this->client->get('/content');
        $listData = json_decode($listResponse->getBody(), true);
        
        if (!empty($listData['data'])) {
            $contentId = $listData['data'][0]['id'];
            
            $response = $this->client->get("/content/{$contentId}");
            
            $this->assertEquals(200, $response->getStatusCode());
            
            $data = json_decode($response->getBody(), true);
            $this->assertTrue($data['success']);
            $this->assertArrayHasKey('data', $data);
            $this->assertEquals($contentId, $data['data']['id']);
        } else {
            $this->markTestSkipped('لا يوجد محتوى للاختبار');
        }
    }

    /**
     * اختبار البحث في المحتوى
     */
    public function testSearchContent()
    {
        $searchQuery = 'فيلم';
        $response = $this->client->get('/content/search', [
            'query' => ['q' => $searchQuery]
        ]);
        
        $this->assertEquals(200, $response->getStatusCode());
        
        $data = json_decode($response->getBody(), true);
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('data', $data);
        $this->assertIsArray($data['data']);
    }

    /**
     * اختبار إضافة محتوى جديد (يتطلب مصادقة)
     */
    public function testCreateContent()
    {
        $contentData = [
            'title' => 'فيلم اختبار',
            'description' => 'وصف فيلم الاختبار',
            'type' => 'movie',
            'genre' => 'action',
            'duration' => 7200,
            'release_year' => 2024,
            'rating' => 4.5,
            'poster_url' => 'https://example.com/poster.jpg',
            'video_url' => 'https://example.com/video.mp4'
        ];

        $response = $this->authenticatedRequest('POST', '/content', [
            'json' => $contentData
        ]);
        
        $this->assertEquals(201, $response->getStatusCode());
        
        $data = json_decode($response->getBody(), true);
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('data', $data);
        $this->assertEquals($contentData['title'], $data['data']['title']);
        
        // حفظ ID للاختبارات اللاحقة
        return $data['data']['id'];
    }

    /**
     * اختبار تحديث المحتوى
     * @depends testCreateContent
     */
    public function testUpdateContent($contentId)
    {
        $updateData = [
            'title' => 'فيلم اختبار محدث',
            'description' => 'وصف محدث لفيلم الاختبار',
            'rating' => 4.8
        ];

        $response = $this->authenticatedRequest('PUT', "/content/{$contentId}", [
            'json' => $updateData
        ]);
        
        $this->assertEquals(200, $response->getStatusCode());
        
        $data = json_decode($response->getBody(), true);
        $this->assertTrue($data['success']);
        $this->assertEquals($updateData['title'], $data['data']['title']);
        $this->assertEquals($updateData['rating'], $data['data']['rating']);
        
        return $contentId;
    }

    /**
     * اختبار حذف المحتوى
     * @depends testUpdateContent
     */
    public function testDeleteContent($contentId)
    {
        $response = $this->authenticatedRequest('DELETE', "/content/{$contentId}");
        
        $this->assertEquals(200, $response->getStatusCode());
        
        $data = json_decode($response->getBody(), true);
        $this->assertTrue($data['success']);
        
        // التحقق من حذف المحتوى
        try {
            $this->client->get("/content/{$contentId}");
            $this->fail('المحتوى لم يتم حذفه');
        } catch (RequestException $e) {
            $this->assertEquals(404, $e->getResponse()->getStatusCode());
        }
    }

    /**
     * اختبار الحصول على المحتوى حسب النوع
     */
    public function testGetContentByType()
    {
        $types = ['movie', 'series', 'documentary'];
        
        foreach ($types as $type) {
            $response = $this->client->get('/content', [
                'query' => ['type' => $type]
            ]);
            
            $this->assertEquals(200, $response->getStatusCode());
            
            $data = json_decode($response->getBody(), true);
            $this->assertTrue($data['success']);
            $this->assertIsArray($data['data']);
            
            // التحقق من أن جميع العناصر من النوع المطلوب
            foreach ($data['data'] as $item) {
                $this->assertEquals($type, $item['type']);
            }
        }
    }

    /**
     * اختبار الحصول على المحتوى الشائع
     */
    public function testGetPopularContent()
    {
        $response = $this->client->get('/content/popular');
        
        $this->assertEquals(200, $response->getStatusCode());
        
        $data = json_decode($response->getBody(), true);
        $this->assertTrue($data['success']);
        $this->assertIsArray($data['data']);
        
        // التحقق من ترتيب المحتوى حسب الشعبية
        $previousViews = PHP_INT_MAX;
        foreach ($data['data'] as $item) {
            $this->assertLessThanOrEqual($previousViews, $item['views']);
            $previousViews = $item['views'];
        }
    }

    /**
     * اختبار الحصول على أحدث المحتوى
     */
    public function testGetLatestContent()
    {
        $response = $this->client->get('/content/latest');
        
        $this->assertEquals(200, $response->getStatusCode());
        
        $data = json_decode($response->getBody(), true);
        $this->assertTrue($data['success']);
        $this->assertIsArray($data['data']);
        
        // التحقق من ترتيب المحتوى حسب التاريخ
        $previousDate = null;
        foreach ($data['data'] as $item) {
            if ($previousDate !== null) {
                $this->assertLessThanOrEqual($previousDate, $item['created_at']);
            }
            $previousDate = $item['created_at'];
        }
    }

    /**
     * اختبار تسجيل مشاهدة
     */
    public function testRecordView()
    {
        // الحصول على محتوى للاختبار
        $listResponse = $this->client->get('/content');
        $listData = json_decode($listResponse->getBody(), true);
        
        if (!empty($listData['data'])) {
            $contentId = $listData['data'][0]['id'];
            $initialViews = $listData['data'][0]['views'];
            
            $response = $this->client->post("/content/{$contentId}/view");
            
            $this->assertEquals(200, $response->getStatusCode());
            
            $data = json_decode($response->getBody(), true);
            $this->assertTrue($data['success']);
            
            // التحقق من زيادة عدد المشاهدات
            $updatedResponse = $this->client->get("/content/{$contentId}");
            $updatedData = json_decode($updatedResponse->getBody(), true);
            $this->assertEquals($initialViews + 1, $updatedData['data']['views']);
        } else {
            $this->markTestSkipped('لا يوجد محتوى للاختبار');
        }
    }

    /**
     * اختبار معالجة الأخطاء
     */
    public function testErrorHandling()
    {
        // اختبار محتوى غير موجود
        try {
            $this->client->get('/content/999999');
            $this->fail('يجب أن يرجع خطأ 404');
        } catch (RequestException $e) {
            $this->assertEquals(404, $e->getResponse()->getStatusCode());
        }
        
        // اختبار طلب غير صحيح
        try {
            $this->authenticatedRequest('POST', '/content', [
                'json' => ['title' => ''] // عنوان فارغ
            ]);
            $this->fail('يجب أن يرجع خطأ 400');
        } catch (RequestException $e) {
            $this->assertEquals(400, $e->getResponse()->getStatusCode());
        }
    }

    /**
     * اختبار حدود الطلبات (Rate Limiting)
     */
    public function testRateLimiting()
    {
        $requests = 0;
        $maxRequests = 100; // حد الطلبات المسموح
        
        try {
            for ($i = 0; $i < $maxRequests + 10; $i++) {
                $this->client->get('/content');
                $requests++;
            }
        } catch (RequestException $e) {
            // يجب أن نصل لحد الطلبات
            $this->assertEquals(429, $e->getResponse()->getStatusCode());
            $this->assertGreaterThan(0, $requests);
        }
    }
}
