<?php
/**
 * 🗄️ فئة إدارة قاعدة البيانات
 * تتعامل مع الاتصال وإدارة قاعدة البيانات
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}

class Database {
    private $connection;
    private static $instance = null;
    
    /**
     * منشئ الفئة
     */
    public function __construct() {
        $this->connect();
    }
    
    /**
     * الحصول على مثيل واحد من الفئة (Singleton)
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * الاتصال بقاعدة البيانات
     */
    private function connect() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => DB_PERSISTENT,
                PDO::ATTR_TIMEOUT => DB_TIMEOUT,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET . " COLLATE utf8mb4_unicode_ci"
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
            
            // تعيين المنطقة الزمنية
            $this->connection->exec("SET time_zone = '" . date('P') . "'");
            
        } catch (PDOException $e) {
            $this->handleConnectionError($e);
        }
    }
    
    /**
     * معالجة أخطاء الاتصال
     */
    private function handleConnectionError($exception) {
        $error_message = "Database connection failed: " . $exception->getMessage();
        
        // تسجيل الخطأ
        error_log($error_message);
        
        if (DEBUG_MODE) {
            die($error_message);
        } else {
            die('Service temporarily unavailable. Please try again later.');
        }
    }
    
    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * تنفيذ استعلام
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            $this->logError($e, $sql, $params);
            throw $e;
        }
    }
    
    /**
     * الحصول على صف واحد
     */
    public function fetchRow($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * الحصول على جميع الصفوف
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * الحصول على عدد الصفوف
     */
    public function fetchCount($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * إدراج بيانات جديدة
     */
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO " . DB_PREFIX . "{$table} ({$columns}) VALUES ({$placeholders})";
        
        $stmt = $this->query($sql, $data);
        return $this->connection->lastInsertId();
    }
    
    /**
     * تحديث البيانات
     */
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $column) {
            $setClause[] = "{$column} = :{$column}";
        }
        $setClause = implode(', ', $setClause);
        
        $sql = "UPDATE " . DB_PREFIX . "{$table} SET {$setClause} WHERE {$where}";
        
        $params = array_merge($data, $whereParams);
        $stmt = $this->query($sql, $params);
        
        return $stmt->rowCount();
    }
    
    /**
     * حذف البيانات
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM " . DB_PREFIX . "{$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->connection->commit();
    }
    
    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->connection->rollback();
    }
    
    /**
     * التحقق من وجود جدول
     */
    public function tableExists($table) {
        $sql = "SHOW TABLES LIKE '" . DB_PREFIX . $table . "'";
        $stmt = $this->query($sql);
        return $stmt->rowCount() > 0;
    }
    
    /**
     * الحصول على هيكل الجدول
     */
    public function getTableStructure($table) {
        $sql = "DESCRIBE " . DB_PREFIX . $table;
        return $this->fetchAll($sql);
    }
    
    /**
     * تحسين الجدول
     */
    public function optimizeTable($table) {
        $sql = "OPTIMIZE TABLE " . DB_PREFIX . $table;
        return $this->query($sql);
    }
    
    /**
     * إنشاء نسخة احتياطية من الجدول
     */
    public function backupTable($table, $backupName = null) {
        $backupName = $backupName ?: $table . '_backup_' . date('Y_m_d_H_i_s');
        $sql = "CREATE TABLE " . DB_PREFIX . $backupName . " AS SELECT * FROM " . DB_PREFIX . $table;
        return $this->query($sql);
    }
    
    /**
     * الحصول على إحصائيات قاعدة البيانات
     */
    public function getDatabaseStats() {
        $stats = [];
        
        // حجم قاعدة البيانات
        $sql = "SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = ?";
        $result = $this->fetchRow($sql, [DB_NAME]);
        $stats['size_mb'] = $result['size_mb'] ?? 0;
        
        // عدد الجداول
        $sql = "SELECT COUNT(*) as table_count 
                FROM information_schema.tables 
                WHERE table_schema = ? AND table_name LIKE ?";
        $result = $this->fetchRow($sql, [DB_NAME, DB_PREFIX . '%']);
        $stats['table_count'] = $result['table_count'] ?? 0;
        
        // إحصائيات الجداول الرئيسية
        $tables = ['users', 'content', 'episodes', 'reviews', 'watch_history'];
        foreach ($tables as $table) {
            $sql = "SELECT COUNT(*) as count FROM " . DB_PREFIX . $table;
            $result = $this->fetchRow($sql);
            $stats['tables'][$table] = $result['count'] ?? 0;
        }
        
        return $stats;
    }
    
    /**
     * تنظيف قاعدة البيانات
     */
    public function cleanup() {
        $cleanupQueries = [
            // حذف الجلسات المنتهية الصلاحية
            "DELETE FROM " . DB_PREFIX . "sessions WHERE expires_at < NOW()",
            
            // حذف التحميلات المنتهية الصلاحية
            "DELETE FROM " . DB_PREFIX . "downloads WHERE status = 'expired' AND expires_at < NOW()",
            
            // حذف السجلات القديمة (أكثر من 90 يوم)
            "DELETE FROM " . DB_PREFIX . "logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)",
            
            // حذف الإشعارات القديمة المقروءة (أكثر من 30 يوم)
            "DELETE FROM " . DB_PREFIX . "notifications 
             WHERE is_read = 1 AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)",
            
            // تحديث إحصائيات المحتوى
            "UPDATE " . DB_PREFIX . "content c 
             SET view_count = (
                 SELECT COUNT(*) FROM " . DB_PREFIX . "watch_history w 
                 WHERE w.content_id = c.id
             )"
        ];
        
        $results = [];
        foreach ($cleanupQueries as $sql) {
            try {
                $stmt = $this->query($sql);
                $results[] = [
                    'query' => $sql,
                    'affected_rows' => $stmt->rowCount(),
                    'success' => true
                ];
            } catch (Exception $e) {
                $results[] = [
                    'query' => $sql,
                    'error' => $e->getMessage(),
                    'success' => false
                ];
            }
        }
        
        return $results;
    }
    
    /**
     * البحث المتقدم
     */
    public function search($table, $searchFields, $searchTerm, $conditions = [], $limit = 20, $offset = 0) {
        $searchConditions = [];
        $params = [];
        
        // إضافة شروط البحث
        foreach ($searchFields as $field) {
            $searchConditions[] = "{$field} LIKE :search_term";
        }
        
        $sql = "SELECT * FROM " . DB_PREFIX . $table;
        
        $whereClause = [];
        if (!empty($searchConditions)) {
            $whereClause[] = "(" . implode(" OR ", $searchConditions) . ")";
            $params['search_term'] = "%{$searchTerm}%";
        }
        
        // إضافة شروط إضافية
        foreach ($conditions as $condition => $value) {
            $whereClause[] = $condition;
            if (is_array($value)) {
                $params = array_merge($params, $value);
            } else {
                $params[str_replace([':', '?'], '', $condition)] = $value;
            }
        }
        
        if (!empty($whereClause)) {
            $sql .= " WHERE " . implode(" AND ", $whereClause);
        }
        
        $sql .= " LIMIT {$limit} OFFSET {$offset}";
        
        return $this->fetchAll($sql, $params);
    }
    
    /**
     * تسجيل الأخطاء
     */
    private function logError($exception, $sql, $params) {
        $errorData = [
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'sql' => $sql,
            'params' => $params,
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ];
        
        error_log("Database Error: " . json_encode($errorData));
    }
    
    /**
     * إغلاق الاتصال
     */
    public function close() {
        $this->connection = null;
    }
    
    /**
     * تدمير المثيل عند انتهاء الكائن
     */
    public function __destruct() {
        $this->close();
    }
}

/**
 * فئة منشئ الاستعلامات
 */
class QueryBuilder {
    private $db;
    private $table;
    private $select = ['*'];
    private $joins = [];
    private $where = [];
    private $orderBy = [];
    private $groupBy = [];
    private $having = [];
    private $limit;
    private $offset;
    private $params = [];
    
    public function __construct(Database $db) {
        $this->db = $db;
    }
    
    /**
     * تحديد الجدول
     */
    public function table($table) {
        $this->table = DB_PREFIX . $table;
        return $this;
    }
    
    /**
     * تحديد الأعمدة
     */
    public function select($columns) {
        $this->select = is_array($columns) ? $columns : func_get_args();
        return $this;
    }
    
    /**
     * إضافة شرط WHERE
     */
    public function where($column, $operator, $value = null) {
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }
        
        $placeholder = ':where_' . count($this->where);
        $this->where[] = "{$column} {$operator} {$placeholder}";
        $this->params[$placeholder] = $value;
        
        return $this;
    }
    
    /**
     * إضافة شرط WHERE IN
     */
    public function whereIn($column, $values) {
        $placeholders = [];
        foreach ($values as $i => $value) {
            $placeholder = ':wherein_' . $i;
            $placeholders[] = $placeholder;
            $this->params[$placeholder] = $value;
        }
        
        $this->where[] = "{$column} IN (" . implode(',', $placeholders) . ")";
        return $this;
    }
    
    /**
     * إضافة JOIN
     */
    public function join($table, $first, $operator, $second) {
        $this->joins[] = "JOIN " . DB_PREFIX . "{$table} ON {$first} {$operator} {$second}";
        return $this;
    }
    
    /**
     * إضافة LEFT JOIN
     */
    public function leftJoin($table, $first, $operator, $second) {
        $this->joins[] = "LEFT JOIN " . DB_PREFIX . "{$table} ON {$first} {$operator} {$second}";
        return $this;
    }
    
    /**
     * ترتيب النتائج
     */
    public function orderBy($column, $direction = 'ASC') {
        $this->orderBy[] = "{$column} {$direction}";
        return $this;
    }
    
    /**
     * تجميع النتائج
     */
    public function groupBy($columns) {
        $this->groupBy = is_array($columns) ? $columns : func_get_args();
        return $this;
    }
    
    /**
     * تحديد عدد النتائج
     */
    public function limit($limit, $offset = 0) {
        $this->limit = $limit;
        $this->offset = $offset;
        return $this;
    }
    
    /**
     * بناء الاستعلام
     */
    private function buildQuery() {
        $sql = "SELECT " . implode(', ', $this->select) . " FROM {$this->table}";
        
        if (!empty($this->joins)) {
            $sql .= " " . implode(' ', $this->joins);
        }
        
        if (!empty($this->where)) {
            $sql .= " WHERE " . implode(' AND ', $this->where);
        }
        
        if (!empty($this->groupBy)) {
            $sql .= " GROUP BY " . implode(', ', $this->groupBy);
        }
        
        if (!empty($this->having)) {
            $sql .= " HAVING " . implode(' AND ', $this->having);
        }
        
        if (!empty($this->orderBy)) {
            $sql .= " ORDER BY " . implode(', ', $this->orderBy);
        }
        
        if ($this->limit) {
            $sql .= " LIMIT {$this->limit}";
            if ($this->offset) {
                $sql .= " OFFSET {$this->offset}";
            }
        }
        
        return $sql;
    }
    
    /**
     * تنفيذ الاستعلام والحصول على النتائج
     */
    public function get() {
        $sql = $this->buildQuery();
        return $this->db->fetchAll($sql, $this->params);
    }
    
    /**
     * الحصول على أول نتيجة
     */
    public function first() {
        $this->limit(1);
        $sql = $this->buildQuery();
        return $this->db->fetchRow($sql, $this->params);
    }
    
    /**
     * عد النتائج
     */
    public function count() {
        $this->select = ['COUNT(*) as count'];
        $sql = $this->buildQuery();
        $result = $this->db->fetchRow($sql, $this->params);
        return $result['count'] ?? 0;
    }
}
?>
