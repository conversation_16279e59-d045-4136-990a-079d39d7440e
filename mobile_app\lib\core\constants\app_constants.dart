import 'package:flutter/material.dart';

/// 🎨 ثوابت التطبيق الأساسية
class AppConstants {
  // منع إنشاء كائن من هذه الفئة
  AppConstants._();

  // ==========================================
  // 🎨 الألوان
  // ==========================================
  static const Color primaryColor = Color(0xFFE50914);
  static const Color secondaryColor = Color(0xFF221F1F);
  static const Color darkColor = Color(0xFF141414);
  static const Color lightColor = Color(0xFFF5F5F5);
  static const Color textColor = Color(0xFFFFFFFF);
  static const Color textMutedColor = Color(0xFFB3B3B3);
  static const Color borderColor = Color(0xFF333333);
  static const Color successColor = Color(0xFF28A745);
  static const Color warningColor = Color(0xFFFFC107);
  static const Color dangerColor = Color(0xFFDC3545);
  static const Color infoColor = Color(0xFF17A2B8);

  // ==========================================
  // 📱 أبعاد الشاشة
  // ==========================================
  static const double screenPadding = 16.0;
  static const double cardPadding = 12.0;
  static const double buttonHeight = 48.0;
  static const double inputHeight = 56.0;
  static const double iconSize = 24.0;
  static const double avatarSize = 40.0;

  // ==========================================
  // 🔤 أحجام الخطوط
  // ==========================================
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeXLarge = 18.0;
  static const double fontSizeXXLarge = 24.0;
  static const double fontSizeTitle = 20.0;
  static const double fontSizeHeading = 28.0;

  // ==========================================
  // 🎭 الرسوم المتحركة
  // ==========================================
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration fastAnimationDuration = Duration(milliseconds: 150);
  static const Duration slowAnimationDuration = Duration(milliseconds: 500);

  // ==========================================
  // 🌐 API والشبكة
  // ==========================================
  static const String baseUrl = 'http://localhost/streaming_platform/website/api';
  static const String mediaBaseUrl = 'http://localhost/streaming_platform/media';
  static const Duration requestTimeout = Duration(seconds: 30);
  static const Duration connectTimeout = Duration(seconds: 15);
  static const Duration receiveTimeout = Duration(seconds: 30);

  // ==========================================
  // 💾 التخزين المحلي
  // ==========================================
  static const String userBoxName = 'user_box';
  static const String settingsBoxName = 'settings_box';
  static const String cacheBoxName = 'cache_box';
  static const String downloadBoxName = 'download_box';

  // مفاتيح التخزين
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String firstLaunchKey = 'first_launch';
  static const String biometricEnabledKey = 'biometric_enabled';

  // ==========================================
  // 🎬 مشغل الفيديو
  // ==========================================
  static const Duration videoSeekDuration = Duration(seconds: 10);
  static const Duration videoBufferDuration = Duration(seconds: 30);
  static const List<double> videoPlaybackSpeeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
  static const List<String> videoQualities = ['240p', '360p', '480p', '720p', '1080p', '4K'];

  // ==========================================
  // 📱 الإشعارات
  // ==========================================
  static const String notificationChannelId = 'streaming_platform_channel';
  static const String notificationChannelName = 'منصة البث الشاملة';
  static const String notificationChannelDescription = 'إشعارات التطبيق';

  // ==========================================
  // 🔐 الأمان
  // ==========================================
  static const int maxLoginAttempts = 5;
  static const Duration lockoutDuration = Duration(minutes: 30);
  static const int pinLength = 4;
  static const Duration sessionTimeout = Duration(hours: 24);

  // ==========================================
  // 📄 التصفح والعرض
  // ==========================================
  static const int itemsPerPage = 20;
  static const int maxSearchHistory = 10;
  static const int maxRecentlyWatched = 50;
  static const Duration cacheExpiration = Duration(hours: 24);

  // ==========================================
  // 🎯 أنواع المحتوى
  // ==========================================
  static const String movieType = 'movie';
  static const String seriesType = 'series';
  static const String documentaryType = 'documentary';
  static const String liveType = 'live';

  // ==========================================
  // 💳 أنواع الاشتراك
  // ==========================================
  static const String freeSubscription = 'free';
  static const String basicSubscription = 'basic';
  static const String premiumSubscription = 'premium';
  static const String vipSubscription = 'vip';

  // ==========================================
  // 🌍 اللغات المدعومة
  // ==========================================
  static const List<Locale> supportedLocales = [
    Locale('ar', 'SA'), // العربية
    Locale('en', 'US'), // الإنجليزية
    Locale('fr', 'FR'), // الفرنسية
    Locale('tr', 'TR'), // التركية
  ];

  static const Locale defaultLocale = Locale('ar', 'SA');

  // ==========================================
  // 📱 معلومات التطبيق
  // ==========================================
  static const String appName = 'منصة البث الشاملة';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';
  static const String appPackageName = 'com.streamingplatform.app';

  // ==========================================
  // 🔗 الروابط المهمة
  // ==========================================
  static const String privacyPolicyUrl = 'https://streamingplatform.com/privacy';
  static const String termsOfServiceUrl = 'https://streamingplatform.com/terms';
  static const String supportUrl = 'https://streamingplatform.com/support';
  static const String faqUrl = 'https://streamingplatform.com/faq';

  // ==========================================
  // 📧 معلومات الاتصال
  // ==========================================
  static const String supportEmail = '<EMAIL>';
  static const String feedbackEmail = '<EMAIL>';
  static const String businessEmail = '<EMAIL>';

  // ==========================================
  // 🎨 أنماط الظلال
  // ==========================================
  static const List<BoxShadow> cardShadow = [
    BoxShadow(
      color: Color(0x1A000000),
      blurRadius: 8,
      offset: Offset(0, 2),
    ),
  ];

  static const List<BoxShadow> buttonShadow = [
    BoxShadow(
      color: Color(0x26000000),
      blurRadius: 4,
      offset: Offset(0, 2),
    ),
  ];

  // ==========================================
  // 🔄 أنماط الحدود الدائرية
  // ==========================================
  static const double borderRadiusSmall = 4.0;
  static const double borderRadiusMedium = 8.0;
  static const double borderRadiusLarge = 12.0;
  static const double borderRadiusXLarge = 16.0;
  static const double borderRadiusCircular = 50.0;

  // ==========================================
  // 📊 إعدادات الشبكة
  // ==========================================
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': 'StreamingPlatform/1.0.0',
  };

  // ==========================================
  // 🎵 الأصوات والاهتزاز
  // ==========================================
  static const Duration hapticFeedbackDuration = Duration(milliseconds: 50);
  static const bool enableSoundEffects = true;
  static const bool enableHapticFeedback = true;

  // ==========================================
  // 📱 إعدادات التطبيق
  // ==========================================
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;
  static const bool enablePerformanceMonitoring = true;
  static const bool enableDebugMode = false;

  // ==========================================
  // 🎯 رسائل الخطأ الافتراضية
  // ==========================================
  static const String networkErrorMessage = 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى';
  static const String serverErrorMessage = 'حدث خطأ في الخادم، يرجى المحاولة لاحقاً';
  static const String unknownErrorMessage = 'حدث خطأ غير متوقع';
  static const String timeoutErrorMessage = 'انتهت مهلة الاتصال، يرجى المحاولة مرة أخرى';
  static const String unauthorizedErrorMessage = 'غير مصرح لك بالوصول لهذا المحتوى';

  // ==========================================
  // 🎬 إعدادات الفيديو الافتراضية
  // ==========================================
  static const String defaultVideoQuality = '720p';
  static const double defaultPlaybackSpeed = 1.0;
  static const bool autoPlayEnabled = true;
  static const bool subtitlesEnabled = true;
  static const String defaultSubtitleLanguage = 'ar';

  // ==========================================
  // 💾 حدود التخزين
  // ==========================================
  static const int maxCacheSize = 500 * 1024 * 1024; // 500 MB
  static const int maxDownloadSize = 2 * 1024 * 1024 * 1024; // 2 GB
  static const int maxImageCacheSize = 100 * 1024 * 1024; // 100 MB

  // ==========================================
  // 🔍 إعدادات البحث
  // ==========================================
  static const int minSearchLength = 2;
  static const Duration searchDebounceDelay = Duration(milliseconds: 500);
  static const int maxSearchSuggestions = 10;

  // ==========================================
  // 📱 إعدادات الواجهة
  // ==========================================
  static const double listItemHeight = 80.0;
  static const double gridItemAspectRatio = 0.7;
  static const int gridCrossAxisCount = 2;
  static const double gridSpacing = 8.0;

  // ==========================================
  // 🎨 التدرجات اللونية
  // ==========================================
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryColor, Color(0xFFB8070F)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient darkGradient = LinearGradient(
    colors: [darkColor, secondaryColor],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  // ==========================================
  // 🔔 أنواع الإشعارات
  // ==========================================
  static const String notificationTypeNewContent = 'new_content';
  static const String notificationTypeSubscription = 'subscription';
  static const String notificationTypeReminder = 'reminder';
  static const String notificationTypePromotion = 'promotion';
}

/// 🎨 فئة الأنماط المخصصة
class AppStyles {
  AppStyles._();

  // أنماط النصوص
  static const TextStyle headingStyle = TextStyle(
    fontSize: AppConstants.fontSizeHeading,
    fontWeight: FontWeight.bold,
    color: AppConstants.textColor,
  );

  static const TextStyle titleStyle = TextStyle(
    fontSize: AppConstants.fontSizeTitle,
    fontWeight: FontWeight.w600,
    color: AppConstants.textColor,
  );

  static const TextStyle bodyStyle = TextStyle(
    fontSize: AppConstants.fontSizeMedium,
    color: AppConstants.textColor,
  );

  static const TextStyle captionStyle = TextStyle(
    fontSize: AppConstants.fontSizeSmall,
    color: AppConstants.textMutedColor,
  );

  // أنماط الأزرار
  static final ButtonStyle primaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: AppConstants.primaryColor,
    foregroundColor: AppConstants.textColor,
    minimumSize: const Size(double.infinity, AppConstants.buttonHeight),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
    ),
  );

  static final ButtonStyle secondaryButtonStyle = OutlinedButton.styleFrom(
    foregroundColor: AppConstants.primaryColor,
    side: const BorderSide(color: AppConstants.primaryColor),
    minimumSize: const Size(double.infinity, AppConstants.buttonHeight),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
    ),
  );
}
