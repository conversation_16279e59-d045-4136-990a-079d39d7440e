<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- SEO Meta Tags -->
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' : '' ?><?= DynamicSettings::get('site_name', 'منصة البث الشاملة') ?></title>
    <meta name="description" content="<?= $pageDescription ?? DynamicSettings::get('seo_description', 'شاهد أحدث الأفلام والمسلسلات بجودة عالية') ?>">
    <meta name="keywords" content="<?= DynamicSettings::get('seo_keywords', 'أفلام, مسلسلات, بث, مشاهدة') ?>">
    <meta name="author" content="<?= DynamicSettings::get('site_name', 'منصة البث الشاملة') ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?= isset($pageTitle) ? $pageTitle . ' - ' : '' ?><?= DynamicSettings::get('site_name', 'منصة البث الشاملة') ?>">
    <meta property="og:description" content="<?= $pageDescription ?? DynamicSettings::get('seo_description', 'شاهد أحدث الأفلام والمسلسلات بجودة عالية') ?>">
    <meta property="og:image" content="<?= asset('images/og-image.jpg') ?>">
    <meta property="og:url" content="<?= url($_SERVER['REQUEST_URI']) ?>">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="<?= DynamicSettings::get('site_name', 'منصة البث الشاملة') ?>">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?= isset($pageTitle) ? $pageTitle . ' - ' : '' ?><?= DynamicSettings::get('site_name', 'منصة البث الشاملة') ?>">
    <meta name="twitter:description" content="<?= $pageDescription ?? DynamicSettings::get('seo_description', 'شاهد أحدث الأفلام والمسلسلات بجودة عالية') ?>">
    <meta name="twitter:image" content="<?= asset('images/og-image.jpg') ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= asset('images/favicon.ico') ?>">
    <link rel="apple-touch-icon" href="<?= asset('images/apple-touch-icon.png') ?>">
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="<?= asset('css/main.css') ?>" rel="stylesheet">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?= generateCSRFToken() ?>">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#1a1a1a">
    <link rel="manifest" href="<?= asset('manifest.json') ?>">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="<?= asset('css/main.css') ?>" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" as="style">
</head>
<body class="<?= isset($bodyClass) ? $bodyClass : '' ?>">
    
    <!-- Loading Spinner -->
    <div id="loading-spinner" class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    </div>
    
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <!-- Logo -->
            <a class="navbar-brand" href="<?= url('/') ?>">
                <img src="<?= asset('images/logo.png') ?>" alt="<?= DynamicSettings::get('site_name', 'منصة البث الشاملة') ?>" height="40">
            </a>
            
            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= url('/') ?>">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-film"></i> أفلام
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?= url('/movies/arabic') ?>">أفلام عربية</a></li>
                            <li><a class="dropdown-item" href="<?= url('/movies/foreign') ?>">أفلام أجنبية</a></li>
                            <li><a class="dropdown-item" href="<?= url('/movies/indian') ?>">أفلام هندية</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-tv"></i> مسلسلات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?= url('/series/arabic') ?>">مسلسلات عربية</a></li>
                            <li><a class="dropdown-item" href="<?= url('/series/foreign') ?>">مسلسلات أجنبية</a></li>
                            <li><a class="dropdown-item" href="<?= url('/series/turkish') ?>">مسلسلات تركية</a></li>
                            <li><a class="dropdown-item" href="<?= url('/series/korean') ?>">مسلسلات كورية</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= url('/documentaries') ?>">
                            <i class="fas fa-file-alt"></i> وثائقيات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= url('/kids') ?>">
                            <i class="fas fa-child"></i> أطفال
                        </a>
                    </li>
                </ul>
                
                <!-- Search Form -->
                <form class="d-flex me-3" role="search">
                    <div class="input-group">
                        <input class="form-control" type="search" placeholder="ابحث عن فيلم أو مسلسل..." aria-label="Search">
                        <button class="btn btn-outline-light" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
                
                <!-- User Menu -->
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <?php $user = getCurrentUser(); ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?= $user['first_name'] ?>
                                <?php if ($user['subscription_type'] !== 'free'): ?>
                                    <span class="badge bg-warning text-dark"><?= strtoupper($user['subscription_type']) ?></span>
                                <?php endif; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="<?= url('/profile') ?>"><i class="fas fa-user-edit"></i> الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="<?= url('/watchlist') ?>"><i class="fas fa-bookmark"></i> قائمة المشاهدة</a></li>
                                <li><a class="dropdown-item" href="<?= url('/favorites') ?>"><i class="fas fa-heart"></i> المفضلة</a></li>
                                <li><a class="dropdown-item" href="<?= url('/subscription') ?>"><i class="fas fa-crown"></i> الاشتراك</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= url('/logout') ?>"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= url('/login') ?>">
                                <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-primary text-white ms-2" href="<?= url('/register') ?>">
                                <i class="fas fa-user-plus"></i> إنشاء حساب
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Flash Messages -->
    <?php $flashMessage = getFlashMessage(); ?>
    <?php if ($flashMessage): ?>
        <div class="alert alert-<?= $flashMessage['type'] === 'error' ? 'danger' : $flashMessage['type'] ?> alert-dismissible fade show mt-5" role="alert">
            <?= $flashMessage['message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <?= $content ?>
    </main>
    
    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5><?= DynamicSettings::get('site_name', 'منصة البث الشاملة') ?></h5>
                    <p><?= DynamicSettings::get('site_description', 'شاهد أحدث الأفلام والمسلسلات بجودة عالية') ?></p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6>الأقسام</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?= url('/movies') ?>" class="text-light">أفلام</a></li>
                        <li><a href="<?= url('/series') ?>" class="text-light">مسلسلات</a></li>
                        <li><a href="<?= url('/documentaries') ?>" class="text-light">وثائقيات</a></li>
                        <li><a href="<?= url('/kids') ?>" class="text-light">أطفال</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6>الحساب</h6>
                    <ul class="list-unstyled">
                        <?php if (isLoggedIn()): ?>
                            <li><a href="<?= url('/profile') ?>" class="text-light">الملف الشخصي</a></li>
                            <li><a href="<?= url('/subscription') ?>" class="text-light">الاشتراك</a></li>
                        <?php else: ?>
                            <li><a href="<?= url('/login') ?>" class="text-light">تسجيل الدخول</a></li>
                            <li><a href="<?= url('/register') ?>" class="text-light">إنشاء حساب</a></li>
                        <?php endif; ?>
                        <li><a href="<?= url('/help') ?>" class="text-light">المساعدة</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6>الشركة</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?= url('/about') ?>" class="text-light">من نحن</a></li>
                        <li><a href="<?= url('/contact') ?>" class="text-light">اتصل بنا</a></li>
                        <li><a href="<?= url('/careers') ?>" class="text-light">الوظائف</a></li>
                        <li><a href="<?= url('/press') ?>" class="text-light">الصحافة</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6>قانوني</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?= url('/privacy') ?>" class="text-light">سياسة الخصوصية</a></li>
                        <li><a href="<?= url('/terms') ?>" class="text-light">شروط الاستخدام</a></li>
                        <li><a href="<?= url('/cookies') ?>" class="text-light">ملفات تعريف الارتباط</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?= date('Y') ?> <?= DynamicSettings::get('site_name', 'منصة البث الشاملة') ?>. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="app-download-links">
                        <a href="#" class="text-light me-3">
                            <i class="fab fa-apple"></i> App Store
                        </a>
                        <a href="#" class="text-light">
                            <i class="fab fa-google-play"></i> Google Play
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?= asset('js/main.js') ?>"></script>
    
    <!-- Additional Scripts -->
    <?php if (isset($additionalScripts)): ?>
        <?= $additionalScripts ?>
    <?php endif; ?>
    
    <!-- Analytics -->
    <?php if (ANALYTICS_ENABLED && defined('GOOGLE_ANALYTICS_ID')): ?>
        <!-- Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=<?= GOOGLE_ANALYTICS_ID ?>"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '<?= GOOGLE_ANALYTICS_ID ?>');
        </script>
    <?php endif; ?>
    
</body>
</html>
