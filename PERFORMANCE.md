# ⚡ دليل تحسين الأداء - منصة البث العربية

## 🎯 **نظرة عامة**

هذا الدليل يوضح كيفية تحسين أداء منصة البث العربية لضمان تجربة مستخدم سريعة وسلسة. نغطي جميع جوانب الأداء من الخادم إلى المتصفح.

### **أهداف الأداء**
- **وقت التحميل الأولي**: < 2 ثانية
- **وقت التفاعل**: < 100ms
- **معدل الإطارات**: 60 FPS
- **استهلاك الذاكرة**: < 100MB
- **استهلاك البيانات**: محسن للشبكات البطيئة

---

## 🚀 **تحسين الخادم (Backend)**

### **تحسين قاعدة البيانات**

#### **الفهرسة المحسنة**

```sql
-- فهارس للبحث السريع
CREATE INDEX idx_movies_title ON movies(title);
CREATE INDEX idx_movies_genre ON movies(genre);
CREATE INDEX idx_movies_year ON movies(year);
CREATE INDEX idx_movies_rating ON movies(rating);

-- فهارس مركبة للاستعلامات المعقدة
CREATE INDEX idx_movies_genre_year ON movies(genre, year);
CREATE INDEX idx_movies_rating_year ON movies(rating, year);

-- فهارس للنص الكامل
CREATE FULLTEXT INDEX idx_movies_search ON movies(title, description);

-- فهارس للمفاتيح الخارجية
CREATE INDEX idx_user_favorites_user_id ON user_favorites(user_id);
CREATE INDEX idx_user_favorites_movie_id ON user_favorites(movie_id);
```

#### **تحسين الاستعلامات**

```php
<?php
// استخدام Eager Loading لتجنب N+1 Problem
$movies = Movie::with(['genre', 'director', 'cast'])
    ->where('status', 'published')
    ->orderBy('created_at', 'desc')
    ->paginate(20);

// استخدام Query Builder للاستعلامات المعقدة
$popularMovies = DB::table('movies')
    ->select('movies.*', DB::raw('AVG(ratings.score) as avg_rating'))
    ->join('ratings', 'movies.id', '=', 'ratings.movie_id')
    ->groupBy('movies.id')
    ->having('avg_rating', '>', 4.0)
    ->orderBy('avg_rating', 'desc')
    ->limit(10)
    ->get();

// استخدام Raw Queries للاستعلامات المحسنة
$trendingMovies = DB::select("
    SELECT m.*, 
           COUNT(v.id) as view_count,
           AVG(r.score) as avg_rating
    FROM movies m
    LEFT JOIN views v ON m.id = v.movie_id 
        AND v.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    LEFT JOIN ratings r ON m.id = r.movie_id
    GROUP BY m.id
    ORDER BY view_count DESC, avg_rating DESC
    LIMIT 20
");
```

### **التخزين المؤقت (Caching)**

#### **Redis للتخزين المؤقت**

```php
<?php
// تخزين مؤقت للبيانات الثابتة
Cache::remember('popular_movies', 3600, function () {
    return Movie::with('genre')
        ->where('rating', '>', 4.0)
        ->orderBy('views', 'desc')
        ->take(20)
        ->get();
});

// تخزين مؤقت للجلسات
Cache::put('user_session_' . $userId, $sessionData, 1440);

// تخزين مؤقت للبحث
$searchResults = Cache::remember("search_{$query}", 300, function () use ($query) {
    return $this->searchService->search($query);
});
```

#### **إعدادات Redis المحسنة**

```redis
# redis.conf
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### **تحسين PHP**

#### **إعدادات PHP المحسنة**

```ini
; php.ini
memory_limit = 512M
max_execution_time = 300
upload_max_filesize = 100M
post_max_size = 100M

; OPcache
opcache.enable=1
opcache.memory_consumption=256
opcache.interned_strings_buffer=16
opcache.max_accelerated_files=20000
opcache.validate_timestamps=0
opcache.save_comments=1
opcache.fast_shutdown=1
```

#### **تحسين الكود**

```php
<?php
// استخدام Generators للبيانات الكبيرة
function getMoviesGenerator($limit = 1000) {
    $offset = 0;
    $batchSize = 100;
    
    while ($offset < $limit) {
        $movies = Movie::offset($offset)->limit($batchSize)->get();
        
        if ($movies->isEmpty()) {
            break;
        }
        
        foreach ($movies as $movie) {
            yield $movie;
        }
        
        $offset += $batchSize;
    }
}

// تحسين معالجة الصور
class ImageOptimizer
{
    public function optimizeImage($imagePath, $quality = 85)
    {
        $image = imagecreatefromjpeg($imagePath);
        
        // تقليل الحجم مع الحفاظ على الجودة
        $optimized = imagescale($image, 1920, 1080, IMG_BICUBIC);
        
        // ضغط الصورة
        imagejpeg($optimized, $imagePath, $quality);
        
        imagedestroy($image);
        imagedestroy($optimized);
    }
}
```

---

## 🌐 **تحسين الواجهة الأمامية (Frontend)**

### **تحسين JavaScript**

#### **تقسيم الكود (Code Splitting)**

```javascript
// استخدام Dynamic Imports
const VideoPlayer = lazy(() => import('./components/VideoPlayer'));
const MovieDetails = lazy(() => import('./pages/MovieDetails'));

// تحميل المكونات عند الحاجة
const loadVideoPlayer = async () => {
    const { default: VideoPlayer } = await import('./components/VideoPlayer');
    return VideoPlayer;
};

// تحسين Bundle Size
import { debounce } from 'lodash-es'; // استيراد محدد
// بدلاً من: import _ from 'lodash';
```

#### **تحسين الأداء**

```javascript
// استخدام Web Workers للمهام الثقيلة
const worker = new Worker('/js/video-processor.js');
worker.postMessage({ videoData, quality: '1080p' });

// تحسين Event Listeners
const debouncedSearch = debounce((query) => {
    searchMovies(query);
}, 300);

// استخدام Intersection Observer للتحميل التدريجي
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            loadImage(entry.target);
        }
    });
});

// تحسين DOM Manipulation
const fragment = document.createDocumentFragment();
movies.forEach(movie => {
    const element = createMovieElement(movie);
    fragment.appendChild(element);
});
container.appendChild(fragment);
```

### **تحسين CSS**

#### **CSS محسن للأداء**

```css
/* استخدام CSS Grid و Flexbox للتخطيط السريع */
.movies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    contain: layout style paint;
}

/* تحسين الرسوم المتحركة */
.movie-card {
    transform: translateZ(0); /* تفعيل Hardware Acceleration */
    will-change: transform;
    transition: transform 0.2s ease-out;
}

.movie-card:hover {
    transform: scale(1.05) translateZ(0);
}

/* تحسين الخطوط */
@font-face {
    font-family: 'Cairo';
    src: url('/fonts/cairo.woff2') format('woff2');
    font-display: swap;
}

/* تحسين الصور */
.movie-poster {
    object-fit: cover;
    image-rendering: -webkit-optimize-contrast;
}
```

### **تحسين الصور والوسائط**

#### **تحسين الصور**

```html
<!-- استخدام WebP مع Fallback -->
<picture>
    <source srcset="poster.webp" type="image/webp">
    <source srcset="poster.jpg" type="image/jpeg">
    <img src="poster.jpg" alt="ملصق الفيلم" loading="lazy">
</picture>

<!-- Responsive Images -->
<img srcset="poster-320w.jpg 320w,
             poster-640w.jpg 640w,
             poster-1280w.jpg 1280w"
     sizes="(max-width: 320px) 280px,
            (max-width: 640px) 600px,
            1200px"
     src="poster-640w.jpg"
     alt="ملصق الفيلم">
```

#### **تحسين الفيديو**

```html
<!-- تحسين مشغل الفيديو -->
<video preload="metadata" 
       poster="thumbnail.jpg"
       controls
       playsinline>
    <source src="movie-1080p.mp4" type="video/mp4" media="(min-width: 1200px)">
    <source src="movie-720p.mp4" type="video/mp4" media="(min-width: 768px)">
    <source src="movie-480p.mp4" type="video/mp4">
</video>
```

---

## 🔧 **تحسين الخادم (Server)**

### **إعدادات Nginx المحسنة**

```nginx
# nginx.conf
worker_processes auto;
worker_connections 1024;

# تفعيل الضغط
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/json
    application/javascript
    application/xml+rss
    application/atom+xml
    image/svg+xml;

# تحسين التخزين المؤقت
location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary Accept-Encoding;
}

# تحسين ملفات الفيديو
location ~* \.(mp4|webm|ogg)$ {
    expires 30d;
    add_header Cache-Control "public";
    add_header Accept-Ranges bytes;
    
    # تفعيل HTTP/2 Server Push
    http2_push_preload on;
}

# تحسين الاتصالات
keepalive_timeout 65;
keepalive_requests 100;
```

### **إعدادات Apache المحسنة**

```apache
# .htaccess
# تفعيل mod_deflate
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تحسين التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType video/mp4 "access plus 1 month"
</IfModule>
```

---

## 📊 **مراقبة الأداء**

### **أدوات القياس**

#### **Google Lighthouse**

```javascript
// تشغيل Lighthouse برمجياً
const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');

async function runLighthouse(url) {
    const chrome = await chromeLauncher.launch({chromeFlags: ['--headless']});
    
    const options = {
        logLevel: 'info',
        output: 'json',
        onlyCategories: ['performance'],
        port: chrome.port,
    };

    const runnerResult = await lighthouse(url, options);
    const score = runnerResult.lhr.categories.performance.score * 100;
    
    console.log(`Performance Score: ${score}`);
    
    await chrome.kill();
    return score;
}
```

#### **Web Vitals**

```javascript
// قياس Core Web Vitals
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

getCLS(console.log);
getFID(console.log);
getFCP(console.log);
getLCP(console.log);
getTTFB(console.log);

// إرسال البيانات للتحليل
function sendToAnalytics(metric) {
    gtag('event', metric.name, {
        value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
        event_category: 'Web Vitals',
        event_label: metric.id,
        non_interaction: true,
    });
}
```

### **مراقبة الخادم**

#### **New Relic Integration**

```php
<?php
// تتبع الأداء
if (extension_loaded('newrelic')) {
    newrelic_name_transaction("Movie/View");
    newrelic_add_custom_parameter("movie_id", $movieId);
    newrelic_add_custom_parameter("user_id", $userId);
}

// تتبع الاستعلامات البطيئة
DB::listen(function ($query) {
    if ($query->time > 1000) { // أكثر من ثانية
        Log::warning('Slow Query', [
            'sql' => $query->sql,
            'time' => $query->time,
            'bindings' => $query->bindings
        ]);
    }
});
```

---

## 🎯 **تحسينات متقدمة**

### **Service Workers للتخزين المؤقت**

```javascript
// sw.js
const CACHE_NAME = 'streaming-platform-v1';
const urlsToCache = [
    '/',
    '/css/app.css',
    '/js/app.js',
    '/images/logo.png'
];

// تثبيت Service Worker
self.addEventListener('install', event => {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => cache.addAll(urlsToCache))
    );
});

// استراتيجية Cache First للأصول الثابتة
self.addEventListener('fetch', event => {
    if (event.request.destination === 'image') {
        event.respondWith(
            caches.match(event.request)
                .then(response => {
                    return response || fetch(event.request);
                })
        );
    }
});
```

### **HTTP/2 Server Push**

```php
<?php
// Laravel HTTP/2 Server Push
class Http2Middleware
{
    public function handle($request, Closure $next)
    {
        $response = $next($request);
        
        // Push critical resources
        $response->header('Link', [
            '</css/app.css>; rel=preload; as=style',
            '</js/app.js>; rel=preload; as=script',
            '</fonts/cairo.woff2>; rel=preload; as=font; crossorigin'
        ]);
        
        return $response;
    }
}
```

### **Database Connection Pooling**

```php
<?php
// config/database.php
'mysql' => [
    'driver' => 'mysql',
    'host' => env('DB_HOST', '127.0.0.1'),
    'port' => env('DB_PORT', '3306'),
    'database' => env('DB_DATABASE', 'forge'),
    'username' => env('DB_USERNAME', 'forge'),
    'password' => env('DB_PASSWORD', ''),
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
    'strict' => true,
    'engine' => null,
    'options' => [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_TIMEOUT => 30,
        PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
    ],
],
```

---

## 📈 **قياس النتائج**

### **مؤشرات الأداء الرئيسية**

| المؤشر | الهدف | الحالي | الحالة |
|---------|--------|--------|---------|
| **First Contentful Paint** | < 1.5s | 1.2s | ✅ |
| **Largest Contentful Paint** | < 2.5s | 2.1s | ✅ |
| **First Input Delay** | < 100ms | 85ms | ✅ |
| **Cumulative Layout Shift** | < 0.1 | 0.08 | ✅ |
| **Time to Interactive** | < 3.5s | 2.8s | ✅ |

### **أدوات المراقبة المستمرة**

```bash
# تشغيل اختبارات الأداء
make performance-test

# مراقبة الأداء المباشر
make monitor-performance

# تقرير الأداء الأسبوعي
make performance-report
```

---

## 🚀 **خطة التحسين المستمر**

### **المرحلة 1: التحسينات الأساسية** ✅
- تحسين قاعدة البيانات والفهرسة
- تفعيل التخزين المؤقت
- ضغط الملفات والصور

### **المرحلة 2: التحسينات المتقدمة** 🔄
- تطبيق CDN
- تحسين Service Workers
- HTTP/2 Server Push

### **المرحلة 3: التحسينات المستقبلية** 📅
- Edge Computing
- Machine Learning للتنبؤ
- WebAssembly للمهام الثقيلة

---

**⚡ دليل شامل لتحسين أداء منصة البث العربية**

آخر تحديث: 15 يناير 2024
