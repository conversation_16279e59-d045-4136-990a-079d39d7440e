import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/app_constants.dart';

/// 🎨 سمات التطبيق
class AppTheme {
  AppTheme._();

  // ==========================================
  // 🌙 السمة المظلمة (الافتراضية)
  // ==========================================
  static ThemeData get darkTheme {
    return ThemeData(
      // الألوان الأساسية
      primarySwatch: _createMaterialColor(AppConstants.primaryColor),
      primaryColor: AppConstants.primaryColor,
      scaffoldBackgroundColor: AppConstants.darkColor,
      canvasColor: AppConstants.secondaryColor,
      cardColor: AppConstants.secondaryColor,
      dividerColor: AppConstants.borderColor,
      
      // سمة الألوان
      colorScheme: const ColorScheme.dark(
        primary: AppConstants.primaryColor,
        secondary: AppConstants.primaryColor,
        surface: AppConstants.secondaryColor,
        background: AppConstants.darkColor,
        error: AppConstants.dangerColor,
        onPrimary: AppConstants.textColor,
        onSecondary: AppConstants.textColor,
        onSurface: AppConstants.textColor,
        onBackground: AppConstants.textColor,
        onError: AppConstants.textColor,
        brightness: Brightness.dark,
      ),
      
      // شريط التطبيق
      appBarTheme: const AppBarTheme(
        backgroundColor: AppConstants.darkColor,
        foregroundColor: AppConstants.textColor,
        elevation: 0,
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: AppConstants.darkColor,
          systemNavigationBarIconBrightness: Brightness.light,
        ),
        titleTextStyle: TextStyle(
          color: AppConstants.textColor,
          fontSize: AppConstants.fontSizeTitle,
          fontWeight: FontWeight.w600,
          fontFamily: 'Cairo',
        ),
        iconTheme: IconThemeData(
          color: AppConstants.textColor,
          size: AppConstants.iconSize,
        ),
      ),
      
      // النصوص
      textTheme: _buildTextTheme(),
      
      // الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppConstants.primaryColor,
          foregroundColor: AppConstants.textColor,
          minimumSize: const Size(double.infinity, AppConstants.buttonHeight),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
          elevation: 2,
          textStyle: const TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.w600,
            fontFamily: 'Cairo',
          ),
        ),
      ),
      
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppConstants.primaryColor,
          side: const BorderSide(color: AppConstants.primaryColor),
          minimumSize: const Size(double.infinity, AppConstants.buttonHeight),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
          textStyle: const TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.w600,
            fontFamily: 'Cairo',
          ),
        ),
      ),
      
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppConstants.primaryColor,
          textStyle: const TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.w600,
            fontFamily: 'Cairo',
          ),
        ),
      ),
      
      // حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppConstants.secondaryColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          borderSide: const BorderSide(color: AppConstants.borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          borderSide: const BorderSide(color: AppConstants.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          borderSide: const BorderSide(color: AppConstants.primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          borderSide: const BorderSide(color: AppConstants.dangerColor),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          borderSide: const BorderSide(color: AppConstants.dangerColor, width: 2),
        ),
        labelStyle: const TextStyle(
          color: AppConstants.textMutedColor,
          fontSize: AppConstants.fontSizeMedium,
          fontFamily: 'Cairo',
        ),
        hintStyle: const TextStyle(
          color: AppConstants.textMutedColor,
          fontSize: AppConstants.fontSizeMedium,
          fontFamily: 'Cairo',
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
      
      // البطاقات
      cardTheme: CardTheme(
        color: AppConstants.secondaryColor,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
        margin: const EdgeInsets.all(8),
      ),
      
      // القوائم
      listTileTheme: const ListTileThemeData(
        textColor: AppConstants.textColor,
        iconColor: AppConstants.textMutedColor,
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
      
      // شريط التنقل السفلي
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppConstants.secondaryColor,
        selectedItemColor: AppConstants.primaryColor,
        unselectedItemColor: AppConstants.textMutedColor,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
        selectedLabelStyle: TextStyle(
          fontSize: AppConstants.fontSizeSmall,
          fontWeight: FontWeight.w600,
          fontFamily: 'Cairo',
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: AppConstants.fontSizeSmall,
          fontFamily: 'Cairo',
        ),
      ),
      
      // الحوارات
      dialogTheme: DialogTheme(
        backgroundColor: AppConstants.secondaryColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        ),
        titleTextStyle: const TextStyle(
          color: AppConstants.textColor,
          fontSize: AppConstants.fontSizeTitle,
          fontWeight: FontWeight.w600,
          fontFamily: 'Cairo',
        ),
        contentTextStyle: const TextStyle(
          color: AppConstants.textColor,
          fontSize: AppConstants.fontSizeMedium,
          fontFamily: 'Cairo',
        ),
      ),
      
      // شريط التمرير
      scrollbarTheme: ScrollbarThemeData(
        thumbColor: MaterialStateProperty.all(AppConstants.primaryColor),
        trackColor: MaterialStateProperty.all(AppConstants.borderColor),
        radius: const Radius.circular(4),
        thickness: MaterialStateProperty.all(6),
      ),
      
      // المؤشرات
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: AppConstants.primaryColor,
        linearTrackColor: AppConstants.borderColor,
        circularTrackColor: AppConstants.borderColor,
      ),
      
      // الأيقونات
      iconTheme: const IconThemeData(
        color: AppConstants.textColor,
        size: AppConstants.iconSize,
      ),
      
      // التبديل والمربعات
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return AppConstants.primaryColor;
          }
          return AppConstants.textMutedColor;
        }),
        trackColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return AppConstants.primaryColor.withOpacity(0.5);
          }
          return AppConstants.borderColor;
        }),
      ),
      
      checkboxTheme: CheckboxThemeData(
        fillColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return AppConstants.primaryColor;
          }
          return Colors.transparent;
        }),
        checkColor: MaterialStateProperty.all(AppConstants.textColor),
        side: const BorderSide(color: AppConstants.borderColor),
      ),
      
      // الشرائح
      sliderTheme: const SliderThemeData(
        activeTrackColor: AppConstants.primaryColor,
        inactiveTrackColor: AppConstants.borderColor,
        thumbColor: AppConstants.primaryColor,
        overlayColor: Color(0x29E50914),
        valueIndicatorColor: AppConstants.primaryColor,
        valueIndicatorTextStyle: TextStyle(
          color: AppConstants.textColor,
          fontFamily: 'Cairo',
        ),
      ),
      
      // الخط الافتراضي
      fontFamily: 'Cairo',
      
      // إعدادات إضافية
      visualDensity: VisualDensity.adaptivePlatformDensity,
      useMaterial3: true,
    );
  }

  // ==========================================
  // ☀️ السمة الفاتحة
  // ==========================================
  static ThemeData get lightTheme {
    return darkTheme.copyWith(
      scaffoldBackgroundColor: AppConstants.lightColor,
      canvasColor: Colors.white,
      cardColor: Colors.white,
      
      colorScheme: const ColorScheme.light(
        primary: AppConstants.primaryColor,
        secondary: AppConstants.primaryColor,
        surface: Colors.white,
        background: AppConstants.lightColor,
        error: AppConstants.dangerColor,
        onPrimary: AppConstants.textColor,
        onSecondary: AppConstants.textColor,
        onSurface: Colors.black87,
        onBackground: Colors.black87,
        onError: AppConstants.textColor,
        brightness: Brightness.light,
      ),
      
      appBarTheme: darkTheme.appBarTheme.copyWith(
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
        titleTextStyle: const TextStyle(
          color: Colors.black87,
          fontSize: AppConstants.fontSizeTitle,
          fontWeight: FontWeight.w600,
          fontFamily: 'Cairo',
        ),
        iconTheme: const IconThemeData(
          color: Colors.black87,
          size: AppConstants.iconSize,
        ),
      ),
      
      textTheme: _buildTextTheme(isLight: true),
      
      inputDecorationTheme: darkTheme.inputDecorationTheme.copyWith(
        fillColor: Colors.grey[50],
        labelStyle: TextStyle(
          color: Colors.grey[600],
          fontSize: AppConstants.fontSizeMedium,
          fontFamily: 'Cairo',
        ),
        hintStyle: TextStyle(
          color: Colors.grey[500],
          fontSize: AppConstants.fontSizeMedium,
          fontFamily: 'Cairo',
        ),
      ),
      
      cardTheme: darkTheme.cardTheme.copyWith(
        color: Colors.white,
      ),
      
      listTileTheme: const ListTileThemeData(
        textColor: Colors.black87,
        iconColor: Colors.grey,
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
      
      bottomNavigationBarTheme: darkTheme.bottomNavigationBarTheme.copyWith(
        backgroundColor: Colors.white,
        unselectedItemColor: Colors.grey,
      ),
      
      dialogTheme: darkTheme.dialogTheme.copyWith(
        backgroundColor: Colors.white,
        titleTextStyle: const TextStyle(
          color: Colors.black87,
          fontSize: AppConstants.fontSizeTitle,
          fontWeight: FontWeight.w600,
          fontFamily: 'Cairo',
        ),
        contentTextStyle: const TextStyle(
          color: Colors.black87,
          fontSize: AppConstants.fontSizeMedium,
          fontFamily: 'Cairo',
        ),
      ),
      
      iconTheme: const IconThemeData(
        color: Colors.black87,
        size: AppConstants.iconSize,
      ),
    );
  }

  // ==========================================
  // 📝 بناء سمة النصوص
  // ==========================================
  static TextTheme _buildTextTheme({bool isLight = false}) {
    final Color textColor = isLight ? Colors.black87 : AppConstants.textColor;
    final Color mutedColor = isLight ? Colors.grey[600]! : AppConstants.textMutedColor;
    
    return TextTheme(
      displayLarge: TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: textColor,
        fontFamily: 'Cairo',
      ),
      displayMedium: TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: textColor,
        fontFamily: 'Cairo',
      ),
      displaySmall: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: textColor,
        fontFamily: 'Cairo',
      ),
      headlineLarge: TextStyle(
        fontSize: AppConstants.fontSizeHeading,
        fontWeight: FontWeight.bold,
        color: textColor,
        fontFamily: 'Cairo',
      ),
      headlineMedium: TextStyle(
        fontSize: AppConstants.fontSizeTitle,
        fontWeight: FontWeight.w600,
        color: textColor,
        fontFamily: 'Cairo',
      ),
      headlineSmall: TextStyle(
        fontSize: AppConstants.fontSizeXLarge,
        fontWeight: FontWeight.w600,
        color: textColor,
        fontFamily: 'Cairo',
      ),
      titleLarge: TextStyle(
        fontSize: AppConstants.fontSizeLarge,
        fontWeight: FontWeight.w600,
        color: textColor,
        fontFamily: 'Cairo',
      ),
      titleMedium: TextStyle(
        fontSize: AppConstants.fontSizeMedium,
        fontWeight: FontWeight.w500,
        color: textColor,
        fontFamily: 'Cairo',
      ),
      titleSmall: TextStyle(
        fontSize: AppConstants.fontSizeSmall,
        fontWeight: FontWeight.w500,
        color: textColor,
        fontFamily: 'Cairo',
      ),
      bodyLarge: TextStyle(
        fontSize: AppConstants.fontSizeLarge,
        color: textColor,
        fontFamily: 'Cairo',
      ),
      bodyMedium: TextStyle(
        fontSize: AppConstants.fontSizeMedium,
        color: textColor,
        fontFamily: 'Cairo',
      ),
      bodySmall: TextStyle(
        fontSize: AppConstants.fontSizeSmall,
        color: mutedColor,
        fontFamily: 'Cairo',
      ),
      labelLarge: TextStyle(
        fontSize: AppConstants.fontSizeMedium,
        fontWeight: FontWeight.w500,
        color: textColor,
        fontFamily: 'Cairo',
      ),
      labelMedium: TextStyle(
        fontSize: AppConstants.fontSizeSmall,
        fontWeight: FontWeight.w500,
        color: textColor,
        fontFamily: 'Cairo',
      ),
      labelSmall: TextStyle(
        fontSize: 10,
        fontWeight: FontWeight.w500,
        color: mutedColor,
        fontFamily: 'Cairo',
      ),
    );
  }

  // ==========================================
  // 🎨 إنشاء MaterialColor
  // ==========================================
  static MaterialColor _createMaterialColor(Color color) {
    List strengths = <double>[.05];
    Map<int, Color> swatch = {};
    final int r = color.red, g = color.green, b = color.blue;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    
    for (var strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    
    return MaterialColor(color.value, swatch);
  }
}
