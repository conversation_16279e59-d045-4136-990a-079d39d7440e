<?php
/**
 * 🧪 اختبارات وحدة المستخدم - منصة البث العربية
 */

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use StreamingPlatform\Models\User;
use StreamingPlatform\Services\AuthService;
use StreamingPlatform\Services\ValidationService;

class UserTest extends TestCase
{
    private $user;
    private $authService;
    private $validationService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = new User();
        $this->authService = new AuthService();
        $this->validationService = new ValidationService();
    }

    protected function tearDown(): void
    {
        $this->user = null;
        $this->authService = null;
        $this->validationService = null;
        
        parent::tearDown();
    }

    /**
     * اختبار إنشاء مستخدم جديد
     */
    public function testCreateUser()
    {
        $userData = [
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'user'
        ];

        $user = $this->user->create($userData);

        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals($userData['name'], $user->getName());
        $this->assertEquals($userData['email'], $user->getEmail());
        $this->assertEquals($userData['role'], $user->getRole());
        $this->assertTrue(password_verify($userData['password'], $user->getPassword()));
    }

    /**
     * اختبار التحقق من صحة البريد الإلكتروني
     */
    public function testEmailValidation()
    {
        // بريد إلكتروني صحيح
        $this->assertTrue($this->validationService->validateEmail('<EMAIL>'));
        
        // بريد إلكتروني غير صحيح
        $this->assertFalse($this->validationService->validateEmail('invalid-email'));
        $this->assertFalse($this->validationService->validateEmail(''));
        $this->assertFalse($this->validationService->validateEmail('test@'));
    }

    /**
     * اختبار التحقق من قوة كلمة المرور
     */
    public function testPasswordStrength()
    {
        // كلمة مرور قوية
        $this->assertTrue($this->validationService->validatePassword('StrongPass123!'));
        
        // كلمة مرور ضعيفة
        $this->assertFalse($this->validationService->validatePassword('123'));
        $this->assertFalse($this->validationService->validatePassword('password'));
        $this->assertFalse($this->validationService->validatePassword(''));
    }

    /**
     * اختبار تسجيل الدخول
     */
    public function testUserLogin()
    {
        // إنشاء مستخدم للاختبار
        $userData = [
            'name' => 'مستخدم تجريبي',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'user'
        ];
        
        $user = $this->user->create($userData);
        
        // اختبار تسجيل دخول صحيح
        $loginResult = $this->authService->login('<EMAIL>', 'password123');
        $this->assertTrue($loginResult['success']);
        $this->assertArrayHasKey('token', $loginResult);
        
        // اختبار تسجيل دخول خاطئ
        $loginResult = $this->authService->login('<EMAIL>', 'wrongpassword');
        $this->assertFalse($loginResult['success']);
        $this->assertArrayHasKey('error', $loginResult);
    }

    /**
     * اختبار تسجيل الخروج
     */
    public function testUserLogout()
    {
        // تسجيل دخول أولاً
        $userData = [
            'name' => 'مستخدم تجريبي',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'user'
        ];
        
        $user = $this->user->create($userData);
        $loginResult = $this->authService->login('<EMAIL>', 'password123');
        
        $this->assertTrue($loginResult['success']);
        
        // اختبار تسجيل الخروج
        $logoutResult = $this->authService->logout($loginResult['token']);
        $this->assertTrue($logoutResult['success']);
    }

    /**
     * اختبار تحديث بيانات المستخدم
     */
    public function testUpdateUser()
    {
        $userData = [
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'user'
        ];

        $user = $this->user->create($userData);
        
        // تحديث الاسم
        $updateData = ['name' => 'أحمد علي'];
        $updatedUser = $this->user->update($user->getId(), $updateData);
        
        $this->assertEquals('أحمد علي', $updatedUser->getName());
        $this->assertEquals($userData['email'], $updatedUser->getEmail());
    }

    /**
     * اختبار حذف المستخدم
     */
    public function testDeleteUser()
    {
        $userData = [
            'name' => 'مستخدم للحذف',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'user'
        ];

        $user = $this->user->create($userData);
        $userId = $user->getId();
        
        // حذف المستخدم
        $deleteResult = $this->user->delete($userId);
        $this->assertTrue($deleteResult);
        
        // التحقق من عدم وجود المستخدم
        $deletedUser = $this->user->findById($userId);
        $this->assertNull($deletedUser);
    }

    /**
     * اختبار البحث عن المستخدمين
     */
    public function testSearchUsers()
    {
        // إنشاء عدة مستخدمين للاختبار
        $users = [
            ['name' => 'أحمد محمد', 'email' => '<EMAIL>'],
            ['name' => 'فاطمة علي', 'email' => '<EMAIL>'],
            ['name' => 'محمد أحمد', 'email' => '<EMAIL>']
        ];

        foreach ($users as $userData) {
            $userData['password'] = 'password123';
            $userData['role'] = 'user';
            $this->user->create($userData);
        }

        // البحث بالاسم
        $searchResults = $this->user->search('أحمد');
        $this->assertGreaterThanOrEqual(2, count($searchResults));

        // البحث بالبريد الإلكتروني
        $searchResults = $this->user->search('<EMAIL>');
        $this->assertEquals(1, count($searchResults));
        $this->assertEquals('فاطمة علي', $searchResults[0]->getName());
    }

    /**
     * اختبار صلاحيات المستخدم
     */
    public function testUserPermissions()
    {
        // مستخدم عادي
        $userData = [
            'name' => 'مستخدم عادي',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'user'
        ];
        $user = $this->user->create($userData);
        
        $this->assertFalse($user->hasPermission('admin_access'));
        $this->assertTrue($user->hasPermission('view_content'));
        
        // مدير
        $adminData = [
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'admin'
        ];
        $admin = $this->user->create($adminData);
        
        $this->assertTrue($admin->hasPermission('admin_access'));
        $this->assertTrue($admin->hasPermission('view_content'));
        $this->assertTrue($admin->hasPermission('manage_users'));
    }

    /**
     * اختبار تفعيل وإلغاء تفعيل المستخدم
     */
    public function testUserActivation()
    {
        $userData = [
            'name' => 'مستخدم للتفعيل',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'user',
            'is_active' => false
        ];

        $user = $this->user->create($userData);
        
        // التحقق من عدم التفعيل
        $this->assertFalse($user->isActive());
        
        // تفعيل المستخدم
        $user->activate();
        $this->assertTrue($user->isActive());
        
        // إلغاء التفعيل
        $user->deactivate();
        $this->assertFalse($user->isActive());
    }

    /**
     * اختبار إعادة تعيين كلمة المرور
     */
    public function testPasswordReset()
    {
        $userData = [
            'name' => 'مستخدم إعادة تعيين',
            'email' => '<EMAIL>',
            'password' => 'oldpassword123',
            'role' => 'user'
        ];

        $user = $this->user->create($userData);
        
        // إنشاء رمز إعادة التعيين
        $resetToken = $this->authService->generatePasswordResetToken($user->getEmail());
        $this->assertNotEmpty($resetToken);
        
        // إعادة تعيين كلمة المرور
        $newPassword = 'newpassword123';
        $resetResult = $this->authService->resetPassword($resetToken, $newPassword);
        $this->assertTrue($resetResult['success']);
        
        // اختبار تسجيل الدخول بكلمة المرور الجديدة
        $loginResult = $this->authService->login('<EMAIL>', $newPassword);
        $this->assertTrue($loginResult['success']);
    }
}
