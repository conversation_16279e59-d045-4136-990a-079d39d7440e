/* 🎬 مشغل الفيديو المتقدم - CSS */

.streaming-player-wrapper {
    position: relative;
    width: 100%;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.streaming-player-wrapper .video-js {
    width: 100%;
    height: 100%;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* عناصر التحكم المخصصة */
.custom-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 20px 15px 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 10;
}

.streaming-player-wrapper:hover .custom-controls {
    opacity: 1;
}

.control-bar {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* شريط التقدم */
.progress-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    cursor: pointer;
    position: relative;
}

.progress-filled {
    height: 100%;
    background: #ff6b6b;
    border-radius: 3px;
    transition: width 0.1s ease;
}

.progress-handle {
    position: absolute;
    top: 50%;
    right: 0;
    width: 12px;
    height: 12px;
    background: #ff6b6b;
    border-radius: 50%;
    transform: translate(50%, -50%);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.progress-bar:hover .progress-handle {
    opacity: 1;
}

.time-display {
    display: flex;
    align-items: center;
    gap: 5px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    min-width: 100px;
}

.time-display::before {
    content: '/';
    opacity: 0.7;
}

/* أزرار التحكم */
.control-buttons {
    display: flex;
    align-items: center;
    gap: 15px;
}

.control-buttons button {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.control-buttons button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.btn-play-pause {
    font-size: 24px !important;
}

/* التحكم في الصوت */
.volume-slider {
    display: flex;
    align-items: center;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.btn-volume:hover + .volume-slider,
.volume-slider:hover {
    opacity: 1;
    transform: translateX(0);
    pointer-events: auto;
}

.volume-slider input {
    width: 80px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    outline: none;
    cursor: pointer;
}

.volume-slider input::-webkit-slider-thumb {
    appearance: none;
    width: 12px;
    height: 12px;
    background: #ff6b6b;
    border-radius: 50%;
    cursor: pointer;
}

/* قائمة الإعدادات */
.settings-menu {
    position: relative;
}

.settings-dropdown {
    position: absolute;
    bottom: 100%;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 8px;
    padding: 15px;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.settings-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.setting-item {
    margin-bottom: 15px;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    display: block;
    color: white;
    font-size: 14px;
    margin-bottom: 5px;
    font-weight: 500;
}

.setting-item select {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    color: white;
    padding: 8px;
    font-size: 14px;
}

.setting-item select option {
    background: #333;
    color: white;
}

/* شاشات التحميل والخطأ */
.loading-overlay,
.error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner i {
    font-size: 48px;
    margin-bottom: 15px;
    color: #ff6b6b;
}

.loading-spinner span {
    display: block;
    font-size: 16px;
    font-weight: 500;
}

.error-message {
    text-align: center;
    color: white;
    max-width: 400px;
    padding: 30px;
}

.error-message i {
    font-size: 48px;
    color: #ff6b6b;
    margin-bottom: 20px;
}

.error-message h3 {
    font-size: 24px;
    margin-bottom: 15px;
}

.error-message p {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 25px;
    opacity: 0.9;
}

.btn-retry {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
}

.btn-retry:hover {
    background: #ff5252;
}

/* الحلقة التالية */
.next-episode-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 30;
}

.next-episode-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    color: white;
    max-width: 400px;
}

.next-episode-content h3 {
    font-size: 24px;
    margin-bottom: 10px;
}

.next-episode-content p {
    font-size: 16px;
    margin-bottom: 25px;
    opacity: 0.9;
}

.btn-next-episode,
.btn-cancel {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    margin: 0 5px;
    transition: all 0.2s ease;
}

.btn-cancel {
    background: rgba(255, 255, 255, 0.2);
}

.btn-next-episode:hover {
    background: #ff5252;
    transform: translateY(-2px);
}

.btn-cancel:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* تأثيرات الاستجابة */
@media (max-width: 768px) {
    .custom-controls {
        padding: 15px 10px 10px;
    }
    
    .control-buttons {
        gap: 10px;
    }
    
    .control-buttons button {
        font-size: 16px;
        padding: 6px;
    }
    
    .btn-play-pause {
        font-size: 20px !important;
    }
    
    .time-display {
        font-size: 12px;
        min-width: 80px;
    }
    
    .volume-slider {
        display: none;
    }
    
    .settings-dropdown {
        right: -50px;
        min-width: 180px;
    }
    
    .next-episode-content {
        margin: 20px;
        padding: 20px;
    }
    
    .next-episode-content h3 {
        font-size: 20px;
    }
    
    .next-episode-content p {
        font-size: 14px;
    }
}

/* تأثيرات إضافية */
.streaming-player-wrapper {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* حالات خاصة */
.streaming-player-wrapper.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    border-radius: 0;
}

.streaming-player-wrapper.loading .custom-controls {
    opacity: 0;
    pointer-events: none;
}

.streaming-player-wrapper.error .custom-controls {
    opacity: 0;
    pointer-events: none;
}
