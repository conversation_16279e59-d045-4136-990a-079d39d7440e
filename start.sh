#!/bin/bash

# 🚀 سكريبت بدء تشغيل منصة البث
# للأنظمة المبنية على Unix (Linux/macOS)

echo "========================================"
echo "    🚀 بدء تشغيل منصة البث"
echo "========================================"
echo

# ألوان للنصوص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة لطباعة رسائل ملونة
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# التحقق من وجود PHP
if ! command -v php &> /dev/null; then
    print_error "PHP غير مثبت أو غير موجود في PATH"
    echo "يرجى تثبيت PHP أولاً:"
    echo "Ubuntu/Debian: sudo apt install php php-mysql php-mbstring php-xml"
    echo "CentOS/RHEL: sudo yum install php php-mysql php-mbstring php-xml"
    echo "macOS: brew install php"
    exit 1
fi

# التحقق من وجود Composer
if ! command -v composer &> /dev/null; then
    print_error "Composer غير مثبت أو غير موجود في PATH"
    echo "يرجى تثبيت Composer أولاً من: https://getcomposer.org/download"
    exit 1
fi

# التحقق من وجود MySQL
if ! command -v mysql &> /dev/null; then
    print_error "MySQL غير مثبت أو غير موجود في PATH"
    echo "يرجى تثبيت MySQL أولاً:"
    echo "Ubuntu/Debian: sudo apt install mysql-server"
    echo "CentOS/RHEL: sudo yum install mysql-server"
    echo "macOS: brew install mysql"
    exit 1
fi

print_success "جميع المتطلبات متوفرة"
echo

# طلب بيانات قاعدة البيانات
echo "📊 إعداد قاعدة البيانات..."
read -p "اسم المستخدم لـ MySQL (افتراضي: root): " db_user
db_user=${db_user:-root}

read -s -p "كلمة مرور MySQL: " db_password
echo

# إنشاء قاعدة البيانات
print_info "إنشاء قاعدة البيانات..."
mysql -u "$db_user" -p"$db_password" -e "CREATE DATABASE IF NOT EXISTS streaming_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null

if [ $? -ne 0 ]; then
    print_error "فشل في إنشاء قاعدة البيانات"
    echo "يرجى التحقق من بيانات الاتصال"
    exit 1
fi

# تشغيل ملف SQL
print_info "تشغيل ملف إعداد قاعدة البيانات..."
mysql -u "$db_user" -p"$db_password" streaming_platform < setup.sql 2>/dev/null

if [ $? -ne 0 ]; then
    print_error "فشل في تشغيل ملف SQL"
    exit 1
fi

print_success "تم إعداد قاعدة البيانات بنجاح"

# تحديث ملف الإعدادات
print_info "تحديث ملف الإعدادات..."
if [ -f "website/includes/config.php" ]; then
    # تحديث بيانات قاعدة البيانات في ملف الإعدادات
    sed -i.bak "s/define('DB_USER', 'root');/define('DB_USER', '$db_user');/" website/includes/config.php
    sed -i.bak "s/define('DB_PASS', '');/define('DB_PASS', '$db_password');/" website/includes/config.php
    print_success "تم تحديث ملف الإعدادات"
fi

# تثبيت تبعيات PHP
print_info "تثبيت تبعيات PHP..."
cd website
composer install --no-dev --optimize-autoloader --quiet

if [ $? -ne 0 ]; then
    print_error "فشل في تثبيت تبعيات PHP"
    exit 1
fi
cd ..

print_success "تم تثبيت تبعيات PHP"

# إنشاء المجلدات المطلوبة
print_info "إنشاء المجلدات المطلوبة..."
mkdir -p website/public/uploads/{posters,banners,videos,avatars,subtitles}
mkdir -p website/{logs,cache}
mkdir -p website/public/assets/images

# تعيين الصلاحيات
print_info "تعيين الصلاحيات..."
chmod -R 755 website/public/uploads
chmod -R 755 website/logs
chmod -R 755 website/cache

# إنشاء ملفات .htaccess للحماية
cat > website/public/uploads/.htaccess << 'EOF'
Options -Indexes
deny from all
<Files ~ "\.(jpg|jpeg|png|gif|webp|mp4|webm|srt|vtt)$">
    allow from all
</Files>
EOF

cat > website/logs/.htaccess << 'EOF'
deny from all
EOF

cat > website/cache/.htaccess << 'EOF'
deny from all
EOF

# إنشاء صورة افتراضية بسيطة (placeholder)
if [ ! -f "website/public/assets/images/default-poster.jpg" ]; then
    print_info "إنشاء الصور الافتراضية..."
    # إنشاء ملف فارغ كـ placeholder
    touch website/public/assets/images/default-poster.jpg
    touch website/public/assets/images/default-banner.jpg
    touch website/public/assets/images/default-avatar.png
fi

print_success "تم إعداد الملفات والمجلدات"

# التحقق من Flutter للتطبيق الجوال
if command -v flutter &> /dev/null; then
    print_info "تثبيت تبعيات Flutter..."
    cd mobile_app
    flutter pub get --quiet
    if [ $? -eq 0 ]; then
        print_success "تم تثبيت تبعيات Flutter"
    else
        print_warning "فشل في تثبيت تبعيات Flutter"
    fi
    cd ..
else
    print_warning "Flutter غير مثبت - لن يتم إعداد التطبيق الجوال"
    echo "لتثبيت Flutter: https://flutter.dev/docs/get-started/install"
fi

echo
echo "========================================"
print_success "تم إعداد المنصة بنجاح!"
echo "========================================"
echo
echo "🌐 بيانات تسجيل الدخول الافتراضية:"
echo "   البريد الإلكتروني: <EMAIL>"
echo "   كلمة المرور: password"
echo
echo "🚀 لبدء تشغيل الخادم:"
echo "   cd website"
echo "   php -S localhost:8000 -t public"
echo
if command -v flutter &> /dev/null; then
    echo "📱 لتشغيل التطبيق الجوال:"
    echo "   cd mobile_app"
    echo "   flutter run"
    echo
fi

# سؤال المستخدم إذا كان يريد بدء الخادم الآن
read -p "هل تريد بدء تشغيل خادم الويب الآن؟ (y/n): " start_server

if [[ $start_server =~ ^[Yy]$ ]]; then
    echo
    print_info "بدء تشغيل خادم الويب على http://localhost:8000"
    echo "اضغط Ctrl+C لإيقاف الخادم"
    echo
    cd website
    php -S localhost:8000 -t public
else
    echo
    print_info "يمكنك بدء تشغيل الخادم لاحقاً باستخدام:"
    echo "   cd website"
    echo "   php -S localhost:8000 -t public"
fi
