<?php
/**
 * 🔧 ملف الإعدادات الأساسية
 * انسخ هذا الملف إلى config.php وعدل الإعدادات
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}

// ===== إعدادات قاعدة البيانات =====
define('DB_HOST', 'localhost');
define('DB_NAME', 'streaming_platform');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// ===== إعدادات الموقع =====
define('SITE_NAME', 'منصة البث');
define('SITE_URL', 'http://localhost');
define('SITE_DESCRIPTION', 'منصة بث شاملة للأفلام والمسلسلات');
define('SITE_KEYWORDS', 'أفلام, مسلسلات, بث, مشاهدة');
define('SITE_AUTHOR', 'فريق منصة البث');
define('SITE_EMAIL', '<EMAIL>');

// ===== إعدادات الأمان =====
define('SECRET_KEY', 'your-secret-key-here-change-this');
define('CSRF_TOKEN_NAME', '_token');
define('SESSION_LIFETIME', 86400); // 24 ساعة
define('PASSWORD_MIN_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// ===== إعدادات الملفات =====
define('UPLOAD_PATH', __DIR__ . '/../public/uploads/');
define('UPLOAD_URL', '/uploads/');
define('MAX_FILE_SIZE', 100 * 1024 * 1024); // 100 MB
define('ALLOWED_VIDEO_TYPES', ['mp4', 'avi', 'mkv', 'mov', 'wmv']);
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// ===== إعدادات البريد الإلكتروني =====
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '<EMAIL>');
define('MAIL_PASSWORD', 'your-app-password');
define('MAIL_FROM_EMAIL', '<EMAIL>');
define('MAIL_FROM_NAME', 'منصة البث');

// ===== إعدادات API =====
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 100); // طلب في الدقيقة
define('API_KEY_LENGTH', 32);
define('JWT_SECRET', 'your-jwt-secret-key');
define('JWT_EXPIRY', 3600); // ساعة واحدة

// ===== إعدادات التخزين المؤقت =====
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // ساعة واحدة
define('CACHE_PATH', __DIR__ . '/../cache/');

// ===== إعدادات السجلات =====
define('LOG_ENABLED', true);
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_PATH', __DIR__ . '/../logs/');
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10 MB

// ===== إعدادات المحتوى =====
define('CONTENT_PER_PAGE', 12);
define('FEATURED_CONTENT_LIMIT', 6);
define('TRENDING_CONTENT_LIMIT', 10);
define('RELATED_CONTENT_LIMIT', 6);

// ===== إعدادات الاشتراكات =====
define('FREE_CONTENT_LIMIT', 5); // عدد المحتوى المجاني يومياً
define('SUBSCRIPTION_PLANS', [
    'free' => [
        'name' => 'مجاني',
        'price' => 0,
        'features' => ['محتوى محدود', 'جودة عادية', 'إعلانات']
    ],
    'basic' => [
        'name' => 'أساسي',
        'price' => 9.99,
        'features' => ['محتوى كامل', 'جودة HD', 'بدون إعلانات']
    ],
    'premium' => [
        'name' => 'مميز',
        'price' => 19.99,
        'features' => ['محتوى حصري', 'جودة 4K', 'تحميل للمشاهدة بدون إنترنت']
    ],
    'vip' => [
        'name' => 'VIP',
        'price' => 29.99,
        'features' => ['جميع الميزات', 'وصول مبكر', 'دعم أولوية']
    ]
]);

// ===== إعدادات التطبيق الجوال =====
define('MOBILE_API_URL', SITE_URL . '/api/');
define('PUSH_NOTIFICATION_KEY', 'your-firebase-key');
define('APP_VERSION', '1.0.0');
define('MIN_APP_VERSION', '1.0.0');

// ===== إعدادات التحليلات =====
define('ANALYTICS_ENABLED', true);
define('GOOGLE_ANALYTICS_ID', 'GA_MEASUREMENT_ID');
define('FACEBOOK_PIXEL_ID', 'FB_PIXEL_ID');

// ===== إعدادات وسائل التواصل =====
define('SOCIAL_LINKS', [
    'facebook' => 'https://facebook.com/streaming-platform',
    'twitter' => 'https://twitter.com/streaming_platform',
    'instagram' => 'https://instagram.com/streaming_platform',
    'youtube' => 'https://youtube.com/streaming-platform',
    'telegram' => 'https://t.me/streaming_platform'
]);

// ===== إعدادات التطوير =====
define('DEBUG_MODE', true);
define('SHOW_ERRORS', true);
define('LOG_QUERIES', false);
define('MAINTENANCE_MODE', false);

// ===== إعدادات اللغة =====
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en', 'fr', 'tr']);
define('RTL_LANGUAGES', ['ar']);

// ===== إعدادات الأداء =====
define('ENABLE_GZIP', true);
define('ENABLE_MINIFICATION', false);
define('CDN_ENABLED', false);
define('CDN_URL', 'https://cdn.streaming-platform.com');

// ===== إعدادات الأمان المتقدمة =====
define('ENABLE_2FA', false);
define('REQUIRE_EMAIL_VERIFICATION', true);
define('ENABLE_CAPTCHA', false);
define('CAPTCHA_SITE_KEY', 'your-recaptcha-site-key');
define('CAPTCHA_SECRET_KEY', 'your-recaptcha-secret-key');

// ===== إعدادات النسخ الاحتياطي =====
define('BACKUP_ENABLED', true);
define('BACKUP_PATH', __DIR__ . '/../backups/');
define('BACKUP_RETENTION_DAYS', 30);
define('AUTO_BACKUP_INTERVAL', 'daily'); // daily, weekly, monthly

// ===== إعدادات الإشعارات =====
define('NOTIFICATIONS_ENABLED', true);
define('EMAIL_NOTIFICATIONS', true);
define('PUSH_NOTIFICATIONS', true);
define('SMS_NOTIFICATIONS', false);

// ===== إعدادات الدفع =====
define('PAYMENT_ENABLED', false);
define('STRIPE_PUBLIC_KEY', 'pk_test_...');
define('STRIPE_SECRET_KEY', 'sk_test_...');
define('PAYPAL_CLIENT_ID', 'your-paypal-client-id');
define('PAYPAL_CLIENT_SECRET', 'your-paypal-client-secret');

// ===== إعدادات المنطقة الزمنية =====
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('DATE_FORMAT', 'Y-m-d');
define('TIME_FORMAT', 'H:i:s');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');

// تطبيق المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

// ===== إعدادات الجلسة =====
ini_set('session.cookie_lifetime', SESSION_LIFETIME);
ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));

// ===== إعدادات PHP =====
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// ===== إعدادات مخصصة =====
// يمكنك إضافة إعداداتك المخصصة هنا

// ===== التحقق من الإعدادات =====
function validateConfig() {
    $errors = [];
    
    // التحقق من إعدادات قاعدة البيانات
    if (empty(DB_HOST) || empty(DB_NAME) || empty(DB_USER)) {
        $errors[] = 'إعدادات قاعدة البيانات غير مكتملة';
    }
    
    // التحقق من المجلدات المطلوبة
    $requiredDirs = [UPLOAD_PATH, CACHE_PATH, LOG_PATH];
    foreach ($requiredDirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        if (!is_writable($dir)) {
            $errors[] = "المجلد غير قابل للكتابة: $dir";
        }
    }
    
    // التحقق من المفتاح السري
    if (SECRET_KEY === 'your-secret-key-here-change-this') {
        $errors[] = 'يجب تغيير المفتاح السري (SECRET_KEY)';
    }
    
    return $errors;
}

// تشغيل التحقق في وضع التطوير
if (DEBUG_MODE) {
    $configErrors = validateConfig();
    if (!empty($configErrors)) {
        foreach ($configErrors as $error) {
            error_log("تحذير الإعدادات: $error");
        }
    }
}
