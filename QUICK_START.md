# 🚀 دليل البدء السريع - Quick Start Guide

## 🎯 البدء الفوري في 3 خطوات

### الخطوة 1: اختبار الحالة 🔍
افتح في المتصفح:
```
file:///d:/qqq/htdocs/flutter_application_2/website/public/test.php
```

### الخطوة 2: العرض التوضيحي 🎬
افتح في المتصفح:
```
file:///d:/qqq/htdocs/flutter_application_2/quick-start.html
```

### الخطوة 3: إعداد قاعدة البيانات 🗄️
```sql
-- افتح MySQL Command Line أو phpMyAdmin
CREATE DATABASE streaming_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE streaming_platform;
SOURCE setup.sql;
```

## 🔧 الحلول السريعة

### إذا لم يعمل PHP
1. **ثبت XAMPP:** https://www.apachefriends.org/download.html
2. **شغل Apache + MySQL**
3. **انسخ المشروع إلى:** `C:\xampp\htdocs\`
4. **افتح:** http://localhost/flutter_application_2/website/public/test.php

### إذا لم تعمل قاعدة البيانات
```bash
# افتح XAMPP Control Panel
# اضغط Admin بجانب MySQL
# أو افتح: http://localhost/phpmyadmin
# أنشئ قاعدة بيانات جديدة: streaming_platform
# استورد ملف: setup.sql
```

## 📋 الملفات المهمة

| الملف | الوصف | الرابط |
|-------|--------|---------|
| `test.php` | صفحة اختبار المنصة | `website/public/test.php` |
| `quick-start.html` | العرض التوضيحي | `quick-start.html` |
| `setup.sql` | إعداد قاعدة البيانات | `setup.sql` |
| `index.php` | الصفحة الرئيسية | `website/public/index.php` |

## 🔐 بيانات الدخول

- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** password

## 🎬 ما تحصل عليه

✅ **موقع ويب كامل** مع مشغل فيديو احترافي  
✅ **تطبيق جوال** مع Flutter  
✅ **نظام إدارة** شامل للمحتوى  
✅ **API متكامل** لجميع العمليات  
✅ **نظام أمان** متقدم  
✅ **بحث ذكي** مع فلاتر  
✅ **تقييمات ومراجعات**  
✅ **إدارة اشتراكات**  

## 🆘 المساعدة السريعة

### مشكلة في PHP
```bash
# تحقق من إصدار PHP
php --version

# إذا لم يعمل، ثبت XAMPP
```

### مشكلة في MySQL
```bash
# تحقق من تشغيل MySQL
# في XAMPP: شغل MySQL من Control Panel
# في Command Line: net start mysql
```

### مشكلة في الملفات
```bash
# تأكد من وجود الملفات في المكان الصحيح
# المسار الصحيح: d:\qqq\htdocs\flutter_application_2\
```

## 🌟 الخطوات التالية

1. **اختبر المنصة** باستخدام `test.php`
2. **استكشف العرض التوضيحي** في `quick-start.html`
3. **أعد قاعدة البيانات** باستخدام `setup.sql`
4. **ابدأ الاستخدام** مع بيانات الدخول الافتراضية
5. **أضف محتوى جديد** واختبر جميع الميزات

## 📞 تحتاج مساعدة؟

1. **افتح `test.php`** لفحص حالة النظام
2. **راجع `quick-start.html`** للعرض التوضيحي
3. **تأكد من تشغيل XAMPP** إذا كنت تستخدمه
4. **تحقق من إعدادات قاعدة البيانات** في `config.php`

---

🎉 **المنصة جاهزة للاستخدام!** ابدأ الآن واستمتع بجميع الميزات المتقدمة.
