<?php
/**
 * 🎬 ملف الإعدادات الرئيسي لمنصة البث الشاملة
 * يحتوي على جميع الإعدادات الأساسية والاتصال بقاعدة البيانات
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}

// ==========================================
// 🔧 إعدادات قاعدة البيانات
// ==========================================
define('DB_HOST', 'localhost');
define('DB_NAME', 'streaming_platform');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// ==========================================
// 🌐 إعدادات الموقع الأساسية
// ==========================================
define('SITE_URL', 'http://localhost/streaming_platform');
define('SITE_PATH', dirname(dirname(__FILE__)));
define('ASSETS_URL', SITE_URL . '/website/assets');
define('MEDIA_URL', SITE_URL . '/media');
define('API_URL', SITE_URL . '/website/api');

// ==========================================
// 🔐 إعدادات الأمان
// ==========================================
define('JWT_SECRET_KEY', 'your-super-secret-jwt-key-change-this-in-production');
define('ENCRYPTION_KEY', 'your-encryption-key-32-characters-long');
define('SESSION_LIFETIME', 24 * 60 * 60); // 24 ساعة
define('CSRF_TOKEN_LIFETIME', 3600); // ساعة واحدة

// ==========================================
// 📧 إعدادات البريد الإلكتروني
// ==========================================
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'منصة البث الشاملة');

// ==========================================
// 💳 إعدادات الدفع
// ==========================================
// Stripe
define('STRIPE_PUBLISHABLE_KEY', 'pk_test_...');
define('STRIPE_SECRET_KEY', 'sk_test_...');
define('STRIPE_WEBHOOK_SECRET', 'whsec_...');

// PayPal
define('PAYPAL_CLIENT_ID', 'your-paypal-client-id');
define('PAYPAL_CLIENT_SECRET', 'your-paypal-client-secret');
define('PAYPAL_MODE', 'sandbox'); // sandbox أو live

// ==========================================
// 🔔 إعدادات Firebase (للإشعارات)
// ==========================================
define('FIREBASE_SERVER_KEY', 'your-firebase-server-key');
define('FIREBASE_SENDER_ID', 'your-firebase-sender-id');

// ==========================================
// 📱 إعدادات OAuth (تسجيل الدخول الاجتماعي)
// ==========================================
// Google OAuth
define('GOOGLE_CLIENT_ID', 'your-google-client-id');
define('GOOGLE_CLIENT_SECRET', 'your-google-client-secret');

// Facebook OAuth
define('FACEBOOK_APP_ID', 'your-facebook-app-id');
define('FACEBOOK_APP_SECRET', 'your-facebook-app-secret');

// ==========================================
// 🎥 إعدادات الفيديو والوسائط
// ==========================================
define('MAX_UPLOAD_SIZE', 2 * 1024 * 1024 * 1024); // 2GB
define('ALLOWED_VIDEO_FORMATS', ['mp4', 'webm', 'ogg', 'mov', 'avi']);
define('ALLOWED_IMAGE_FORMATS', ['jpg', 'jpeg', 'png', 'webp', 'gif']);
define('ALLOWED_SUBTITLE_FORMATS', ['vtt', 'srt', 'ass']);

// مجلدات الوسائط
define('VIDEOS_PATH', SITE_PATH . '/media/videos/');
define('IMAGES_PATH', SITE_PATH . '/media/images/');
define('SUBTITLES_PATH', SITE_PATH . '/media/subtitles/');
define('AUDIO_PATH', SITE_PATH . '/media/audio/');

// ==========================================
// 🚀 إعدادات الأداء والتخزين المؤقت
// ==========================================
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // ساعة واحدة
define('CACHE_PATH', SITE_PATH . '/cache/');

// ==========================================
// 🛡️ إعدادات الأمان المتقدمة
// ==========================================
define('RATE_LIMIT_ENABLED', true);
define('RATE_LIMIT_REQUESTS', 100); // طلب في الدقيقة
define('RATE_LIMIT_WINDOW', 60); // ثانية

define('BRUTE_FORCE_PROTECTION', true);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_DURATION', 30 * 60); // 30 دقيقة

// ==========================================
// 🌍 إعدادات التدويل
// ==========================================
define('DEFAULT_LANGUAGE', 'ar');
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('SUPPORTED_LANGUAGES', ['ar', 'en', 'fr', 'tr']);

// ==========================================
// 📊 إعدادات التحليلات
// ==========================================
define('ANALYTICS_ENABLED', true);
define('GOOGLE_ANALYTICS_ID', 'GA_MEASUREMENT_ID');

// ==========================================
// 🔧 إعدادات التطوير والإنتاج
// ==========================================
define('ENVIRONMENT', 'development'); // development أو production
define('DEBUG_MODE', ENVIRONMENT === 'development');
define('ERROR_REPORTING', DEBUG_MODE ? E_ALL : 0);
define('DISPLAY_ERRORS', DEBUG_MODE ? 1 : 0);

// ==========================================
// 📝 إعدادات السجلات
// ==========================================
define('LOGGING_ENABLED', true);
define('LOG_PATH', SITE_PATH . '/logs/');
define('LOG_LEVEL', DEBUG_MODE ? 'DEBUG' : 'ERROR');

// ==========================================
// 🎮 إعدادات الميزات المتقدمة
// ==========================================
define('GROUP_WATCH_ENABLED', true);
define('OFFLINE_DOWNLOAD_ENABLED', true);
define('AI_RECOMMENDATIONS_ENABLED', true);
define('PUSH_NOTIFICATIONS_ENABLED', true);

// ==========================================
// 🔄 إعدادات API
// ==========================================
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 1000); // طلب في الساعة
define('API_KEY_REQUIRED', false); // للمطورين الخارجيين

// ==========================================
// 🎯 إعدادات SEO
// ==========================================
define('SEO_ENABLED', true);
define('SITEMAP_ENABLED', true);
define('ROBOTS_TXT_ENABLED', true);

// ==========================================
// 🔧 إعدادات النظام
// ==========================================
// تعيين المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

// تعيين الترميز
mb_internal_encoding('UTF-8');

// تعيين إعدادات الأخطاء
error_reporting(ERROR_REPORTING);
ini_set('display_errors', DISPLAY_ERRORS);

// تعيين حد الذاكرة
ini_set('memory_limit', '512M');

// تعيين حد وقت التنفيذ
ini_set('max_execution_time', 300);

// ==========================================
// 🛠️ فئة الإعدادات الديناميكية
// ==========================================
class DynamicSettings {
    private static $settings = [];
    private static $loaded = false;
    
    /**
     * تحميل الإعدادات من قاعدة البيانات
     */
    public static function load() {
        if (self::$loaded) return;
        
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1");
            $stmt->execute();
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $value = $row['setting_value'];
                
                // تحويل القيمة حسب النوع
                switch ($row['setting_type']) {
                    case 'boolean':
                        $value = (bool) $value;
                        break;
                    case 'integer':
                        $value = (int) $value;
                        break;
                    case 'json':
                        $value = json_decode($value, true);
                        break;
                }
                
                self::$settings[$row['setting_key']] = $value;
            }
            
            self::$loaded = true;
        } catch (Exception $e) {
            error_log("Error loading dynamic settings: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على قيمة إعداد
     */
    public static function get($key, $default = null) {
        self::load();
        return isset(self::$settings[$key]) ? self::$settings[$key] : $default;
    }
    
    /**
     * تحديث قيمة إعداد
     */
    public static function set($key, $value) {
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("UPDATE system_settings SET setting_value = ? WHERE setting_key = ?");
            $stmt->execute([$value, $key]);
            
            self::$settings[$key] = $value;
            return true;
        } catch (Exception $e) {
            error_log("Error updating setting: " . $e->getMessage());
            return false;
        }
    }
}

// ==========================================
// 🎯 تحميل الملفات الأساسية
// ==========================================
// سيتم تحميل هذه الملفات لاحقاً
// require_once 'database.php';
// require_once 'functions.php';
// require_once 'security.php';

?>
