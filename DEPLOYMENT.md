# 🚀 دليل النشر - منصة البث العربية

هذا الدليل يوضح كيفية نشر منصة البث العربية على بيئات مختلفة من التطوير إلى الإنتاج.

## 📋 **متطلبات النظام**

### **الحد الأدنى للمتطلبات**
- **المعالج**: 2 CPU cores
- **الذاكرة**: 4GB RAM
- **التخزين**: 50GB SSD
- **الشبكة**: 100 Mbps
- **نظام التشغيل**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+

### **المتطلبات الموصى بها للإنتاج**
- **المعالج**: 8+ CPU cores
- **الذاكرة**: 16GB+ RAM
- **التخزين**: 500GB+ SSD
- **الشبكة**: 1 Gbps
- **نظام التشغيل**: Ubuntu 22.04 LTS

---

## 🐳 **النشر باستخدام Docker (الطريقة الموصى بها)**

### **1. التحضير**

```bash
# استنساخ المستودع
git clone https://github.com/streaming-platform/arabic-streaming-platform.git
cd arabic-streaming-platform

# نسخ ملف الإعدادات
cp .env.example .env

# تحرير الإعدادات
nano .env
```

### **2. النشر السريع**

```bash
# بناء وتشغيل جميع الخدمات
make docker-deploy

# أو باستخدام Docker Compose مباشرة
docker-compose -f docker-compose.prod.yml up -d
```

### **3. التحقق من الحالة**

```bash
# فحص حالة الحاويات
docker-compose ps

# عرض السجلات
docker-compose logs -f

# فحص صحة الخدمات
make health-check
```

---

## ☁️ **النشر السحابي**

### **🌊 Amazon Web Services (AWS)**

#### **إعداد EC2**

```bash
# إنشاء مثيل EC2
aws ec2 run-instances \
    --image-id ami-0c02fb55956c7d316 \
    --instance-type t3.large \
    --key-name your-key-pair \
    --security-group-ids sg-xxxxxxxxx \
    --subnet-id subnet-xxxxxxxxx

# الاتصال بالمثيل
ssh -i your-key.pem ubuntu@your-ec2-ip
```

#### **إعداد RDS**

```bash
# إنشاء قاعدة بيانات MySQL
aws rds create-db-instance \
    --db-instance-identifier streaming-platform-db \
    --db-instance-class db.t3.micro \
    --engine mysql \
    --master-username admin \
    --master-user-password your-secure-password \
    --allocated-storage 20
```

#### **إعداد S3**

```bash
# إنشاء bucket للملفات
aws s3 mb s3://streaming-platform-files

# إعداد سياسة الوصول
aws s3api put-bucket-policy \
    --bucket streaming-platform-files \
    --policy file://s3-policy.json
```

### **☁️ Google Cloud Platform (GCP)**

#### **إعداد Compute Engine**

```bash
# إنشاء مثيل VM
gcloud compute instances create streaming-platform \
    --zone=us-central1-a \
    --machine-type=e2-standard-4 \
    --image-family=ubuntu-2004-lts \
    --image-project=ubuntu-os-cloud

# الاتصال بالمثيل
gcloud compute ssh streaming-platform
```

#### **إعداد Cloud SQL**

```bash
# إنشاء مثيل قاعدة البيانات
gcloud sql instances create streaming-platform-db \
    --database-version=MYSQL_8_0 \
    --tier=db-n1-standard-2 \
    --region=us-central1
```

### **🔷 Microsoft Azure**

#### **إعداد Virtual Machine**

```bash
# إنشاء مجموعة موارد
az group create --name StreamingPlatformRG --location eastus

# إنشاء VM
az vm create \
    --resource-group StreamingPlatformRG \
    --name StreamingPlatformVM \
    --image UbuntuLTS \
    --size Standard_D2s_v3 \
    --admin-username azureuser \
    --generate-ssh-keys
```

---

## 🖥️ **النشر على خادم مخصص**

### **1. إعداد الخادم**

```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت المتطلبات الأساسية
sudo apt install -y curl wget git unzip

# تثبيت Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# تثبيت Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### **2. إعداد قاعدة البيانات**

```bash
# تثبيت MySQL
sudo apt install -y mysql-server

# تأمين MySQL
sudo mysql_secure_installation

# إنشاء قاعدة البيانات والمستخدم
sudo mysql -e "CREATE DATABASE streaming_platform;"
sudo mysql -e "CREATE USER 'streaming_user'@'localhost' IDENTIFIED BY 'secure_password';"
sudo mysql -e "GRANT ALL PRIVILEGES ON streaming_platform.* TO 'streaming_user'@'localhost';"
sudo mysql -e "FLUSH PRIVILEGES;"
```

### **3. إعداد الويب سيرفر**

#### **Nginx**

```bash
# تثبيت Nginx
sudo apt install -y nginx

# نسخ إعدادات Nginx
sudo cp nginx.conf /etc/nginx/sites-available/streaming-platform
sudo ln -s /etc/nginx/sites-available/streaming-platform /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

#### **Apache**

```bash
# تثبيت Apache
sudo apt install -y apache2

# تفعيل الوحدات المطلوبة
sudo a2enmod rewrite ssl headers

# نسخ إعدادات Apache
sudo cp apache.conf /etc/apache2/sites-available/streaming-platform.conf
sudo a2ensite streaming-platform.conf
sudo systemctl reload apache2
```

---

## 🔒 **إعداد SSL/TLS**

### **Let's Encrypt (مجاني)**

```bash
# تثبيت Certbot
sudo apt install -y certbot python3-certbot-nginx

# الحصول على شهادة SSL
sudo certbot --nginx -d streaming-platform.com -d www.streaming-platform.com

# تجديد تلقائي
sudo crontab -e
# إضافة السطر التالي:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### **شهادة مدفوعة**

```bash
# إنشاء مفتاح خاص
openssl genrsa -out streaming-platform.key 2048

# إنشاء طلب شهادة
openssl req -new -key streaming-platform.key -out streaming-platform.csr

# بعد الحصول على الشهادة من المزود
sudo cp streaming-platform.crt /etc/ssl/certs/
sudo cp streaming-platform.key /etc/ssl/private/
sudo chmod 600 /etc/ssl/private/streaming-platform.key
```

---

## 📊 **المراقبة والسجلات**

### **إعداد المراقبة**

```bash
# تثبيت Prometheus
docker run -d \
    --name prometheus \
    -p 9090:9090 \
    -v $(pwd)/prometheus.yml:/etc/prometheus/prometheus.yml \
    prom/prometheus

# تثبيت Grafana
docker run -d \
    --name grafana \
    -p 3000:3000 \
    grafana/grafana
```

### **إعداد السجلات**

```bash
# تثبيت ELK Stack
docker-compose -f elk-stack.yml up -d

# إعداد Logrotate
sudo nano /etc/logrotate.d/streaming-platform
```

---

## 🔄 **التحديث والصيانة**

### **تحديث التطبيق**

```bash
# سحب أحدث التغييرات
git pull origin main

# إعادة بناء الحاويات
docker-compose build --no-cache

# إعادة تشغيل الخدمات
docker-compose up -d

# تشغيل migrations
make migrate
```

### **النسخ الاحتياطي**

```bash
# نسخ احتياطي لقاعدة البيانات
make backup-db

# نسخ احتياطي للملفات
make backup-files

# نسخ احتياطي شامل
make backup-full
```

### **الاستعادة**

```bash
# استعادة قاعدة البيانات
make restore-db backup-file.sql

# استعادة الملفات
make restore-files backup-files.tar.gz

# استعادة شاملة
make restore-full backup-full.tar.gz
```

---

## 🚨 **استكشاف الأخطاء**

### **مشاكل شائعة وحلولها**

#### **خطأ في الاتصال بقاعدة البيانات**

```bash
# فحص حالة MySQL
sudo systemctl status mysql

# فحص السجلات
sudo tail -f /var/log/mysql/error.log

# إعادة تشغيل MySQL
sudo systemctl restart mysql
```

#### **مشاكل الأذونات**

```bash
# إصلاح أذونات الملفات
sudo chown -R www-data:www-data /var/www/streaming-platform
sudo chmod -R 755 /var/www/streaming-platform
sudo chmod -R 777 /var/www/streaming-platform/storage
```

#### **مشاكل الذاكرة**

```bash
# فحص استخدام الذاكرة
free -h
htop

# إضافة swap
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

---

## 📈 **تحسين الأداء**

### **تحسين قاعدة البيانات**

```sql
-- إضافة فهارس
CREATE INDEX idx_movies_genre ON movies(genre);
CREATE INDEX idx_users_email ON users(email);

-- تحسين إعدادات MySQL
SET innodb_buffer_pool_size = 1G;
SET query_cache_size = 256M;
```

### **تحسين الويب سيرفر**

```nginx
# إعدادات Nginx للأداء
worker_processes auto;
worker_connections 1024;

# تفعيل الضغط
gzip on;
gzip_vary on;
gzip_min_length 1024;

# تحسين التخزين المؤقت
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

---

## 🔐 **الأمان**

### **تأمين الخادم**

```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# إعداد جدار الحماية
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https

# تثبيت Fail2Ban
sudo apt install -y fail2ban
sudo systemctl enable fail2ban
```

### **تأمين التطبيق**

```bash
# تشفير ملف .env
gpg --symmetric --cipher-algo AES256 .env

# إعداد مفاتيح SSH
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# تقييد الوصول للملفات الحساسة
chmod 600 .env
chmod 600 config/*.php
```

---

## 📞 **الدعم والمساعدة**

### **قنوات الدعم**

- **الوثائق**: https://docs.streaming-platform.com
- **GitHub Issues**: https://github.com/streaming-platform/issues
- **Discord**: https://discord.gg/streaming-platform
- **البريد الإلكتروني**: <EMAIL>

### **خدمات النشر المدفوعة**

نقدم خدمات نشر مدفوعة تشمل:
- إعداد الخادم الكامل
- تكوين قاعدة البيانات
- إعداد SSL والأمان
- المراقبة والصيانة
- الدعم الفني 24/7

للاستفسار: <EMAIL>

---

**🚀 نشر ناجح لمنصة البث العربية!**

آخر تحديث: 15 يناير 2024
