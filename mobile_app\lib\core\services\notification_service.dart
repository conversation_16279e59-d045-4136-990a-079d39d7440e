import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import '../constants/app_constants.dart';

/// 🔔 خدمة الإشعارات
/// تدير جميع أنواع الإشعارات المحلية والبعيدة
class NotificationService {
  static NotificationService? _instance;
  static NotificationService get instance => _instance ??= NotificationService._();
  
  NotificationService._();

  late FlutterLocalNotificationsPlugin _localNotifications;
  late FirebaseMessaging _firebaseMessaging;
  
  bool _isInitialized = false;
  String? _fcmToken;

  /// تهيئة خدمة الإشعارات
  static Future<void> init() async {
    final service = NotificationService.instance;
    
    if (service._isInitialized) return;
    
    try {
      await service._initializeLocalNotifications();
      await service._initializeFirebaseMessaging();
      
      service._isInitialized = true;
      print('✅ تم تهيئة خدمة الإشعارات بنجاح');
    } catch (e) {
      print('❌ خطأ في تهيئة خدمة الإشعارات: $e');
    }
  }

  /// تهيئة الإشعارات المحلية
  Future<void> _initializeLocalNotifications() async {
    _localNotifications = FlutterLocalNotificationsPlugin();

    // إعدادات Android
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    
    // إعدادات iOS
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // إنشاء قناة الإشعارات لـ Android
    if (Platform.isAndroid) {
      await _createNotificationChannel();
    }
  }

  /// تهيئة Firebase Messaging
  Future<void> _initializeFirebaseMessaging() async {
    _firebaseMessaging = FirebaseMessaging.instance;

    // طلب الأذونات
    await _requestPermissions();

    // الحصول على FCM Token
    _fcmToken = await _firebaseMessaging.getToken();
    print('FCM Token: $_fcmToken');

    // الاستماع للرسائل في المقدمة
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // الاستماع للرسائل عند النقر
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

    // التحقق من الرسائل عند فتح التطبيق
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      _handleMessageOpenedApp(initialMessage);
    }

    // الاستماع لتحديث الرمز المميز
    _firebaseMessaging.onTokenRefresh.listen((token) {
      _fcmToken = token;
      print('FCM Token Updated: $token');
      // يمكن إرسال الرمز المحدث للخادم هنا
    });
  }

  /// إنشاء قناة الإشعارات لـ Android
  Future<void> _createNotificationChannel() async {
    const channel = AndroidNotificationChannel(
      AppConstants.notificationChannelId,
      AppConstants.notificationChannelName,
      description: AppConstants.notificationChannelDescription,
      importance: Importance.high,
      enableVibration: true,
      playSound: true,
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
  }

  /// طلب أذونات الإشعارات
  Future<void> _requestPermissions() async {
    if (Platform.isIOS) {
      await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        announcement: false,
      );
    }

    // طلب أذونات الإشعارات المحلية
    if (Platform.isAndroid) {
      await _localNotifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();
    }
  }

  /// معالجة الرسائل في المقدمة
  void _handleForegroundMessage(RemoteMessage message) {
    print('رسالة في المقدمة: ${message.messageId}');
    
    // عرض إشعار محلي
    showNotification(
      title: message.notification?.title ?? 'إشعار جديد',
      body: message.notification?.body ?? '',
      payload: message.data.toString(),
    );
  }

  /// معالجة النقر على الرسائل
  void _handleMessageOpenedApp(RemoteMessage message) {
    print('تم النقر على الرسالة: ${message.messageId}');
    
    // التنقل حسب نوع الإشعار
    final data = message.data;
    _handleNotificationNavigation(data);
  }

  /// معالجة النقر على الإشعارات المحلية
  void _onNotificationTapped(NotificationResponse response) {
    print('تم النقر على الإشعار المحلي: ${response.payload}');
    
    if (response.payload != null) {
      // معالجة البيانات والتنقل
      _handleNotificationNavigation({'payload': response.payload});
    }
  }

  /// معالجة التنقل حسب نوع الإشعار
  void _handleNotificationNavigation(Map<String, dynamic> data) {
    final type = data['type'] as String?;
    
    switch (type) {
      case AppConstants.notificationTypeNewContent:
        // التنقل لصفحة المحتوى الجديد
        break;
      case AppConstants.notificationTypeSubscription:
        // التنقل لصفحة الاشتراك
        break;
      case AppConstants.notificationTypeReminder:
        // التنقل لصفحة المحتوى المحدد
        break;
      case AppConstants.notificationTypePromotion:
        // التنقل لصفحة العروض
        break;
      default:
        // التنقل للصفحة الرئيسية
        break;
    }
  }

  // ==========================================
  // 📱 الإشعارات المحلية
  // ==========================================

  /// عرض إشعار محلي
  Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
    int id = 0,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      AppConstants.notificationChannelId,
      AppConstants.notificationChannelName,
      channelDescription: AppConstants.notificationChannelDescription,
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
      enableVibration: true,
      playSound: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      id,
      title,
      body,
      details,
      payload: payload,
    );
  }

  /// جدولة إشعار
  Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    int id = 0,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      AppConstants.notificationChannelId,
      AppConstants.notificationChannelName,
      channelDescription: AppConstants.notificationChannelDescription,
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails();

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.schedule(
      id,
      title,
      body,
      scheduledDate,
      details,
      payload: payload,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    );
  }

  /// إلغاء إشعار محدد
  Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
  }

  /// إلغاء جميع الإشعارات
  Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  /// الحصول على الإشعارات المجدولة
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _localNotifications.pendingNotificationRequests();
  }

  // ==========================================
  // 🔔 إشعارات خاصة بالتطبيق
  // ==========================================

  /// إشعار محتوى جديد
  Future<void> showNewContentNotification({
    required String contentTitle,
    required String contentType,
  }) async {
    await showNotification(
      title: 'محتوى جديد متاح!',
      body: 'تم إضافة $contentType جديد: $contentTitle',
      payload: 'new_content',
    );
  }

  /// إشعار تذكير المشاهدة
  Future<void> showWatchReminderNotification({
    required String contentTitle,
    required DateTime reminderTime,
  }) async {
    await scheduleNotification(
      title: 'تذكير المشاهدة',
      body: 'لا تنس مشاهدة: $contentTitle',
      scheduledDate: reminderTime,
      payload: 'watch_reminder',
    );
  }

  /// إشعار انتهاء الاشتراك
  Future<void> showSubscriptionExpiryNotification({
    required int daysLeft,
  }) async {
    await showNotification(
      title: 'تنبيه الاشتراك',
      body: 'سينتهي اشتراكك خلال $daysLeft أيام. جدد الآن!',
      payload: 'subscription_expiry',
    );
  }

  /// إشعار اكتمال التحميل
  Future<void> showDownloadCompleteNotification({
    required String contentTitle,
  }) async {
    await showNotification(
      title: 'اكتمل التحميل',
      body: 'تم تحميل $contentTitle بنجاح',
      payload: 'download_complete',
    );
  }

  /// إشعار عرض خاص
  Future<void> showPromotionNotification({
    required String title,
    required String description,
  }) async {
    await showNotification(
      title: title,
      body: description,
      payload: 'promotion',
    );
  }

  // ==========================================
  // 🔧 إدارة الإشعارات
  // ==========================================

  /// الحصول على FCM Token
  String? get fcmToken => _fcmToken;

  /// تحديث FCM Token على الخادم
  Future<void> updateTokenOnServer() async {
    if (_fcmToken != null) {
      // إرسال الرمز المميز للخادم
      print('تحديث FCM Token على الخادم: $_fcmToken');
      // يمكن إضافة استدعاء API هنا
    }
  }

  /// الاشتراك في موضوع
  Future<void> subscribeToTopic(String topic) async {
    await _firebaseMessaging.subscribeToTopic(topic);
    print('تم الاشتراك في الموضوع: $topic');
  }

  /// إلغاء الاشتراك من موضوع
  Future<void> unsubscribeFromTopic(String topic) async {
    await _firebaseMessaging.unsubscribeFromTopic(topic);
    print('تم إلغاء الاشتراك من الموضوع: $topic');
  }

  /// تفعيل/تعطيل الإشعارات
  Future<void> setNotificationsEnabled(bool enabled) async {
    if (enabled) {
      await _firebaseMessaging.requestPermission();
    } else {
      await _firebaseMessaging.deleteToken();
    }
  }

  /// التحقق من حالة الأذونات
  Future<bool> areNotificationsEnabled() async {
    final settings = await _firebaseMessaging.getNotificationSettings();
    return settings.authorizationStatus == AuthorizationStatus.authorized;
  }

  /// تنظيف الموارد
  void dispose() {
    // تنظيف الموارد إذا لزم الأمر
  }
}
