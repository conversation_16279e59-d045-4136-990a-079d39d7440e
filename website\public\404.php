<?php
/**
 * 🚫 صفحة 404 - الصفحة غير موجودة
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}

http_response_code(404);
?>

<!DOCTYPE html>
<html lang="<?php echo CURRENT_LANGUAGE; ?>" dir="<?php echo CURRENT_LANGUAGE === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة غير موجودة - <?php echo SITE_NAME; ?></title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .error-container {
            text-align: center;
            max-width: 600px;
            padding: 40px 20px;
        }

        .error-code {
            font-size: 8rem;
            font-weight: bold;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
        }

        .error-title {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .error-message {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .error-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            border-color: transparent;
        }

        .btn-primary:hover {
            background: white;
            color: #5a67d8;
        }

        .search-container {
            margin: 40px 0;
            position: relative;
        }

        .search-input {
            width: 100%;
            max-width: 400px;
            padding: 15px 50px 15px 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            backdrop-filter: blur(10px);
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-input:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.2);
        }

        .search-btn {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 10px;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .search-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .suggestions {
            margin-top: 30px;
        }

        .suggestions-title {
            font-size: 1.1rem;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .suggestions-list {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .suggestion-link {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .suggestion-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .error-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .error-code {
                font-size: 6rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-message {
                font-size: 1rem;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }

        /* تأثيرات الحركة */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .error-code {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .error-container > * {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        .error-container > *:nth-child(2) { animation-delay: 0.1s; }
        .error-container > *:nth-child(3) { animation-delay: 0.2s; }
        .error-container > *:nth-child(4) { animation-delay: 0.3s; }
        .error-container > *:nth-child(5) { animation-delay: 0.4s; }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">🎬</div>
        <div class="error-code">404</div>
        <h1 class="error-title">الصفحة غير موجودة</h1>
        <p class="error-message">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
            <br>
            يمكنك البحث عن المحتوى الذي تريده أو العودة إلى الصفحة الرئيسية.
        </p>

        <!-- البحث -->
        <div class="search-container">
            <form action="/search" method="GET" style="position: relative; display: inline-block;">
                <input type="text" 
                       name="q" 
                       class="search-input" 
                       placeholder="ابحث عن الأفلام والمسلسلات..."
                       autocomplete="off">
                <button type="submit" class="search-btn">
                    🔍
                </button>
            </form>
        </div>

        <!-- الإجراءات -->
        <div class="error-actions">
            <a href="/" class="btn btn-primary">
                🏠 الصفحة الرئيسية
            </a>
            <a href="/movies" class="btn">
                🎬 الأفلام
            </a>
            <a href="/series" class="btn">
                📺 المسلسلات
            </a>
            <a href="javascript:history.back()" class="btn">
                ↩️ العودة للخلف
            </a>
        </div>

        <!-- اقتراحات -->
        <div class="suggestions">
            <div class="suggestions-title">أو جرب هذه الصفحات:</div>
            <div class="suggestions-list">
                <a href="/trending" class="suggestion-link">الرائج الآن</a>
                <a href="/top-rated" class="suggestion-link">الأعلى تقييماً</a>
                <a href="/latest" class="suggestion-link">الأحدث</a>
                <a href="/genres" class="suggestion-link">التصنيفات</a>
                <?php if (IS_LOGGED_IN): ?>
                <a href="/my-list" class="suggestion-link">قائمتي</a>
                <a href="/favorites" class="suggestion-link">المفضلة</a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // تتبع الخطأ 404
        if (typeof trackEvent === 'function') {
            trackEvent('404_error', {
                page: window.location.pathname,
                referrer: document.referrer,
                user_agent: navigator.userAgent
            });
        }

        // تحسين تجربة البحث
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('.search-input');
            const searchForm = document.querySelector('form');

            if (searchInput) {
                searchInput.addEventListener('keyup', function(e) {
                    if (e.key === 'Enter') {
                        searchForm.submit();
                    }
                });

                // التركيز التلقائي على حقل البحث
                setTimeout(() => {
                    searchInput.focus();
                }, 1000);
            }
        });

        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('.btn, .suggestion-link').forEach(element => {
            element.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px) scale(1.02)';
            });

            element.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
