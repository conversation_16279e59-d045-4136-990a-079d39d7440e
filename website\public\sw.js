// 🔧 Service Worker لمنصة البث العربية
// يوفر إمكانيات التطبيق التقدمي (PWA) والعمل بدون إنترنت

const CACHE_NAME = 'streaming-platform-v1.0.0';
const OFFLINE_URL = '/offline.html';

// الملفات الأساسية للتخزين المؤقت
const CORE_CACHE_FILES = [
  '/',
  '/simple-index.php',
  '/offline.html',
  '/manifest.json',
  
  // CSS
  '/assets/css/style.css',
  '/assets/css/admin.css',
  '/assets/css/video-player.css',
  
  // JavaScript
  '/assets/js/main.js',
  '/assets/js/admin.js',
  '/assets/js/video-player.js',
  
  // الصور الأساسية
  '/assets/images/logo.png',
  '/assets/images/placeholder.jpg',
  '/assets/images/icons/icon-192x192.png',
  '/assets/images/icons/icon-512x512.png',
  
  // الخطوط
  '/assets/fonts/cairo-regular.woff2',
  '/assets/fonts/cairo-bold.woff2'
];

// الملفات الديناميكية للتخزين المؤقت
const DYNAMIC_CACHE_FILES = [
  '/search.php',
  '/category.php',
  '/player.php',
  '/api/'
];

// ===== تثبيت Service Worker =====
self.addEventListener('install', event => {
  console.log('🔧 تثبيت Service Worker...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('📦 تخزين الملفات الأساسية...');
        return cache.addAll(CORE_CACHE_FILES);
      })
      .then(() => {
        console.log('✅ تم تثبيت Service Worker بنجاح');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('❌ خطأ في تثبيت Service Worker:', error);
      })
  );
});

// ===== تفعيل Service Worker =====
self.addEventListener('activate', event => {
  console.log('🚀 تفعيل Service Worker...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME) {
              console.log('🗑️ حذف التخزين المؤقت القديم:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('✅ تم تفعيل Service Worker بنجاح');
        return self.clients.claim();
      })
      .catch(error => {
        console.error('❌ خطأ في تفعيل Service Worker:', error);
      })
  );
});

// ===== اعتراض الطلبات =====
self.addEventListener('fetch', event => {
  const request = event.request;
  const url = new URL(request.url);
  
  // تجاهل الطلبات غير HTTP/HTTPS
  if (!request.url.startsWith('http')) {
    return;
  }
  
  // تجاهل طلبات API الحساسة
  if (url.pathname.includes('/admin/') || 
      url.pathname.includes('/api/private/')) {
    return;
  }
  
  event.respondWith(
    handleRequest(request)
  );
});

// ===== معالجة الطلبات =====
async function handleRequest(request) {
  const url = new URL(request.url);
  
  try {
    // محاولة الحصول على الاستجابة من الشبكة أولاً
    const networkResponse = await fetch(request);
    
    // إذا كانت الاستجابة ناجحة، احفظها في التخزين المؤقت
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      
      // تخزين الصفحات والأصول فقط
      if (shouldCache(request)) {
        cache.put(request, networkResponse.clone());
      }
    }
    
    return networkResponse;
    
  } catch (error) {
    console.log('🌐 لا يوجد اتصال بالإنترنت، البحث في التخزين المؤقت...');
    
    // البحث في التخزين المؤقت
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      console.log('📦 تم العثور على الملف في التخزين المؤقت');
      return cachedResponse;
    }
    
    // إذا كان طلب تنقل، أرجع صفحة بدون إنترنت
    if (request.mode === 'navigate') {
      console.log('📄 عرض صفحة بدون إنترنت');
      return caches.match(OFFLINE_URL);
    }
    
    // للطلبات الأخرى، أرجع استجابة خطأ
    return new Response(
      JSON.stringify({
        error: 'لا يوجد اتصال بالإنترنت',
        message: 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى'
      }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      }
    );
  }
}

// ===== تحديد ما يجب تخزينه مؤقتاً =====
function shouldCache(request) {
  const url = new URL(request.url);
  
  // تخزين الصفحات الرئيسية
  if (request.mode === 'navigate') {
    return true;
  }
  
  // تخزين الأصول (CSS, JS, الصور)
  if (request.destination === 'style' ||
      request.destination === 'script' ||
      request.destination === 'image' ||
      request.destination === 'font') {
    return true;
  }
  
  // تخزين API العام
  if (url.pathname.startsWith('/api/') && 
      !url.pathname.includes('/private/')) {
    return true;
  }
  
  return false;
}

// ===== معالجة الرسائل =====
self.addEventListener('message', event => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
      
    case 'GET_VERSION':
      event.ports[0].postMessage({
        version: CACHE_NAME
      });
      break;
      
    case 'CLEAR_CACHE':
      clearCache().then(() => {
        event.ports[0].postMessage({
          success: true,
          message: 'تم مسح التخزين المؤقت'
        });
      });
      break;
      
    case 'CACHE_URLS':
      cacheUrls(data.urls).then(() => {
        event.ports[0].postMessage({
          success: true,
          message: 'تم تخزين الروابط'
        });
      });
      break;
  }
});

// ===== مسح التخزين المؤقت =====
async function clearCache() {
  const cacheNames = await caches.keys();
  await Promise.all(
    cacheNames.map(cacheName => caches.delete(cacheName))
  );
  console.log('🗑️ تم مسح جميع ملفات التخزين المؤقت');
}

// ===== تخزين روابط محددة =====
async function cacheUrls(urls) {
  const cache = await caches.open(CACHE_NAME);
  await cache.addAll(urls);
  console.log('📦 تم تخزين الروابط المحددة');
}

// ===== معالجة الإشعارات =====
self.addEventListener('push', event => {
  console.log('🔔 تم استلام إشعار push');
  
  const options = {
    body: 'محتوى جديد متاح للمشاهدة!',
    icon: '/assets/images/icons/icon-192x192.png',
    badge: '/assets/images/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'استكشاف المحتوى',
        icon: '/assets/images/icons/explore.png'
      },
      {
        action: 'close',
        title: 'إغلاق',
        icon: '/assets/images/icons/close.png'
      }
    ]
  };
  
  if (event.data) {
    const data = event.data.json();
    options.body = data.body || options.body;
    options.title = data.title || 'منصة البث';
  }
  
  event.waitUntil(
    self.registration.showNotification('منصة البث', options)
  );
});

// ===== معالجة النقر على الإشعارات =====
self.addEventListener('notificationclick', event => {
  console.log('👆 تم النقر على الإشعار');
  
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/')
    );
  } else if (event.action === 'close') {
    // لا تفعل شيئاً، فقط أغلق الإشعار
  } else {
    // النقر على الإشعار نفسه
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// ===== معالجة المزامنة في الخلفية =====
self.addEventListener('sync', event => {
  console.log('🔄 مزامنة في الخلفية:', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

// ===== تنفيذ المزامنة في الخلفية =====
async function doBackgroundSync() {
  try {
    // مزامنة البيانات المحفوظة محلياً
    console.log('🔄 تنفيذ المزامنة في الخلفية...');
    
    // هنا يمكن إضافة منطق المزامنة
    // مثل رفع البيانات المحفوظة محلياً
    
    console.log('✅ تمت المزامنة بنجاح');
  } catch (error) {
    console.error('❌ خطأ في المزامنة:', error);
  }
}

// ===== تسجيل معلومات Service Worker =====
console.log('🔧 Service Worker لمنصة البث العربية جاهز');
console.log('📦 إصدار التخزين المؤقت:', CACHE_NAME);
console.log('🌐 يدعم العمل بدون إنترنت والإشعارات');
