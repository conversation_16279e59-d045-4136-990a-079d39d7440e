<?php
/**
 * 🔧 ملف التكوين الرئيسي لمنصة البث الشاملة
 * يحتوي على جميع الإعدادات والثوابت المطلوبة للموقع
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}

// ==========================================
// 🌐 إعدادات الموقع الأساسية
// ==========================================

// معلومات الموقع
define('SITE_NAME', 'منصة البث الشاملة');
define('SITE_DESCRIPTION', 'شاهد أفضل الأفلام والمسلسلات بجودة عالية');
define('SITE_KEYWORDS', 'أفلام, مسلسلات, بث, مشاهدة, ترفيه');
define('SITE_AUTHOR', 'فريق منصة البث الشاملة');
define('SITE_VERSION', '1.0.0');

// روابط الموقع
define('SITE_URL', 'http://localhost/streaming_platform');
define('BASE_URL', SITE_URL . '/website');
define('ADMIN_URL', BASE_URL . '/admin');
define('API_URL', BASE_URL . '/api');
define('MEDIA_URL', SITE_URL . '/media');
define('UPLOADS_URL', BASE_URL . '/uploads');

// مسارات الملفات
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('VIEWS_PATH', ROOT_PATH . '/public/views');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('CACHE_PATH', ROOT_PATH . '/cache');
define('LOGS_PATH', ROOT_PATH . '/logs');

// ==========================================
// 🗄️ إعدادات قاعدة البيانات
// ==========================================

define('DB_HOST', 'localhost');
define('DB_NAME', 'streaming_platform');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');
define('DB_PREFIX', 'sp_');

// إعدادات الاتصال
define('DB_TIMEOUT', 30);
define('DB_PERSISTENT', false);

// ==========================================
// 🔐 إعدادات الأمان
// ==========================================

// مفاتيح التشفير
define('ENCRYPTION_KEY', 'your-secret-encryption-key-here');
define('JWT_SECRET', 'your-jwt-secret-key-here');
define('CSRF_SECRET', 'your-csrf-secret-key-here');

// إعدادات الجلسات
define('SESSION_NAME', 'streaming_session');
define('SESSION_LIFETIME', 86400); // 24 ساعة
define('SESSION_SECURE', false); // true في الإنتاج مع HTTPS
define('SESSION_HTTPONLY', true);

// إعدادات كلمات المرور
define('PASSWORD_MIN_LENGTH', 8);
define('PASSWORD_REQUIRE_UPPERCASE', true);
define('PASSWORD_REQUIRE_LOWERCASE', true);
define('PASSWORD_REQUIRE_NUMBERS', true);
define('PASSWORD_REQUIRE_SYMBOLS', true);

// إعدادات محاولات تسجيل الدخول
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 1800); // 30 دقيقة

// ==========================================
// 📧 إعدادات البريد الإلكتروني
// ==========================================

define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_SECURE', 'tls');
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', SITE_NAME);

// قوالب البريد الإلكتروني
define('EMAIL_TEMPLATES_PATH', INCLUDES_PATH . '/email_templates');

// ==========================================
// 📁 إعدادات الملفات والرفع
// ==========================================

// أنواع الملفات المسموحة
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_VIDEO_TYPES', ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv']);
define('ALLOWED_SUBTITLE_TYPES', ['srt', 'vtt', 'ass']);

// أحجام الملفات القصوى (بالبايت)
define('MAX_IMAGE_SIZE', 10 * 1024 * 1024); // 10 ميجابايت
define('MAX_VIDEO_SIZE', 2 * 1024 * 1024 * 1024); // 2 جيجابايت
define('MAX_SUBTITLE_SIZE', 1 * 1024 * 1024); // 1 ميجابايت

// مجلدات الرفع
define('IMAGES_FOLDER', 'images');
define('VIDEOS_FOLDER', 'videos');
define('SUBTITLES_FOLDER', 'subtitles');
define('THUMBNAILS_FOLDER', 'thumbnails');

// ==========================================
// 🎬 إعدادات المحتوى والفيديو
// ==========================================

// أنواع المحتوى
define('CONTENT_TYPE_MOVIE', 'movie');
define('CONTENT_TYPE_SERIES', 'series');
define('CONTENT_TYPE_DOCUMENTARY', 'documentary');
define('CONTENT_TYPE_LIVE', 'live');

// جودات الفيديو
define('VIDEO_QUALITIES', ['240p', '360p', '480p', '720p', '1080p', '4K']);
define('DEFAULT_VIDEO_QUALITY', '720p');

// إعدادات التشغيل
define('DEFAULT_PLAYBACK_SPEED', 1.0);
define('PLAYBACK_SPEEDS', [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]);

// إعدادات الترجمة
define('DEFAULT_SUBTITLE_LANGUAGE', 'ar');
define('SUPPORTED_SUBTITLE_LANGUAGES', ['ar', 'en', 'fr', 'tr']);

// ==========================================
// 💳 إعدادات الاشتراكات والدفع
// ==========================================

// أنواع الاشتراكات
define('SUBSCRIPTION_FREE', 'free');
define('SUBSCRIPTION_BASIC', 'basic');
define('SUBSCRIPTION_PREMIUM', 'premium');
define('SUBSCRIPTION_VIP', 'vip');

// أسعار الاشتراكات (بالدولار)
define('SUBSCRIPTION_PRICES', [
    SUBSCRIPTION_FREE => 0,
    SUBSCRIPTION_BASIC => 9.99,
    SUBSCRIPTION_PREMIUM => 19.99,
    SUBSCRIPTION_VIP => 29.99
]);

// مدة الاشتراكات (بالأيام)
define('SUBSCRIPTION_DURATIONS', [
    SUBSCRIPTION_FREE => 0,
    SUBSCRIPTION_BASIC => 30,
    SUBSCRIPTION_PREMIUM => 30,
    SUBSCRIPTION_VIP => 30
]);

// إعدادات Stripe
define('STRIPE_PUBLIC_KEY', 'pk_test_your_stripe_public_key');
define('STRIPE_SECRET_KEY', 'sk_test_your_stripe_secret_key');

// ==========================================
// 🔔 إعدادات الإشعارات
// ==========================================

// Firebase
define('FIREBASE_SERVER_KEY', 'your-firebase-server-key');
define('FIREBASE_SENDER_ID', 'your-firebase-sender-id');

// أنواع الإشعارات
define('NOTIFICATION_NEW_CONTENT', 'new_content');
define('NOTIFICATION_SUBSCRIPTION', 'subscription');
define('NOTIFICATION_REMINDER', 'reminder');
define('NOTIFICATION_PROMOTION', 'promotion');

// ==========================================
// 🌍 إعدادات اللغة والتوطين
// ==========================================

// اللغة الافتراضية
define('DEFAULT_LANGUAGE', 'ar');

// اللغات المدعومة
define('SUPPORTED_LANGUAGES', [
    'ar' => 'العربية',
    'en' => 'English',
    'fr' => 'Français',
    'tr' => 'Türkçe'
]);

// المنطقة الزمنية
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');

// تنسيق التاريخ والوقت
define('DATE_FORMAT', 'Y-m-d');
define('TIME_FORMAT', 'H:i:s');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');

// ==========================================
// 📊 إعدادات التخزين المؤقت والأداء
// ==========================================

// تفعيل التخزين المؤقت
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // ساعة واحدة

// ضغط الصفحات
define('GZIP_ENABLED', true);

// تحسين الصور
define('IMAGE_OPTIMIZATION', true);
define('IMAGE_QUALITY', 85);

// ==========================================
// 🔍 إعدادات البحث والفهرسة
// ==========================================

// عدد النتائج في الصفحة
define('SEARCH_RESULTS_PER_PAGE', 20);
define('CONTENT_PER_PAGE', 24);

// الحد الأدنى لطول البحث
define('MIN_SEARCH_LENGTH', 2);

// ==========================================
// 📱 إعدادات API والتطبيق الجوال
// ==========================================

// إصدار API
define('API_VERSION', 'v1');

// مفاتيح API
define('API_KEY_HEADER', 'X-API-Key');
define('API_RATE_LIMIT', 1000); // طلب في الساعة

// إعدادات CORS
define('CORS_ALLOWED_ORIGINS', ['*']);
define('CORS_ALLOWED_METHODS', ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']);
define('CORS_ALLOWED_HEADERS', ['Content-Type', 'Authorization', 'X-API-Key']);

// ==========================================
// 🛠️ إعدادات التطوير والتصحيح
// ==========================================

// وضع التطوير
define('DEBUG_MODE', true);
define('SHOW_ERRORS', DEBUG_MODE);
define('LOG_ERRORS', true);

// مستويات السجلات
define('LOG_LEVEL_ERROR', 'error');
define('LOG_LEVEL_WARNING', 'warning');
define('LOG_LEVEL_INFO', 'info');
define('LOG_LEVEL_DEBUG', 'debug');

// ==========================================
// 🔗 إعدادات وسائل التواصل الاجتماعي
// ==========================================

// روابط وسائل التواصل
define('SOCIAL_FACEBOOK', 'https://facebook.com/streamingplatform');
define('SOCIAL_TWITTER', 'https://twitter.com/streamingplatform');
define('SOCIAL_INSTAGRAM', 'https://instagram.com/streamingplatform');
define('SOCIAL_YOUTUBE', 'https://youtube.com/streamingplatform');

// مفاتيح تسجيل الدخول الاجتماعي
define('GOOGLE_CLIENT_ID', 'your-google-client-id');
define('GOOGLE_CLIENT_SECRET', 'your-google-client-secret');
define('FACEBOOK_APP_ID', 'your-facebook-app-id');
define('FACEBOOK_APP_SECRET', 'your-facebook-app-secret');

// ==========================================
// 📈 إعدادات التحليلات والإحصائيات
// ==========================================

// Google Analytics
define('GOOGLE_ANALYTICS_ID', 'GA_MEASUREMENT_ID');

// إعدادات التتبع
define('TRACK_USER_ACTIVITY', true);
define('TRACK_VIDEO_VIEWS', true);
define('TRACK_SEARCH_QUERIES', true);

// ==========================================
// ⚙️ إعدادات النظام
// ==========================================

// حد الذاكرة
ini_set('memory_limit', '256M');

// حد وقت التنفيذ
ini_set('max_execution_time', 300);

// حد حجم الرفع
ini_set('upload_max_filesize', '100M');
ini_set('post_max_size', '100M');

// تعيين المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

// تعيين الترميز
mb_internal_encoding('UTF-8');

// ==========================================
// 🚀 تهيئة النظام
// ==========================================

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_set_cookie_params([
        'lifetime' => SESSION_LIFETIME,
        'path' => '/',
        'domain' => '',
        'secure' => SESSION_SECURE,
        'httponly' => SESSION_HTTPONLY,
        'samesite' => 'Lax'
    ]);
    session_start();
}

// تحديد مستوى الأخطاء
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// تحميل الملفات الأساسية
require_once INCLUDES_PATH . '/database.php';
require_once INCLUDES_PATH . '/functions.php';
require_once INCLUDES_PATH . '/auth.php';
require_once INCLUDES_PATH . '/security.php';

// تهيئة قاعدة البيانات
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    if (DEBUG_MODE) {
        die('Database connection failed: ' . $e->getMessage());
    } else {
        die('Service temporarily unavailable');
    }
}

// تحديد اللغة الحالية
$current_language = $_SESSION['language'] ?? $_COOKIE['language'] ?? DEFAULT_LANGUAGE;
if (!array_key_exists($current_language, SUPPORTED_LANGUAGES)) {
    $current_language = DEFAULT_LANGUAGE;
}

// تحميل ملف الترجمة
$lang_file = INCLUDES_PATH . "/languages/{$current_language}.php";
if (file_exists($lang_file)) {
    require_once $lang_file;
} else {
    require_once INCLUDES_PATH . "/languages/" . DEFAULT_LANGUAGE . ".php";
}

// تعيين ثوابت إضافية
define('CURRENT_LANGUAGE', $current_language);
define('IS_LOGGED_IN', isset($_SESSION['user_id']));
define('CURRENT_USER_ID', $_SESSION['user_id'] ?? null);
define('CURRENT_USER_ROLE', $_SESSION['user_role'] ?? 'guest');

// تنظيف البيانات القديمة (يتم تشغيله أحياناً)
if (rand(1, 100) === 1) {
    cleanupOldSessions();
    cleanupOldLogs();
    cleanupOldCache();
}

/**
 * تنظيف الجلسات القديمة
 */
function cleanupOldSessions() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("DELETE FROM " . DB_PREFIX . "sessions WHERE expires_at < NOW()");
        $stmt->execute();
    } catch (Exception $e) {
        error_log("Failed to cleanup old sessions: " . $e->getMessage());
    }
}

/**
 * تنظيف السجلات القديمة
 */
function cleanupOldLogs() {
    $log_files = glob(LOGS_PATH . '/*.log');
    foreach ($log_files as $file) {
        if (filemtime($file) < time() - (30 * 24 * 60 * 60)) { // 30 يوم
            unlink($file);
        }
    }
}

/**
 * تنظيف التخزين المؤقت القديم
 */
function cleanupOldCache() {
    if (is_dir(CACHE_PATH)) {
        $cache_files = glob(CACHE_PATH . '/*');
        foreach ($cache_files as $file) {
            if (is_file($file) && filemtime($file) < time() - CACHE_LIFETIME) {
                unlink($file);
            }
        }
    }
}

// إشارة أن التكوين تم تحميله بنجاح
define('CONFIG_LOADED', true);
?>
