<?php
/**
 * 🚨 صفحة خطأ الخادم 500
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}

http_response_code(500);
?>

<!DOCTYPE html>
<html lang="<?php echo CURRENT_LANGUAGE ?? 'ar'; ?>" dir="<?php echo (CURRENT_LANGUAGE ?? 'ar') === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطأ في الخادم - <?php echo SITE_NAME ?? 'منصة البث'; ?></title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .error-container {
            text-align: center;
            max-width: 600px;
            padding: 40px 20px;
        }

        .error-code {
            font-size: 8rem;
            font-weight: bold;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
        }

        .error-title {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .error-message {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .error-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: rgba(255, 255, 255, 0.9);
            color: #ff6b6b;
            border-color: transparent;
        }

        .btn-primary:hover {
            background: white;
            color: #ee5a24;
        }

        .error-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .error-code {
                font-size: 6rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-message {
                font-size: 1rem;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }

        /* تأثيرات الحركة */
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .error-code {
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .error-container > * {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        .error-container > *:nth-child(2) { animation-delay: 0.1s; }
        .error-container > *:nth-child(3) { animation-delay: 0.2s; }
        .error-container > *:nth-child(4) { animation-delay: 0.3s; }
        .error-container > *:nth-child(5) { animation-delay: 0.4s; }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">⚠️</div>
        <div class="error-code">500</div>
        <h1 class="error-title">خطأ في الخادم</h1>
        <p class="error-message">
            عذراً، حدث خطأ غير متوقع في الخادم.
            <br>
            نحن نعمل على حل هذه المشكلة. يرجى المحاولة مرة أخرى لاحقاً.
        </p>

        <div class="error-actions">
            <a href="/" class="btn btn-primary">
                🏠 الصفحة الرئيسية
            </a>
            <a href="javascript:location.reload()" class="btn">
                🔄 إعادة المحاولة
            </a>
            <a href="javascript:history.back()" class="btn">
                ↩️ العودة للخلف
            </a>
        </div>
    </div>

    <script>
        // تتبع خطأ 500
        if (typeof trackEvent === 'function') {
            trackEvent('500_error', {
                page: window.location.pathname,
                referrer: document.referrer,
                user_agent: navigator.userAgent,
                timestamp: new Date().toISOString()
            });
        }

        // إعادة المحاولة التلقائية بعد 30 ثانية
        setTimeout(function() {
            if (confirm('هل تريد إعادة المحاولة تلقائياً؟')) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
