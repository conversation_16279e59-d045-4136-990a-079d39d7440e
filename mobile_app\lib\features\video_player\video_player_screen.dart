import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:video_player/video_player.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../../core/constants/app_constants.dart';

/// 📺 شاشة مشغل الفيديو
class VideoPlayerScreen extends ConsumerStatefulWidget {
  final String contentId;
  final String? episodeId;

  const VideoPlayerScreen({
    super.key,
    required this.contentId,
    this.episodeId,
  });

  @override
  ConsumerState<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends ConsumerState<VideoPlayerScreen> {
  late VideoPlayerController _controller;
  bool _isInitialized = false;
  bool _isPlaying = false;
  bool _showControls = true;
  bool _isFullscreen = false;
  bool _isLoading = true;
  
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  
  // إعدادات المشغل
  double _playbackSpeed = 1.0;
  String _selectedQuality = '720p';
  bool _subtitlesEnabled = true;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _setupWakelock();
  }

  @override
  void dispose() {
    _controller.dispose();
    WakelockPlus.disable();
    _exitFullscreen();
    super.dispose();
  }

  Future<void> _initializePlayer() async {
    try {
      // محاكاة رابط الفيديو
      const videoUrl = 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4';
      
      _controller = VideoPlayerController.networkUrl(Uri.parse(videoUrl));
      
      await _controller.initialize();
      
      _controller.addListener(_videoListener);
      
      setState(() {
        _isInitialized = true;
        _isLoading = false;
        _duration = _controller.value.duration;
      });
      
      // بدء التشغيل التلقائي
      _controller.play();
      setState(() {
        _isPlaying = true;
      });
      
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showError('خطأ في تحميل الفيديو');
    }
  }

  void _videoListener() {
    if (mounted) {
      setState(() {
        _position = _controller.value.position;
        _isPlaying = _controller.value.isPlaying;
      });
    }
  }

  void _setupWakelock() {
    WakelockPlus.enable();
  }

  void _togglePlayPause() {
    if (_controller.value.isPlaying) {
      _controller.pause();
    } else {
      _controller.play();
    }
    _showControlsTemporarily();
  }

  void _seekTo(Duration position) {
    _controller.seekTo(position);
    _showControlsTemporarily();
  }

  void _seekForward() {
    final newPosition = _position + AppConstants.videoSeekDuration;
    _seekTo(newPosition > _duration ? _duration : newPosition);
  }

  void _seekBackward() {
    final newPosition = _position - AppConstants.videoSeekDuration;
    _seekTo(newPosition < Duration.zero ? Duration.zero : newPosition);
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });
    
    if (_isFullscreen) {
      _enterFullscreen();
    } else {
      _exitFullscreen();
    }
  }

  void _enterFullscreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  void _exitFullscreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
  }

  void _showControlsTemporarily() {
    setState(() {
      _showControls = true;
    });
    
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _isPlaying) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppConstants.dangerColor,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: _isLoading
            ? _buildLoadingWidget()
            : _isInitialized
                ? _buildVideoPlayer()
                : _buildErrorWidget(),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppConstants.primaryColor,
          ),
          SizedBox(height: 16),
          Text(
            'جاري تحميل الفيديو...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppConstants.dangerColor,
          ),
          const SizedBox(height: 16),
          const Text(
            'خطأ في تحميل الفيديو',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'تحقق من اتصالك بالإنترنت وحاول مرة أخرى',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              _initializePlayer();
            },
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoPlayer() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showControls = !_showControls;
        });
        if (_showControls) {
          _showControlsTemporarily();
        }
      },
      child: Stack(
        children: [
          // مشغل الفيديو
          Center(
            child: AspectRatio(
              aspectRatio: _controller.value.aspectRatio,
              child: VideoPlayer(_controller),
            ),
          ),
          
          // أدوات التحكم
          if (_showControls) _buildControls(),
          
          // مؤشر التحميل
          if (_controller.value.isBuffering)
            const Center(
              child: CircularProgressIndicator(
                color: AppConstants.primaryColor,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildControls() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.7),
            Colors.transparent,
            Colors.transparent,
            Colors.black.withOpacity(0.7),
          ],
        ),
      ),
      child: Column(
        children: [
          // شريط التحكم العلوي
          _buildTopControls(),
          
          // المنطقة الوسطى
          Expanded(
            child: Row(
              children: [
                // زر الترجيع
                Expanded(
                  child: GestureDetector(
                    onDoubleTap: _seekBackward,
                    child: Container(
                      color: Colors.transparent,
                      child: const Center(
                        child: Icon(
                          Icons.fast_rewind,
                          color: Colors.white,
                          size: 40,
                        ),
                      ),
                    ),
                  ),
                ),
                
                // زر التشغيل/الإيقاف
                GestureDetector(
                  onTap: _togglePlayPause,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _isPlaying ? Icons.pause : Icons.play_arrow,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                ),
                
                // زر التقديم
                Expanded(
                  child: GestureDetector(
                    onDoubleTap: _seekForward,
                    child: Container(
                      color: Colors.transparent,
                      child: const Center(
                        child: Icon(
                          Icons.fast_forward,
                          color: Colors.white,
                          size: 40,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // شريط التحكم السفلي
          _buildBottomControls(),
        ],
      ),
    );
  }

  Widget _buildTopControls() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // زر الرجوع
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
          ),
          
          const Spacer(),
          
          // زر الإعدادات
          IconButton(
            onPressed: _showSettingsDialog,
            icon: const Icon(
              Icons.settings,
              color: Colors.white,
            ),
          ),
          
          // زر ملء الشاشة
          IconButton(
            onPressed: _toggleFullscreen,
            icon: Icon(
              _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomControls() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط التقدم
          Row(
            children: [
              Text(
                _formatDuration(_position),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: AppConstants.primaryColor,
                    inactiveTrackColor: Colors.white.withOpacity(0.3),
                    thumbColor: AppConstants.primaryColor,
                    overlayColor: AppConstants.primaryColor.withOpacity(0.3),
                    trackHeight: 3,
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 6,
                    ),
                  ),
                  child: Slider(
                    value: _position.inMilliseconds.toDouble(),
                    min: 0,
                    max: _duration.inMilliseconds.toDouble(),
                    onChanged: (value) {
                      _seekTo(Duration(milliseconds: value.toInt()));
                    },
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                _formatDuration(_duration),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // أزرار إضافية
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // زر الترجمة
              IconButton(
                onPressed: _toggleSubtitles,
                icon: Icon(
                  _subtitlesEnabled ? Icons.subtitles : Icons.subtitles_off,
                  color: _subtitlesEnabled ? AppConstants.primaryColor : Colors.white,
                ),
              ),
              
              // زر سرعة التشغيل
              TextButton(
                onPressed: _showSpeedDialog,
                child: Text(
                  '${_playbackSpeed}x',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              
              // زر الجودة
              TextButton(
                onPressed: _showQualityDialog,
                child: Text(
                  _selectedQuality,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    
    if (duration.inHours > 0) {
      return '$hours:$minutes:$seconds';
    } else {
      return '$minutes:$seconds';
    }
  }

  void _toggleSubtitles() {
    setState(() {
      _subtitlesEnabled = !_subtitlesEnabled;
    });
  }

  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعدادات المشغل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.speed),
              title: const Text('سرعة التشغيل'),
              subtitle: Text('${_playbackSpeed}x'),
              onTap: () {
                Navigator.pop(context);
                _showSpeedDialog();
              },
            ),
            ListTile(
              leading: const Icon(Icons.hd),
              title: const Text('الجودة'),
              subtitle: Text(_selectedQuality),
              onTap: () {
                Navigator.pop(context);
                _showQualityDialog();
              },
            ),
            SwitchListTile(
              secondary: const Icon(Icons.subtitles),
              title: const Text('الترجمة'),
              value: _subtitlesEnabled,
              onChanged: (value) {
                setState(() {
                  _subtitlesEnabled = value;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showSpeedDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سرعة التشغيل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AppConstants.videoPlaybackSpeeds.map((speed) {
            return RadioListTile<double>(
              title: Text('${speed}x'),
              value: speed,
              groupValue: _playbackSpeed,
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _playbackSpeed = value;
                  });
                  _controller.setPlaybackSpeed(value);
                  Navigator.pop(context);
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showQualityDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('جودة الفيديو'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AppConstants.videoQualities.map((quality) {
            return RadioListTile<String>(
              title: Text(quality),
              value: quality,
              groupValue: _selectedQuality,
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedQuality = value;
                  });
                  // هنا يمكن تغيير مصدر الفيديو حسب الجودة
                  Navigator.pop(context);
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }
}
