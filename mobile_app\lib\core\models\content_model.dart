import 'package:freezed_annotation/freezed_annotation.dart';

part 'content_model.freezed.dart';
part 'content_model.g.dart';

/// 🎬 نموذج المحتوى
/// يمثل الأفلام والمسلسلات والوثائقيات
@freezed
class ContentModel with _$ContentModel {
  const factory ContentModel({
    required int id,
    required String title,
    required String slug,
    String? description,
    String? synopsis,
    required ContentType type,
    String? genre,
    String? subgenre,
    DateTime? releaseDate,
    int? duration, // بالدقائق
    String? language,
    String? country,
    String? director,
    String? producer,
    String? writer,
    String? cast,
    double? ourRating,
    double? imdbRating,
    String? ageRating,
    String? poster,
    String? banner,
    String? trailerUrl,
    String? videoUrl,
    String? videoQuality,
    int? fileSize,
    List<SubtitleTrack>? subtitles,
    List<AudioTrack>? audioTracks,
    List<String>? tags,
    @Default(0) int viewCount,
    @Default(0) int downloadCount,
    @Default(0) int likeCount,
    @Default(0) int commentCount,
    @Default(SubscriptionType.free) SubscriptionType subscriptionRequired,
    @Default(false) bool isFeatured,
    @Default(false) bool isTrending,
    @Default(true) bool isNew,
    @Default(ContentStatus.published) ContentStatus status,
    DateTime? publishedAt,
    int? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    
    // حقول إضافية للمستخدم
    @Default(false) bool isFavorite,
    @Default(false) bool inWatchlist,
    double? userRating,
    double? watchProgress, // نسبة مئوية
    DateTime? lastWatched,
    
    // للمسلسلات
    List<SeasonModel>? seasons,
    int? totalSeasons,
    int? totalEpisodes,
    
    // إحصائيات
    double? averageRating,
    int? totalRatings,
  }) = _ContentModel;

  factory ContentModel.fromJson(Map<String, dynamic> json) =>
      _$ContentModelFromJson(json);
}

/// 🎭 أنواع المحتوى
enum ContentType {
  @JsonValue('movie')
  movie,
  @JsonValue('series')
  series,
  @JsonValue('documentary')
  documentary,
  @JsonValue('live')
  live,
}

/// 📊 حالة المحتوى
enum ContentStatus {
  @JsonValue('draft')
  draft,
  @JsonValue('published')
  published,
  @JsonValue('archived')
  archived,
  @JsonValue('deleted')
  deleted,
}

/// 💳 أنواع الاشتراك
enum SubscriptionType {
  @JsonValue('free')
  free,
  @JsonValue('basic')
  basic,
  @JsonValue('premium')
  premium,
  @JsonValue('vip')
  vip,
}

/// 📺 نموذج الموسم
@freezed
class SeasonModel with _$SeasonModel {
  const factory SeasonModel({
    required int id,
    required int contentId,
    required int seasonNumber,
    String? title,
    String? description,
    String? poster,
    DateTime? releaseDate,
    @Default(0) int episodeCount,
    @Default(SeasonStatus.upcoming) SeasonStatus status,
    DateTime? createdAt,
    DateTime? updatedAt,
    
    // الحلقات
    List<EpisodeModel>? episodes,
  }) = _SeasonModel;

  factory SeasonModel.fromJson(Map<String, dynamic> json) =>
      _$SeasonModelFromJson(json);
}

/// 📺 حالة الموسم
enum SeasonStatus {
  @JsonValue('upcoming')
  upcoming,
  @JsonValue('airing')
  airing,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled,
}

/// 🎞️ نموذج الحلقة
@freezed
class EpisodeModel with _$EpisodeModel {
  const factory EpisodeModel({
    required int id,
    required int seasonId,
    required int episodeNumber,
    required String title,
    String? description,
    int? duration, // بالدقائق
    String? videoUrl,
    String? thumbnail,
    DateTime? airDate,
    @Default(0) int viewCount,
    @Default(EpisodeStatus.upcoming) EpisodeStatus status,
    DateTime? createdAt,
    DateTime? updatedAt,
    
    // تقدم المشاهدة للمستخدم
    double? watchProgress,
    DateTime? lastWatched,
  }) = _EpisodeModel;

  factory EpisodeModel.fromJson(Map<String, dynamic> json) =>
      _$EpisodeModelFromJson(json);
}

/// 🎞️ حالة الحلقة
enum EpisodeStatus {
  @JsonValue('upcoming')
  upcoming,
  @JsonValue('available')
  available,
  @JsonValue('unavailable')
  unavailable,
}

/// 📝 نموذج الترجمة
@freezed
class SubtitleTrack with _$SubtitleTrack {
  const factory SubtitleTrack({
    required String language,
    required String label,
    required String url,
    @Default(false) bool isDefault,
  }) = _SubtitleTrack;

  factory SubtitleTrack.fromJson(Map<String, dynamic> json) =>
      _$SubtitleTrackFromJson(json);
}

/// 🔊 نموذج المسار الصوتي
@freezed
class AudioTrack with _$AudioTrack {
  const factory AudioTrack({
    required String language,
    required String label,
    required String url,
    @Default(false) bool isDefault,
  }) = _AudioTrack;

  factory AudioTrack.fromJson(Map<String, dynamic> json) =>
      _$AudioTrackFromJson(json);
}

/// ⭐ نموذج التقييم
@freezed
class RatingModel with _$RatingModel {
  const factory RatingModel({
    required int id,
    required int contentId,
    required int userId,
    required double rating,
    String? review,
    @Default(false) bool isSpoiler,
    @Default(0) int helpfulCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    
    // معلومات المستخدم
    String? userName,
    String? userAvatar,
  }) = _RatingModel;

  factory RatingModel.fromJson(Map<String, dynamic> json) =>
      _$RatingModelFromJson(json);
}

/// 💬 نموذج التعليق
@freezed
class CommentModel with _$CommentModel {
  const factory CommentModel({
    required int id,
    required int contentId,
    required int userId,
    int? parentId,
    required String comment,
    @Default(false) bool isSpoiler,
    @Default(0) int likeCount,
    @Default(0) int dislikeCount,
    @Default(CommentStatus.pending) CommentStatus status,
    DateTime? createdAt,
    DateTime? updatedAt,
    
    // معلومات المستخدم
    String? userName,
    String? userAvatar,
    
    // الردود
    List<CommentModel>? replies,
  }) = _CommentModel;

  factory CommentModel.fromJson(Map<String, dynamic> json) =>
      _$CommentModelFromJson(json);
}

/// 💬 حالة التعليق
enum CommentStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('approved')
  approved,
  @JsonValue('rejected')
  rejected,
  @JsonValue('hidden')
  hidden,
}

/// 📊 نموذج إحصائيات المحتوى
@freezed
class ContentStatsModel with _$ContentStatsModel {
  const factory ContentStatsModel({
    required int contentId,
    @Default(0) int totalViews,
    @Default(0) int uniqueViewers,
    @Default(0) int totalDownloads,
    @Default(0) int totalFavorites,
    @Default(0) int totalWatchlist,
    @Default(0) int totalRatings,
    double? averageRating,
    @Default(0) int totalComments,
    
    // إحصائيات زمنية
    Map<String, int>? viewsByDay,
    Map<String, int>? viewsByCountry,
    Map<String, int>? viewsByDevice,
  }) = _ContentStatsModel;

  factory ContentStatsModel.fromJson(Map<String, dynamic> json) =>
      _$ContentStatsModelFromJson(json);
}

/// 🔍 نموذج نتائج البحث
@freezed
class SearchResultModel with _$SearchResultModel {
  const factory SearchResultModel({
    required List<ContentModel> results,
    required PaginationModel pagination,
    required SearchFiltersModel filters,
  }) = _SearchResultModel;

  factory SearchResultModel.fromJson(Map<String, dynamic> json) =>
      _$SearchResultModelFromJson(json);
}

/// 📄 نموذج الصفحات
@freezed
class PaginationModel with _$PaginationModel {
  const factory PaginationModel({
    required int currentPage,
    required int totalPages,
    required int totalResults,
    required int perPage,
    required bool hasNextPage,
    required bool hasPrevPage,
  }) = _PaginationModel;

  factory PaginationModel.fromJson(Map<String, dynamic> json) =>
      _$PaginationModelFromJson(json);
}

/// 🎛️ نموذج فلاتر البحث
@freezed
class SearchFiltersModel with _$SearchFiltersModel {
  const factory SearchFiltersModel({
    String? query,
    ContentType? type,
    String? genre,
    int? year,
    double? rating,
    String? sortBy,
    String? sortOrder,
  }) = _SearchFiltersModel;

  factory SearchFiltersModel.fromJson(Map<String, dynamic> json) =>
      _$SearchFiltersModelFromJson(json);
}

/// 📱 نموذج التحميل
@freezed
class DownloadModel with _$DownloadModel {
  const factory DownloadModel({
    required int id,
    required int userId,
    required int contentId,
    int? episodeId,
    required String quality,
    int? fileSize,
    String? downloadUrl,
    @Default(DownloadStatus.pending) DownloadStatus status,
    @Default(0.0) double progress,
    DateTime? expiresAt,
    DateTime? downloadedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    
    // معلومات المحتوى
    ContentModel? content,
    EpisodeModel? episode,
  }) = _DownloadModel;

  factory DownloadModel.fromJson(Map<String, dynamic> json) =>
      _$DownloadModelFromJson(json);
}

/// 📱 حالة التحميل
enum DownloadStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('downloading')
  downloading,
  @JsonValue('completed')
  completed,
  @JsonValue('failed')
  failed,
  @JsonValue('expired')
  expired,
}

/// 🎯 امتدادات مفيدة للمحتوى
extension ContentModelExtensions on ContentModel {
  /// هل المحتوى متاح للمشاهدة؟
  bool get isAvailable => status == ContentStatus.published;
  
  /// هل المحتوى مجاني؟
  bool get isFree => subscriptionRequired == SubscriptionType.free;
  
  /// هل المحتوى مسلسل؟
  bool get isSeries => type == ContentType.series;
  
  /// هل المحتوى فيلم؟
  bool get isMovie => type == ContentType.movie;
  
  /// مدة المحتوى بصيغة نصية
  String get formattedDuration {
    if (duration == null) return '';
    
    final hours = duration! ~/ 60;
    final minutes = duration! % 60;
    
    if (hours > 0) {
      return '${hours}س ${minutes}د';
    } else {
      return '${minutes}د';
    }
  }
  
  /// سنة الإصدار
  String get releaseYear {
    if (releaseDate == null) return '';
    return releaseDate!.year.toString();
  }
  
  /// التقييم بصيغة نصية
  String get formattedRating {
    if (ourRating == null) return '';
    return '${ourRating!.toStringAsFixed(1)}/10';
  }
  
  /// عدد المشاهدات بصيغة نصية
  String get formattedViewCount {
    if (viewCount < 1000) {
      return viewCount.toString();
    } else if (viewCount < 1000000) {
      return '${(viewCount / 1000).toStringAsFixed(1)}ك';
    } else {
      return '${(viewCount / 1000000).toStringAsFixed(1)}م';
    }
  }
  
  /// هل تم مشاهدة المحتوى بالكامل؟
  bool get isCompleted => watchProgress != null && watchProgress! >= 90;
  
  /// هل بدأت مشاهدة المحتوى؟
  bool get isStarted => watchProgress != null && watchProgress! > 0;
  
  /// نسبة التقدم كنص
  String get progressText {
    if (watchProgress == null || watchProgress! <= 0) return '';
    return '${watchProgress!.toInt()}%';
  }
}

/// 🎞️ امتدادات مفيدة للحلقة
extension EpisodeModelExtensions on EpisodeModel {
  /// هل الحلقة متاحة؟
  bool get isAvailable => status == EpisodeStatus.available;
  
  /// مدة الحلقة بصيغة نصية
  String get formattedDuration {
    if (duration == null) return '';
    
    final hours = duration! ~/ 60;
    final minutes = duration! % 60;
    
    if (hours > 0) {
      return '${hours}س ${minutes}د';
    } else {
      return '${minutes}د';
    }
  }
  
  /// رقم الحلقة بصيغة نصية
  String get episodeNumberText => 'الحلقة $episodeNumber';
  
  /// هل تم مشاهدة الحلقة بالكامل؟
  bool get isCompleted => watchProgress != null && watchProgress! >= 90;
  
  /// هل بدأت مشاهدة الحلقة؟
  bool get isStarted => watchProgress != null && watchProgress! > 0;
}
