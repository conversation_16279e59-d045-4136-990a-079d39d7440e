<?php
/**
 * 🎬 صفحة مشغل الفيديو
 * مشغل فيديو احترافي مع جميع الميزات
 */

// تعريف ثابت المنصة
define('STREAMING_PLATFORM', true);

// تحميل ملف التهيئة
require_once __DIR__ . '/../includes/init.php';

// الحصول على معرف المحتوى
$contentId = $_GET['id'] ?? null;
$episodeId = $_GET['episode'] ?? null;

if (!$contentId) {
    header('Location: /');
    exit;
}

// الحصول على بيانات المحتوى
$content = null;
$episode = null;
$seasons = [];
$episodes = [];

if (DB_CONNECTED) {
    try {
        global $db;
        
        // الحصول على المحتوى
        $content = $db->selectOne(
            "SELECT * FROM content WHERE id = ? AND status = 'published'",
            [$contentId]
        );
        
        if (!$content) {
            header('Location: /404.php');
            exit;
        }
        
        // التحقق من صلاحية الوصول
        if (!canAccessContent($content)) {
            header('Location: /subscription');
            exit;
        }
        
        // زيادة عدد المشاهدات
        $db->query(
            "UPDATE content SET view_count = view_count + 1 WHERE id = ?",
            [$contentId]
        );
        
        // إذا كان مسلسل، احصل على المواسم والحلقات
        if ($content['type'] === 'series') {
            $seasons = $db->select(
                "SELECT * FROM seasons WHERE content_id = ? ORDER BY season_number ASC",
                [$contentId]
            );
            
            if (!empty($seasons)) {
                foreach ($seasons as $season) {
                    $seasonEpisodes = $db->select(
                        "SELECT * FROM episodes WHERE season_id = ? ORDER BY episode_number ASC",
                        [$season['id']]
                    );
                    $episodes[$season['id']] = $seasonEpisodes;
                }
                
                // إذا تم تحديد حلقة معينة
                if ($episodeId) {
                    $episode = $db->selectOne(
                        "SELECT e.*, s.season_number FROM episodes e 
                         JOIN seasons s ON e.season_id = s.id 
                         WHERE e.id = ?",
                        [$episodeId]
                    );
                }
            }
        }
        
        // تسجيل نشاط المشاهدة
        if (IS_LOGGED_IN) {
            logUserActivity('video_watch', [
                'content_id' => $contentId,
                'episode_id' => $episodeId,
                'content_title' => $content['title']
            ]);
        }
        
    } catch (Exception $e) {
        error_log("خطأ في تحميل المحتوى: " . $e->getMessage());
        header('Location: /500.php');
        exit;
    }
}

// تحديد الفيديو المراد تشغيله
$videoToPlay = $episode ?: $content;
$videoTitle = $episode ? 
    $content['title'] . ' - الموسم ' . $episode['season_number'] . ' الحلقة ' . $episode['episode_number'] :
    $content['title'];

// الحصول على الحلقة التالية (للمسلسلات)
$nextEpisode = null;
if ($episode && !empty($episodes)) {
    foreach ($episodes as $seasonId => $seasonEpisodes) {
        foreach ($seasonEpisodes as $index => $ep) {
            if ($ep['id'] == $episodeId && isset($seasonEpisodes[$index + 1])) {
                $nextEpisode = $seasonEpisodes[$index + 1];
                break 2;
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($videoTitle); ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="stylesheet" href="/assets/css/video-player.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">
    
    <meta name="description" content="<?php echo htmlspecialchars($content['description'] ?? ''); ?>">
    <meta property="og:title" content="<?php echo htmlspecialchars($videoTitle); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($content['description'] ?? ''); ?>">
    <meta property="og:image" content="<?php echo htmlspecialchars($content['poster'] ?? ''); ?>">
    <meta property="og:type" content="video.movie">
    
    <style>
        body {
            background: #000;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .player-container {
            position: relative;
            width: 100%;
            height: 100vh;
            background: #000;
        }
        
        .video-wrapper {
            width: 100%;
            height: 70vh;
            min-height: 400px;
        }
        
        .content-info {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            padding: 30px;
            min-height: 30vh;
        }
        
        .content-header {
            display: flex;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .content-poster {
            width: 200px;
            height: 300px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        .content-poster img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .content-details {
            flex: 1;
        }
        
        .content-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .content-meta {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .meta-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            backdrop-filter: blur(10px);
        }
        
        .content-description {
            font-size: 1.1rem;
            line-height: 1.6;
            opacity: 0.9;
            margin-bottom: 25px;
        }
        
        .content-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        .episodes-section {
            margin-top: 40px;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 20px;
            color: #fff;
        }
        
        .seasons-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            overflow-x: auto;
        }
        
        .season-tab {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }
        
        .season-tab.active {
            background: #ff6b6b;
        }
        
        .episodes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .episode-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .episode-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .episode-card.current {
            border: 2px solid #ff6b6b;
        }
        
        .episode-thumbnail {
            width: 100%;
            height: 150px;
            background: linear-gradient(45deg, #333, #555);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .episode-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .episode-number {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .episode-info {
            padding: 15px;
        }
        
        .episode-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: white;
        }
        
        .episode-description {
            font-size: 0.9rem;
            opacity: 0.8;
            line-height: 1.4;
            color: white;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            z-index: 1000;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
        }
        
        @media (max-width: 768px) {
            .content-header {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }
            
            .content-poster {
                width: 150px;
                height: 225px;
            }
            
            .content-title {
                font-size: 2rem;
            }
            
            .video-wrapper {
                height: 50vh;
            }
            
            .content-info {
                padding: 20px;
            }
            
            .episodes-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- زر العودة -->
    <button class="back-button" onclick="history.back()">
        <i class="fas fa-arrow-right"></i>
    </button>
    
    <div class="player-container">
        <!-- مشغل الفيديو -->
        <div class="video-wrapper">
            <div id="videoPlayer"></div>
        </div>
        
        <!-- معلومات المحتوى -->
        <div class="content-info">
            <div class="content-header">
                <?php if ($content['poster']): ?>
                <div class="content-poster">
                    <img src="<?php echo htmlspecialchars($content['poster']); ?>" 
                         alt="<?php echo htmlspecialchars($content['title']); ?>">
                </div>
                <?php endif; ?>
                
                <div class="content-details">
                    <h1 class="content-title"><?php echo htmlspecialchars($videoTitle); ?></h1>
                    
                    <div class="content-meta">
                        <span class="meta-item">
                            <?php echo $content['type'] === 'movie' ? 'فيلم' : 'مسلسل'; ?>
                        </span>
                        
                        <?php if ($content['release_date']): ?>
                        <span class="meta-item">
                            <?php echo date('Y', strtotime($content['release_date'])); ?>
                        </span>
                        <?php endif; ?>
                        
                        <?php if ($content['duration']): ?>
                        <span class="meta-item">
                            <?php echo formatDuration($content['duration']); ?>
                        </span>
                        <?php endif; ?>
                        
                        <?php if ($content['our_rating']): ?>
                        <span class="meta-item">
                            <i class="fas fa-star"></i>
                            <?php echo $content['our_rating']; ?>/10
                        </span>
                        <?php endif; ?>
                        
                        <?php if ($content['genre']): ?>
                        <span class="meta-item">
                            <?php echo htmlspecialchars($content['genre']); ?>
                        </span>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($content['description']): ?>
                    <p class="content-description">
                        <?php echo htmlspecialchars($content['description']); ?>
                    </p>
                    <?php endif; ?>
                    
                    <div class="content-actions">
                        <?php if (IS_LOGGED_IN): ?>
                        <button class="btn btn-secondary" onclick="toggleFavorite(<?php echo $contentId; ?>)">
                            <i class="fas fa-heart"></i>
                            إضافة للمفضلة
                        </button>
                        
                        <button class="btn btn-secondary" onclick="toggleWatchlist(<?php echo $contentId; ?>)">
                            <i class="fas fa-bookmark"></i>
                            إضافة لقائمة المشاهدة
                        </button>
                        <?php endif; ?>
                        
                        <button class="btn btn-secondary" onclick="shareContent()">
                            <i class="fas fa-share"></i>
                            مشاركة
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- قائمة الحلقات للمسلسلات -->
            <?php if ($content['type'] === 'series' && !empty($seasons)): ?>
            <div class="episodes-section">
                <h2 class="section-title">الحلقات</h2>
                
                <?php if (count($seasons) > 1): ?>
                <div class="seasons-tabs">
                    <?php foreach ($seasons as $index => $season): ?>
                    <button class="season-tab <?php echo $index === 0 ? 'active' : ''; ?>" 
                            onclick="showSeason(<?php echo $season['id']; ?>, this)">
                        الموسم <?php echo $season['season_number']; ?>
                    </button>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
                
                <?php foreach ($seasons as $index => $season): ?>
                <div class="episodes-grid season-episodes" 
                     data-season="<?php echo $season['id']; ?>" 
                     style="<?php echo $index === 0 ? '' : 'display: none;'; ?>">
                    
                    <?php if (isset($episodes[$season['id']])): ?>
                        <?php foreach ($episodes[$season['id']] as $ep): ?>
                        <div class="episode-card <?php echo $ep['id'] == $episodeId ? 'current' : ''; ?>" 
                             onclick="playEpisode(<?php echo $ep['id']; ?>)">
                            <div class="episode-thumbnail">
                                <?php if ($ep['thumbnail']): ?>
                                <img src="<?php echo htmlspecialchars($ep['thumbnail']); ?>" 
                                     alt="الحلقة <?php echo $ep['episode_number']; ?>">
                                <?php else: ?>
                                <i class="fas fa-play" style="font-size: 2rem; color: rgba(255,255,255,0.7);"></i>
                                <?php endif; ?>
                                <span class="episode-number">الحلقة <?php echo $ep['episode_number']; ?></span>
                            </div>
                            <div class="episode-info">
                                <h3 class="episode-title">
                                    <?php echo htmlspecialchars($ep['title'] ?: 'الحلقة ' . $ep['episode_number']); ?>
                                </h3>
                                <?php if ($ep['description']): ?>
                                <p class="episode-description">
                                    <?php echo htmlspecialchars(substr($ep['description'], 0, 100)); ?>...
                                </p>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>
    <script src="/assets/js/video-player.js"></script>
    
    <script>
        // بيانات الفيديو
        const videoData = {
            id: <?php echo $contentId; ?>,
            url: '<?php echo htmlspecialchars($videoToPlay['video_url'] ?? ''); ?>',
            type: 'video/mp4',
            poster: '<?php echo htmlspecialchars($content['poster'] ?? ''); ?>',
            title: '<?php echo htmlspecialchars($videoTitle); ?>'
        };
        
        // الحلقة التالية
        const nextEpisode = <?php echo $nextEpisode ? json_encode([
            'id' => $nextEpisode['id'],
            'title' => $nextEpisode['title'] ?: 'الحلقة ' . $nextEpisode['episode_number'],
            'url' => "/player.php?id=$contentId&episode=" . $nextEpisode['id']
        ]) : 'null'; ?>;
        
        // تهيئة المشغل
        const player = new StreamingVideoPlayer('videoPlayer', {
            autoplay: false,
            nextEpisode: nextEpisode
        });
        
        // تحميل الفيديو
        player.loadVideo(videoData);
        
        // دوال التفاعل
        function showSeason(seasonId, button) {
            // إخفاء جميع المواسم
            document.querySelectorAll('.season-episodes').forEach(el => {
                el.style.display = 'none';
            });
            
            // إظهار الموسم المحدد
            document.querySelector(`[data-season="${seasonId}"]`).style.display = 'grid';
            
            // تحديث التبويبات
            document.querySelectorAll('.season-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            button.classList.add('active');
        }
        
        function playEpisode(episodeId) {
            window.location.href = `/player.php?id=<?php echo $contentId; ?>&episode=${episodeId}`;
        }
        
        function toggleFavorite(contentId) {
            // إضافة/إزالة من المفضلة
            fetch('/api/favorites', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ content_id: contentId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                }
            });
        }
        
        function toggleWatchlist(contentId) {
            // إضافة/إزالة من قائمة المشاهدة
            fetch('/api/watchlist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ content_id: contentId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                }
            });
        }
        
        function shareContent() {
            if (navigator.share) {
                navigator.share({
                    title: '<?php echo htmlspecialchars($videoTitle); ?>',
                    text: '<?php echo htmlspecialchars($content['description'] ?? ''); ?>',
                    url: window.location.href
                });
            } else {
                // نسخ الرابط
                navigator.clipboard.writeText(window.location.href);
                alert('تم نسخ الرابط');
            }
        }
        
        // تتبع الأحداث
        function trackEvent(event, data) {
            fetch('/api/analytics', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    event: event,
                    data: data,
                    timestamp: new Date().toISOString()
                })
            });
        }
        
        console.log('🎬 صفحة مشغل الفيديو جاهزة');
    </script>
</body>
</html>
