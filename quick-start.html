<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 منصة البث - عرض توضيحي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo {
            font-size: 3rem;
            margin-bottom: 10px;
        }

        .title {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-title {
            font-size: 1.8rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .feature-title {
            font-size: 1.2rem;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .feature-desc {
            opacity: 0.9;
            line-height: 1.6;
        }

        .video-player {
            background: #000;
            border-radius: 15px;
            aspect-ratio: 16/9;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .play-button {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .play-button:hover {
            background: white;
            transform: scale(1.1);
        }

        .play-icon {
            font-size: 2rem;
            color: #667eea;
            margin-right: -5px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .content-card:hover {
            transform: scale(1.05);
        }

        .content-poster {
            aspect-ratio: 2/3;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
        }

        .content-info {
            padding: 15px;
        }

        .content-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .content-meta {
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .setup-instructions {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
        }

        .step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 15px;
            flex-shrink: 0;
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            border-right: 4px solid #667eea;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .alert {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.5);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }

        .alert-icon {
            font-size: 1.2rem;
            margin-left: 10px;
        }

        @media (max-width: 768px) {
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .content-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
            
            .title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🎬</div>
            <h1 class="title">منصة البث الشاملة</h1>
            <p class="subtitle">عرض توضيحي للمنصة - Streaming Platform Demo</p>
        </div>

        <!-- Features Section -->
        <div class="demo-section">
            <h2 class="section-title">
                <span>✨</span>
                الميزات الرئيسية
            </h2>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🌐</div>
                    <div class="feature-title">موقع ويب متطور</div>
                    <div class="feature-desc">واجهة مستخدم حديثة مع تصميم متجاوب ومشغل فيديو احترافي</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <div class="feature-title">تطبيق جوال</div>
                    <div class="feature-desc">تطبيق Flutter متكامل مع جميع الميزات وواجهة Material Design</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔐</div>
                    <div class="feature-title">نظام أمان متقدم</div>
                    <div class="feature-desc">حماية شاملة مع تشفير البيانات وتتبع الجلسات</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔍</div>
                    <div class="feature-title">بحث ذكي</div>
                    <div class="feature-desc">بحث متقدم مع فلاتر متعددة ونتائج فورية</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⭐</div>
                    <div class="feature-title">تقييمات ومراجعات</div>
                    <div class="feature-desc">نظام تقييم شامل مع مراجعات المستخدمين</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <div class="feature-title">تحليلات متقدمة</div>
                    <div class="feature-desc">إحصائيات مفصلة وتتبع سلوك المستخدمين</div>
                </div>
            </div>
        </div>

        <!-- Video Player Demo -->
        <div class="demo-section">
            <h2 class="section-title">
                <span>🎬</span>
                مشغل الفيديو
            </h2>
            
            <div class="video-player" onclick="playDemo()">
                <div class="play-button">
                    <div class="play-icon">▶</div>
                </div>
            </div>
            
            <p style="text-align: center; opacity: 0.8;">
                مشغل فيديو احترافي مع عناصر تحكم كاملة ودعم جودات متعددة
            </p>
        </div>

        <!-- Content Demo -->
        <div class="demo-section">
            <h2 class="section-title">
                <span>🎭</span>
                المحتوى المتاح
            </h2>
            
            <div class="content-grid">
                <div class="content-card" onclick="showContent('movie1')">
                    <div class="content-poster">🎬</div>
                    <div class="content-info">
                        <div class="content-title">فيلم الأكشن</div>
                        <div class="content-meta">2024 • أكشن • 120 دقيقة</div>
                    </div>
                </div>
                
                <div class="content-card" onclick="showContent('series1')">
                    <div class="content-poster">📺</div>
                    <div class="content-info">
                        <div class="content-title">مسلسل الدراما</div>
                        <div class="content-meta">2024 • دراما • 10 حلقات</div>
                    </div>
                </div>
                
                <div class="content-card" onclick="showContent('movie2')">
                    <div class="content-poster">🎭</div>
                    <div class="content-info">
                        <div class="content-title">فيلم الكوميديا</div>
                        <div class="content-meta">2024 • كوميديا • 95 دقيقة</div>
                    </div>
                </div>
                
                <div class="content-card" onclick="showContent('doc1')">
                    <div class="content-poster">🌍</div>
                    <div class="content-info">
                        <div class="content-title">وثائقي الطبيعة</div>
                        <div class="content-meta">2024 • وثائقي • 60 دقيقة</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Setup Instructions -->
        <div class="setup-instructions">
            <h2 class="section-title">
                <span>🚀</span>
                خطوات التشغيل الكامل
            </h2>
            
            <div class="alert">
                <span class="alert-icon">⚠️</span>
                <strong>هذا عرض توضيحي فقط.</strong> لتشغيل المنصة الكاملة، اتبع الخطوات التالية:
            </div>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <div class="step-title">تثبيت XAMPP</div>
                    <div>حمل وثبت XAMPP من الموقع الرسمي لتشغيل PHP و MySQL</div>
                    <div class="code-block">https://www.apachefriends.org/download.html</div>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <div class="step-title">تشغيل الخدمات</div>
                    <div>افتح XAMPP Control Panel وشغل Apache و MySQL</div>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <div class="step-title">إعداد قاعدة البيانات</div>
                    <div>افتح phpMyAdmin وأنشئ قاعدة بيانات جديدة</div>
                    <div class="code-block">CREATE DATABASE streaming_platform;</div>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">4</div>
                <div class="step-content">
                    <div class="step-title">نسخ الملفات</div>
                    <div>انسخ ملفات المشروع إلى مجلد htdocs في XAMPP</div>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">5</div>
                <div class="step-content">
                    <div class="step-title">الوصول للمنصة</div>
                    <div>افتح المتصفح واذهب إلى localhost/streaming-platform</div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="#" class="btn" onclick="showFullInstructions()">
                    📖 عرض التعليمات الكاملة
                </a>
            </div>
        </div>
    </div>

    <script>
        function playDemo() {
            alert('🎬 هذا عرض توضيحي!\n\nفي المنصة الكاملة ستجد:\n• مشغل فيديو احترافي\n• دعم جودات متعددة\n• ترجمات\n• تحكم كامل في التشغيل');
        }

        function showContent(contentId) {
            const content = {
                'movie1': 'فيلم أكشن مثير مليء بالمغامرات والإثارة',
                'series1': 'مسلسل درامي يحكي قصة شيقة عبر 10 حلقات',
                'movie2': 'كوميديا خفيفة مناسبة لجميع أفراد العائلة',
                'doc1': 'وثائقي رائع عن عجائب الطبيعة حول العالم'
            };
            
            alert(`📺 ${content[contentId]}\n\nفي المنصة الكاملة ستجد:\n• تفاصيل شاملة\n• تقييمات المستخدمين\n• مقاطع دعائية\n• إمكانية المشاهدة`);
        }

        function showFullInstructions() {
            window.open('README.md', '_blank');
        }

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الظهور التدريجي
            const cards = document.querySelectorAll('.feature-card, .content-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
