/**
 * 🚀 ملف JavaScript الرئيسي لمنصة البث الشاملة
 * يحتوي على جميع الوظائف الأساسية والتفاعلات
 */

// ==========================================
// 🔧 الإعدادات العامة
// ==========================================
const StreamingPlatform = {
    // إعدادات CSRF
    csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
    
    // إعدادات API
    apiUrl: '/website/api',
    
    // إعدادات التحميل
    loadingSpinner: document.getElementById('loading-spinner'),
    
    // مهلة الطلبات
    requestTimeout: 30000,
    
    // تهيئة التطبيق
    init() {
        this.setupEventListeners();
        this.setupAjaxDefaults();
        this.setupFormValidation();
        this.setupLazyLoading();
        this.hideLoadingSpinner();
    },
    
    // ==========================================
    // 🎯 إعداد مستمعي الأحداث
    // ==========================================
    setupEventListeners() {
        // تسجيل الدخول
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', this.handleLogin.bind(this));
        }
        
        // التسجيل
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            registerForm.addEventListener('submit', this.handleRegister.bind(this));
        }
        
        // البحث
        const searchForm = document.querySelector('form[role="search"]');
        if (searchForm) {
            searchForm.addEventListener('submit', this.handleSearch.bind(this));
        }
        
        // بطاقات المحتوى
        document.querySelectorAll('.content-card').forEach(card => {
            card.addEventListener('click', this.handleContentClick.bind(this));
        });
        
        // أزرار المفضلة
        document.querySelectorAll('.favorite-btn').forEach(btn => {
            btn.addEventListener('click', this.handleFavoriteToggle.bind(this));
        });
        
        // أزرار قائمة المشاهدة
        document.querySelectorAll('.watchlist-btn').forEach(btn => {
            btn.addEventListener('click', this.handleWatchlistToggle.bind(this));
        });
        
        // التمرير اللانهائي
        window.addEventListener('scroll', this.handleInfiniteScroll.bind(this));
        
        // تغيير حجم النافذة
        window.addEventListener('resize', this.handleResize.bind(this));
    },
    
    // ==========================================
    // 🌐 إعداد AJAX الافتراضي
    // ==========================================
    setupAjaxDefaults() {
        // إضافة CSRF token لجميع طلبات AJAX
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            if (!options.headers) {
                options.headers = {};
            }
            
            // إضافة CSRF token للطلبات POST/PUT/DELETE
            if (options.method && ['POST', 'PUT', 'DELETE'].includes(options.method.toUpperCase())) {
                options.headers['X-CSRF-Token'] = StreamingPlatform.csrfToken;
            }
            
            // إضافة Content-Type للطلبات JSON
            if (options.body && typeof options.body === 'string') {
                options.headers['Content-Type'] = 'application/json';
            }
            
            return originalFetch(url, options);
        };
    },
    
    // ==========================================
    // 📝 التحقق من صحة النماذج
    // ==========================================
    setupFormValidation() {
        // التحقق من البريد الإلكتروني
        document.querySelectorAll('input[type="email"]').forEach(input => {
            input.addEventListener('blur', function() {
                StreamingPlatform.validateEmail(this);
            });
        });
        
        // التحقق من كلمة المرور
        document.querySelectorAll('input[type="password"]').forEach(input => {
            input.addEventListener('input', function() {
                if (this.name === 'password') {
                    StreamingPlatform.validatePassword(this);
                }
            });
        });
        
        // التحقق من تطابق كلمات المرور
        const confirmPasswordInput = document.querySelector('input[name="confirm_password"]');
        if (confirmPasswordInput) {
            confirmPasswordInput.addEventListener('input', function() {
                const passwordInput = document.querySelector('input[name="password"]');
                StreamingPlatform.validatePasswordMatch(passwordInput, this);
            });
        }
    },
    
    // ==========================================
    // 🖼️ التحميل الكسول للصور
    // ==========================================
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    },
    
    // ==========================================
    // 🔐 معالجة تسجيل الدخول
    // ==========================================
    async handleLogin(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        // إضافة CSRF token
        data.csrf_token = this.csrfToken;
        
        this.showLoadingSpinner();
        
        try {
            const response = await fetch('/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert('تم تسجيل الدخول بنجاح', 'success');
                setTimeout(() => {
                    window.location.href = result.data.redirect || '/';
                }, 1000);
            } else {
                this.showAlert(result.message, 'danger');
                this.displayFormErrors(form, result.errors);
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showAlert('حدث خطأ في تسجيل الدخول', 'danger');
        } finally {
            this.hideLoadingSpinner();
        }
    },
    
    // ==========================================
    // 📝 معالجة التسجيل
    // ==========================================
    async handleRegister(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        // إضافة CSRF token
        data.csrf_token = this.csrfToken;
        
        this.showLoadingSpinner();
        
        try {
            const response = await fetch('/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert('تم إنشاء الحساب بنجاح', 'success');
                setTimeout(() => {
                    window.location.href = result.data.redirect || '/login';
                }, 1000);
            } else {
                this.showAlert(result.message, 'danger');
                this.displayFormErrors(form, result.errors);
            }
        } catch (error) {
            console.error('Registration error:', error);
            this.showAlert('حدث خطأ في إنشاء الحساب', 'danger');
        } finally {
            this.hideLoadingSpinner();
        }
    },
    
    // ==========================================
    // 🔍 معالجة البحث
    // ==========================================
    handleSearch(event) {
        event.preventDefault();
        
        const form = event.target;
        const searchInput = form.querySelector('input[type="search"]');
        const query = searchInput.value.trim();
        
        if (query.length < 2) {
            this.showAlert('يجب أن يكون البحث أكثر من حرفين', 'warning');
            return;
        }
        
        // إعادة توجيه لصفحة البحث
        window.location.href = `/search?q=${encodeURIComponent(query)}`;
    },
    
    // ==========================================
    // 🎬 معالجة النقر على بطاقة المحتوى
    // ==========================================
    handleContentClick(event) {
        const card = event.currentTarget;
        const contentSlug = card.dataset.slug;
        
        if (contentSlug) {
            window.location.href = `/content/${contentSlug}`;
        }
    },
    
    // ==========================================
    // ❤️ معالجة تبديل المفضلة
    // ==========================================
    async handleFavoriteToggle(event) {
        event.preventDefault();
        event.stopPropagation();
        
        const btn = event.currentTarget;
        const contentId = btn.dataset.contentId;
        
        try {
            const response = await fetch(`${this.apiUrl}/favorites/toggle`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ content_id: contentId })
            });
            
            const result = await response.json();
            
            if (result.success) {
                const icon = btn.querySelector('i');
                if (result.data.is_favorite) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    btn.classList.add('active');
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    btn.classList.remove('active');
                }
                
                this.showAlert(result.message, 'success');
            } else {
                this.showAlert(result.message, 'danger');
            }
        } catch (error) {
            console.error('Favorite toggle error:', error);
            this.showAlert('حدث خطأ في تحديث المفضلة', 'danger');
        }
    },
    
    // ==========================================
    // 📋 معالجة تبديل قائمة المشاهدة
    // ==========================================
    async handleWatchlistToggle(event) {
        event.preventDefault();
        event.stopPropagation();
        
        const btn = event.currentTarget;
        const contentId = btn.dataset.contentId;
        
        try {
            const response = await fetch(`${this.apiUrl}/watchlist/toggle`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ content_id: contentId })
            });
            
            const result = await response.json();
            
            if (result.success) {
                const icon = btn.querySelector('i');
                if (result.data.is_in_watchlist) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    btn.classList.add('active');
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    btn.classList.remove('active');
                }
                
                this.showAlert(result.message, 'success');
            } else {
                this.showAlert(result.message, 'danger');
            }
        } catch (error) {
            console.error('Watchlist toggle error:', error);
            this.showAlert('حدث خطأ في تحديث قائمة المشاهدة', 'danger');
        }
    },
    
    // ==========================================
    // ♾️ التمرير اللانهائي
    // ==========================================
    handleInfiniteScroll() {
        if ((window.innerHeight + window.scrollY) >= document.body.offsetHeight - 1000) {
            // تحميل المزيد من المحتوى
            this.loadMoreContent();
        }
    },
    
    // ==========================================
    // 📱 معالجة تغيير حجم النافذة
    // ==========================================
    handleResize() {
        // تحديث تخطيط الشبكة أو أي عناصر أخرى حسب الحاجة
        this.updateLayout();
    },
    
    // ==========================================
    // 🔧 دوال مساعدة
    // ==========================================
    
    // عرض مؤشر التحميل
    showLoadingSpinner() {
        if (this.loadingSpinner) {
            this.loadingSpinner.classList.add('show');
        }
    },
    
    // إخفاء مؤشر التحميل
    hideLoadingSpinner() {
        if (this.loadingSpinner) {
            this.loadingSpinner.classList.remove('show');
        }
    },
    
    // عرض تنبيه
    showAlert(message, type = 'info') {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertContainer.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
        alertContainer.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertContainer);
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alertContainer.parentNode) {
                alertContainer.remove();
            }
        }, 5000);
    },
    
    // عرض أخطاء النموذج
    displayFormErrors(form, errors) {
        // إزالة الأخطاء السابقة
        form.querySelectorAll('.invalid-feedback').forEach(el => el.remove());
        form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        
        // عرض الأخطاء الجديدة
        if (errors) {
            Object.keys(errors).forEach(field => {
                const input = form.querySelector(`[name="${field}"]`);
                if (input) {
                    input.classList.add('is-invalid');
                    
                    const feedback = document.createElement('div');
                    feedback.className = 'invalid-feedback';
                    feedback.textContent = errors[field];
                    
                    input.parentNode.appendChild(feedback);
                }
            });
        }
    },
    
    // التحقق من صحة البريد الإلكتروني
    validateEmail(input) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const isValid = emailRegex.test(input.value);
        
        if (input.value && !isValid) {
            input.classList.add('is-invalid');
            this.showFieldError(input, 'البريد الإلكتروني غير صحيح');
        } else {
            input.classList.remove('is-invalid');
            this.hideFieldError(input);
        }
        
        return isValid;
    },
    
    // التحقق من قوة كلمة المرور
    validatePassword(input) {
        const password = input.value;
        const minLength = password.length >= 8;
        const hasUpper = /[A-Z]/.test(password);
        const hasLower = /[a-z]/.test(password);
        const hasNumber = /\d/.test(password);
        const hasSpecial = /[@$!%*?&]/.test(password);
        
        const isValid = minLength && hasUpper && hasLower && hasNumber && hasSpecial;
        
        if (password && !isValid) {
            input.classList.add('is-invalid');
            this.showFieldError(input, 'كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل مع حرف كبير وصغير ورقم ورمز خاص');
        } else {
            input.classList.remove('is-invalid');
            this.hideFieldError(input);
        }
        
        return isValid;
    },
    
    // التحقق من تطابق كلمات المرور
    validatePasswordMatch(passwordInput, confirmInput) {
        const isMatch = passwordInput.value === confirmInput.value;
        
        if (confirmInput.value && !isMatch) {
            confirmInput.classList.add('is-invalid');
            this.showFieldError(confirmInput, 'كلمات المرور غير متطابقة');
        } else {
            confirmInput.classList.remove('is-invalid');
            this.hideFieldError(confirmInput);
        }
        
        return isMatch;
    },
    
    // عرض خطأ الحقل
    showFieldError(input, message) {
        this.hideFieldError(input);
        
        const feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        feedback.textContent = message;
        
        input.parentNode.appendChild(feedback);
    },
    
    // إخفاء خطأ الحقل
    hideFieldError(input) {
        const feedback = input.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.remove();
        }
    },
    
    // تحميل المزيد من المحتوى
    async loadMoreContent() {
        // سيتم تطوير هذا لاحقاً
        console.log('Loading more content...');
    },
    
    // تحديث التخطيط
    updateLayout() {
        // سيتم تطوير هذا لاحقاً
        console.log('Updating layout...');
    }
};

// ==========================================
// 🚀 تهيئة التطبيق عند تحميل الصفحة
// ==========================================
document.addEventListener('DOMContentLoaded', function() {
    StreamingPlatform.init();
});

// تصدير الكائن للاستخدام العام
window.StreamingPlatform = StreamingPlatform;
