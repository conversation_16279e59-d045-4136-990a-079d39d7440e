#!/bin/bash

echo "========================================"
echo "🚀 منصة البث الشاملة - تشغيل المشروع"
echo "========================================"
echo

# التحقق من وجود Flutter
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter غير مثبت أو غير موجود في PATH"
    echo "يرجى تثبيت Flutter من: https://flutter.dev/docs/get-started/install"
    exit 1
fi

# عرض إصدار Flutter
echo "📱 إصدار Flutter:"
flutter --version

# التحقق من وجود الخادم المحلي
echo
echo "🔍 التحقق من الخادم المحلي..."
if ! curl -s http://localhost/streaming_platform > /dev/null 2>&1; then
    echo "⚠️  الخادم المحلي غير متاح"
    echo "يرجى تشغيل خادم ويب (Apache/Nginx) مع PHP و MySQL"
    echo
    echo "📝 خطوات تشغيل الخادم:"
    echo "1. تشغيل Apache و MySQL"
    echo "2. نسخ مجلد website إلى مجلد الخادم"
    echo "3. إنشاء قاعدة البيانات من ملف schema.sql"
    echo
    read -p "هل تريد المتابعة بدون الخادم؟ (y/n): " choice
    if [[ "$choice" != "y" && "$choice" != "Y" ]]; then
        exit 1
    fi
fi

echo
echo "📱 تشغيل التطبيق الجوال..."
echo "========================================"

# الانتقال لمجلد التطبيق
cd mobile_app

# تثبيت التبعيات
echo "📦 تثبيت التبعيات..."
flutter pub get

if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت التبعيات"
    exit 1
fi

# تنظيف المشروع
echo "🧹 تنظيف المشروع..."
flutter clean
flutter pub get

# التحقق من الأجهزة المتاحة
echo "📱 البحث عن الأجهزة المتاحة..."
flutter devices

echo
echo "🚀 تشغيل التطبيق..."
echo "========================================"

# تشغيل التطبيق
flutter run

echo
echo "✅ انتهى تشغيل التطبيق"
