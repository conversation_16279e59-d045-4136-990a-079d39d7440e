# 🐳 Dockerfile لمنصة البث العربية
# صورة Docker محسنة للإنتاج

FROM php:8.1-apache

# معلومات الصورة
LABEL maintainer="<EMAIL>"
LABEL description="منصة البث العربية الشاملة"
LABEL version="1.0.0"

# تثبيت التبعيات النظام
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg62-turbo-dev \
    libfreetype6-dev \
    libzip-dev \
    libicu-dev \
    libonig-dev \
    libxml2-dev \
    libcurl4-openssl-dev \
    libssl-dev \
    mariadb-client \
    unzip \
    git \
    curl \
    nano \
    htop \
    && rm -rf /var/lib/apt/lists/*

# تثبيت امتدادات PHP
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
    gd \
    pdo \
    pdo_mysql \
    mysqli \
    zip \
    intl \
    mbstring \
    xml \
    curl \
    json \
    opcache \
    bcmath \
    exif

# تثبيت Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# إعدادات PHP محسنة
RUN { \
    echo 'memory_limit = 256M'; \
    echo 'upload_max_filesize = 100M'; \
    echo 'post_max_size = 100M'; \
    echo 'max_execution_time = 300'; \
    echo 'max_input_time = 300'; \
    echo 'max_file_uploads = 20'; \
    echo 'date.timezone = Asia/Riyadh'; \
    echo 'expose_php = Off'; \
    echo 'display_errors = Off'; \
    echo 'log_errors = On'; \
    echo 'error_log = /var/log/php_errors.log'; \
} > /usr/local/etc/php/conf.d/streaming-platform.ini

# إعدادات OPcache
RUN { \
    echo 'opcache.enable=1'; \
    echo 'opcache.memory_consumption=128'; \
    echo 'opcache.interned_strings_buffer=8'; \
    echo 'opcache.max_accelerated_files=4000'; \
    echo 'opcache.revalidate_freq=2'; \
    echo 'opcache.fast_shutdown=1'; \
    echo 'opcache.enable_cli=1'; \
} > /usr/local/etc/php/conf.d/opcache.ini

# تفعيل mod_rewrite و mod_headers
RUN a2enmod rewrite headers ssl

# إعدادات Apache
RUN { \
    echo 'ServerTokens Prod'; \
    echo 'ServerSignature Off'; \
    echo 'TraceEnable Off'; \
    echo 'Header always set X-Content-Type-Options nosniff'; \
    echo 'Header always set X-Frame-Options DENY'; \
    echo 'Header always set X-XSS-Protection "1; mode=block"'; \
} >> /etc/apache2/conf-available/security.conf

RUN a2enconf security

# إنشاء مستخدم للتطبيق
RUN groupadd -r streaming && useradd -r -g streaming streaming

# إعداد مجلد العمل
WORKDIR /var/www/html

# نسخ ملفات التطبيق
COPY website/ .

# تثبيت تبعيات Composer
RUN composer install --no-dev --optimize-autoloader --no-interaction

# إنشاء المجلدات المطلوبة
RUN mkdir -p logs cache uploads/posters uploads/thumbnails uploads/videos \
    && chown -R streaming:streaming logs cache uploads \
    && chmod -R 755 logs cache uploads

# نسخ إعدادات Apache المخصصة
COPY docker/apache-vhost.conf /etc/apache2/sites-available/000-default.conf

# إعداد الصلاحيات
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html \
    && chmod -R 777 logs cache uploads

# إنشاء ملف إعدادات من المثال
RUN cp includes/config.example.php includes/config.php

# متغيرات البيئة
ENV APACHE_DOCUMENT_ROOT=/var/www/html/public
ENV APACHE_LOG_DIR=/var/log/apache2

# تحديث إعدادات Apache
RUN sed -ri -e 's!/var/www/html!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/sites-available/*.conf
RUN sed -ri -e 's!/var/www/!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/apache2.conf /etc/apache2/conf-available/*.conf

# فتح المنافذ
EXPOSE 80 443

# إنشاء نقطة دخول مخصصة
COPY docker/entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/entrypoint.sh

# فحص صحة الحاوية
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/test.php || exit 1

# نقطة الدخول
ENTRYPOINT ["entrypoint.sh"]
CMD ["apache2-foreground"]
