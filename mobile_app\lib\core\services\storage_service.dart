import 'dart:convert';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';

/// 💾 خدمة التخزين المحلي
/// تدير جميع عمليات التخزين والاسترجاع للبيانات المحلية
class StorageService {
  static StorageService? _instance;
  static StorageService get instance => _instance ??= StorageService._();
  
  StorageService._();

  late Box _userBox;
  late Box _settingsBox;
  late Box _cacheBox;
  late Box _downloadBox;
  late SharedPreferences _prefs;

  /// تهيئة خدمة التخزين
  static Future<void> init() async {
    final service = StorageService.instance;
    
    // تهيئة SharedPreferences
    service._prefs = await SharedPreferences.getInstance();
    
    // تهيئة Hive boxes
    service._userBox = await Hive.openBox(AppConstants.userBoxName);
    service._settingsBox = await Hive.openBox(AppConstants.settingsBoxName);
    service._cacheBox = await Hive.openBox(AppConstants.cacheBoxName);
    service._downloadBox = await Hive.openBox(AppConstants.downloadBoxName);
    
    print('✅ تم تهيئة خدمة التخزين بنجاح');
  }

  // ==========================================
  // 👤 إدارة بيانات المستخدم
  // ==========================================

  /// حفظ رمز المصادقة
  Future<void> saveUserToken(String token) async {
    await _userBox.put(AppConstants.userTokenKey, token);
  }

  /// الحصول على رمز المصادقة
  String? getUserToken() {
    return _userBox.get(AppConstants.userTokenKey);
  }

  /// حذف رمز المصادقة
  Future<void> deleteUserToken() async {
    await _userBox.delete(AppConstants.userTokenKey);
  }

  /// حفظ بيانات المستخدم
  Future<void> saveUserData(Map<String, dynamic> userData) async {
    await _userBox.put(AppConstants.userDataKey, jsonEncode(userData));
  }

  /// الحصول على بيانات المستخدم
  Map<String, dynamic>? getUserData() {
    final data = _userBox.get(AppConstants.userDataKey);
    if (data != null) {
      return jsonDecode(data);
    }
    return null;
  }

  /// حذف بيانات المستخدم
  Future<void> deleteUserData() async {
    await _userBox.delete(AppConstants.userDataKey);
  }

  /// التحقق من تسجيل الدخول
  bool isLoggedIn() {
    return getUserToken() != null;
  }

  /// تسجيل الخروج (حذف جميع بيانات المستخدم)
  Future<void> logout() async {
    await _userBox.clear();
  }

  // ==========================================
  // ⚙️ إدارة الإعدادات
  // ==========================================

  /// حفظ وضع السمة
  Future<void> saveThemeMode(String themeMode) async {
    await _settingsBox.put(AppConstants.themeKey, themeMode);
  }

  /// الحصول على وضع السمة
  String getThemeMode() {
    return _settingsBox.get(AppConstants.themeKey, defaultValue: 'dark');
  }

  /// حفظ اللغة
  Future<void> saveLanguage(String language) async {
    await _settingsBox.put(AppConstants.languageKey, language);
  }

  /// الحصول على اللغة
  String getLanguage() {
    return _settingsBox.get(AppConstants.languageKey, defaultValue: 'ar');
  }

  /// حفظ حالة التشغيل الأول
  Future<void> setFirstLaunch(bool isFirst) async {
    await _settingsBox.put(AppConstants.firstLaunchKey, isFirst);
  }

  /// التحقق من التشغيل الأول
  bool isFirstLaunch() {
    return _settingsBox.get(AppConstants.firstLaunchKey, defaultValue: true);
  }

  /// حفظ حالة تفعيل البصمة
  Future<void> setBiometricEnabled(bool enabled) async {
    await _settingsBox.put(AppConstants.biometricEnabledKey, enabled);
  }

  /// التحقق من تفعيل البصمة
  bool isBiometricEnabled() {
    return _settingsBox.get(AppConstants.biometricEnabledKey, defaultValue: false);
  }

  /// حفظ جودة الفيديو المفضلة
  Future<void> savePreferredVideoQuality(String quality) async {
    await _settingsBox.put('preferred_video_quality', quality);
  }

  /// الحصول على جودة الفيديو المفضلة
  String getPreferredVideoQuality() {
    return _settingsBox.get('preferred_video_quality', 
        defaultValue: AppConstants.defaultVideoQuality);
  }

  /// حفظ سرعة التشغيل المفضلة
  Future<void> savePreferredPlaybackSpeed(double speed) async {
    await _settingsBox.put('preferred_playback_speed', speed);
  }

  /// الحصول على سرعة التشغيل المفضلة
  double getPreferredPlaybackSpeed() {
    return _settingsBox.get('preferred_playback_speed', 
        defaultValue: AppConstants.defaultPlaybackSpeed);
  }

  /// حفظ حالة التشغيل التلقائي
  Future<void> setAutoPlayEnabled(bool enabled) async {
    await _settingsBox.put('auto_play_enabled', enabled);
  }

  /// التحقق من تفعيل التشغيل التلقائي
  bool isAutoPlayEnabled() {
    return _settingsBox.get('auto_play_enabled', 
        defaultValue: AppConstants.autoPlayEnabled);
  }

  /// حفظ حالة تفعيل الترجمة
  Future<void> setSubtitlesEnabled(bool enabled) async {
    await _settingsBox.put('subtitles_enabled', enabled);
  }

  /// التحقق من تفعيل الترجمة
  bool isSubtitlesEnabled() {
    return _settingsBox.get('subtitles_enabled', 
        defaultValue: AppConstants.subtitlesEnabled);
  }

  // ==========================================
  // 💾 إدارة التخزين المؤقت
  // ==========================================

  /// حفظ بيانات في التخزين المؤقت
  Future<void> cacheData(String key, dynamic data, {Duration? expiration}) async {
    final cacheItem = {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'expiration': expiration?.inMilliseconds,
    };
    await _cacheBox.put(key, jsonEncode(cacheItem));
  }

  /// الحصول على بيانات من التخزين المؤقت
  T? getCachedData<T>(String key) {
    final cachedItem = _cacheBox.get(key);
    if (cachedItem == null) return null;

    try {
      final item = jsonDecode(cachedItem);
      final timestamp = item['timestamp'] as int;
      final expiration = item['expiration'] as int?;

      // التحقق من انتهاء الصلاحية
      if (expiration != null) {
        final expirationTime = timestamp + expiration;
        if (DateTime.now().millisecondsSinceEpoch > expirationTime) {
          _cacheBox.delete(key);
          return null;
        }
      }

      return item['data'] as T;
    } catch (e) {
      print('خطأ في قراءة البيانات المؤقتة: $e');
      return null;
    }
  }

  /// حذف بيانات من التخزين المؤقت
  Future<void> deleteCachedData(String key) async {
    await _cacheBox.delete(key);
  }

  /// مسح جميع البيانات المؤقتة
  Future<void> clearCache() async {
    await _cacheBox.clear();
  }

  /// الحصول على حجم التخزين المؤقت
  int getCacheSize() {
    return _cacheBox.length;
  }

  // ==========================================
  // 📥 إدارة التحميلات
  // ==========================================

  /// حفظ معلومات التحميل
  Future<void> saveDownloadInfo(String contentId, Map<String, dynamic> info) async {
    await _downloadBox.put(contentId, jsonEncode(info));
  }

  /// الحصول على معلومات التحميل
  Map<String, dynamic>? getDownloadInfo(String contentId) {
    final data = _downloadBox.get(contentId);
    if (data != null) {
      return jsonDecode(data);
    }
    return null;
  }

  /// حذف معلومات التحميل
  Future<void> deleteDownloadInfo(String contentId) async {
    await _downloadBox.delete(contentId);
  }

  /// الحصول على جميع التحميلات
  List<Map<String, dynamic>> getAllDownloads() {
    final downloads = <Map<String, dynamic>>[];
    for (final key in _downloadBox.keys) {
      final data = _downloadBox.get(key);
      if (data != null) {
        try {
          downloads.add(jsonDecode(data));
        } catch (e) {
          print('خطأ في قراءة بيانات التحميل: $e');
        }
      }
    }
    return downloads;
  }

  /// مسح جميع التحميلات
  Future<void> clearDownloads() async {
    await _downloadBox.clear();
  }

  // ==========================================
  // 🔍 إدارة تاريخ البحث
  // ==========================================

  /// حفظ عنصر في تاريخ البحث
  Future<void> addToSearchHistory(String query) async {
    final history = getSearchHistory();
    
    // إزالة العنصر إذا كان موجوداً مسبقاً
    history.remove(query);
    
    // إضافة العنصر في المقدمة
    history.insert(0, query);
    
    // الاحتفاظ بحد أقصى من العناصر
    if (history.length > AppConstants.maxSearchHistory) {
      history.removeRange(AppConstants.maxSearchHistory, history.length);
    }
    
    await _settingsBox.put('search_history', jsonEncode(history));
  }

  /// الحصول على تاريخ البحث
  List<String> getSearchHistory() {
    final data = _settingsBox.get('search_history');
    if (data != null) {
      try {
        final List<dynamic> history = jsonDecode(data);
        return history.cast<String>();
      } catch (e) {
        print('خطأ في قراءة تاريخ البحث: $e');
      }
    }
    return [];
  }

  /// مسح تاريخ البحث
  Future<void> clearSearchHistory() async {
    await _settingsBox.delete('search_history');
  }

  // ==========================================
  // 📺 إدارة المشاهدة الأخيرة
  // ==========================================

  /// حفظ عنصر في المشاهدة الأخيرة
  Future<void> addToRecentlyWatched(Map<String, dynamic> content) async {
    final recent = getRecentlyWatched();
    
    // إزالة العنصر إذا كان موجوداً مسبقاً
    recent.removeWhere((item) => item['id'] == content['id']);
    
    // إضافة العنصر في المقدمة
    recent.insert(0, content);
    
    // الاحتفاظ بحد أقصى من العناصر
    if (recent.length > AppConstants.maxRecentlyWatched) {
      recent.removeRange(AppConstants.maxRecentlyWatched, recent.length);
    }
    
    await _settingsBox.put('recently_watched', jsonEncode(recent));
  }

  /// الحصول على المشاهدة الأخيرة
  List<Map<String, dynamic>> getRecentlyWatched() {
    final data = _settingsBox.get('recently_watched');
    if (data != null) {
      try {
        final List<dynamic> recent = jsonDecode(data);
        return recent.cast<Map<String, dynamic>>();
      } catch (e) {
        print('خطأ في قراءة المشاهدة الأخيرة: $e');
      }
    }
    return [];
  }

  /// مسح المشاهدة الأخيرة
  Future<void> clearRecentlyWatched() async {
    await _settingsBox.delete('recently_watched');
  }

  // ==========================================
  // 🧹 تنظيف البيانات
  // ==========================================

  /// تنظيف البيانات المنتهية الصلاحية
  Future<void> cleanExpiredData() async {
    final keysToDelete = <String>[];
    
    for (final key in _cacheBox.keys) {
      final cachedItem = _cacheBox.get(key);
      if (cachedItem != null) {
        try {
          final item = jsonDecode(cachedItem);
          final timestamp = item['timestamp'] as int;
          final expiration = item['expiration'] as int?;

          if (expiration != null) {
            final expirationTime = timestamp + expiration;
            if (DateTime.now().millisecondsSinceEpoch > expirationTime) {
              keysToDelete.add(key);
            }
          }
        } catch (e) {
          // حذف البيانات التالفة
          keysToDelete.add(key);
        }
      }
    }

    for (final key in keysToDelete) {
      await _cacheBox.delete(key);
    }

    print('تم تنظيف ${keysToDelete.length} عنصر منتهي الصلاحية');
  }

  /// مسح جميع البيانات
  Future<void> clearAllData() async {
    await _userBox.clear();
    await _settingsBox.clear();
    await _cacheBox.clear();
    await _downloadBox.clear();
    await _prefs.clear();
  }
}
