<?php
/**
 * 🔐 نظام الأمان والحماية لمنصة البث
 * يحتوي على جميع وظائف الأمان والحماية من الهجمات
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}

/**
 * ==========================================
 * 🛡️ فئة الأمان الرئيسية
 * ==========================================
 */
class Security {
    
    /**
     * تطبيق إعدادات الأمان الأساسية
     */
    public static function initialize() {
        // إعدادات الجلسة الآمنة
        self::secureSession();
        
        // إعدادات الأمان للرؤوس
        self::setSecurityHeaders();
        
        // تفعيل حماية CSRF
        self::initCSRFProtection();
        
        // تفعيل Rate Limiting
        if (RATE_LIMIT_ENABLED) {
            self::checkRateLimit();
        }
    }
    
    /**
     * إعدادات الجلسة الآمنة
     */
    private static function secureSession() {
        // إعدادات الجلسة الآمنة
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_samesite', 'Strict');
        
        // تعيين اسم الجلسة
        session_name('STREAMING_SESSION');
        
        // بدء الجلسة إذا لم تكن مبدوءة
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // تجديد معرف الجلسة دورياً
        if (!isset($_SESSION['last_regeneration'])) {
            $_SESSION['last_regeneration'] = time();
        } elseif (time() - $_SESSION['last_regeneration'] > 300) { // كل 5 دقائق
            session_regenerate_id(true);
            $_SESSION['last_regeneration'] = time();
        }
    }
    
    /**
     * تعيين رؤوس الأمان
     */
    private static function setSecurityHeaders() {
        // منع XSS
        header('X-XSS-Protection: 1; mode=block');
        
        // منع MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // منع Clickjacking
        header('X-Frame-Options: DENY');
        
        // Content Security Policy
        header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self'; media-src 'self' blob:;");
        
        // Strict Transport Security (HTTPS only)
        if (isset($_SERVER['HTTPS'])) {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        }
        
        // Referrer Policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Feature Policy
        header("Feature-Policy: camera 'none'; microphone 'none'; geolocation 'self';");
    }
    
    /**
     * تهيئة حماية CSRF
     */
    private static function initCSRFProtection() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
    }
    
    /**
     * فحص Rate Limiting
     */
    private static function checkRateLimit() {
        $ip = self::getClientIP();
        $key = 'rate_limit_' . md5($ip);
        
        // استخدام ملف مؤقت للتخزين (يمكن استخدام Redis لاحقاً)
        $cacheFile = CACHE_PATH . $key . '.cache';
        
        if (!is_dir(CACHE_PATH)) {
            mkdir(CACHE_PATH, 0755, true);
        }
        
        $currentTime = time();
        $windowStart = $currentTime - RATE_LIMIT_WINDOW;
        
        // قراءة البيانات المحفوظة
        $requests = [];
        if (file_exists($cacheFile)) {
            $data = file_get_contents($cacheFile);
            $requests = json_decode($data, true) ?: [];
        }
        
        // تنظيف الطلبات القديمة
        $requests = array_filter($requests, function($timestamp) use ($windowStart) {
            return $timestamp > $windowStart;
        });
        
        // فحص عدد الطلبات
        if (count($requests) >= RATE_LIMIT_REQUESTS) {
            http_response_code(429);
            header('Retry-After: ' . RATE_LIMIT_WINDOW);
            die(json_encode(['error' => 'Too many requests. Please try again later.']));
        }
        
        // إضافة الطلب الحالي
        $requests[] = $currentTime;
        
        // حفظ البيانات
        file_put_contents($cacheFile, json_encode($requests), LOCK_EX);
    }
    
    /**
     * الحصول على IP العميل الحقيقي
     */
    public static function getClientIP() {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * تسجيل محاولة تسجيل دخول فاشلة
     */
    public static function logFailedLogin($identifier) {
        $ip = self::getClientIP();
        $key = 'failed_login_' . md5($identifier . $ip);
        $cacheFile = CACHE_PATH . $key . '.cache';
        
        if (!is_dir(CACHE_PATH)) {
            mkdir(CACHE_PATH, 0755, true);
        }
        
        $attempts = 0;
        if (file_exists($cacheFile)) {
            $data = file_get_contents($cacheFile);
            $attempts = (int) $data;
        }
        
        $attempts++;
        file_put_contents($cacheFile, $attempts, LOCK_EX);
        
        // تسجيل في قاعدة البيانات
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("INSERT INTO activity_logs (action, description, ip_address, user_agent, request_data, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $stmt->execute([
                'failed_login',
                "Failed login attempt for: $identifier",
                $ip,
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                json_encode(['identifier' => $identifier, 'attempts' => $attempts])
            ]);
        } catch (Exception $e) {
            error_log("Failed to log failed login: " . $e->getMessage());
        }
        
        return $attempts;
    }
    
    /**
     * فحص ما إذا كان الحساب مقفل
     */
    public static function isAccountLocked($identifier) {
        $ip = self::getClientIP();
        $key = 'failed_login_' . md5($identifier . $ip);
        $cacheFile = CACHE_PATH . $key . '.cache';
        
        if (!file_exists($cacheFile)) {
            return false;
        }
        
        $attempts = (int) file_get_contents($cacheFile);
        return $attempts >= MAX_LOGIN_ATTEMPTS;
    }
    
    /**
     * إعادة تعيين محاولات تسجيل الدخول
     */
    public static function resetLoginAttempts($identifier) {
        $ip = self::getClientIP();
        $key = 'failed_login_' . md5($identifier . $ip);
        $cacheFile = CACHE_PATH . $key . '.cache';
        
        if (file_exists($cacheFile)) {
            unlink($cacheFile);
        }
    }
    
    /**
     * تشفير البيانات الحساسة
     */
    public static function encrypt($data) {
        $key = ENCRYPTION_KEY;
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * فك تشفير البيانات
     */
    public static function decrypt($encryptedData) {
        $key = ENCRYPTION_KEY;
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }
    
    /**
     * تنظيف البيانات من XSS
     */
    public static function cleanXSS($data) {
        if (is_array($data)) {
            return array_map([self::class, 'cleanXSS'], $data);
        }
        
        // إزالة الأحرف الخطيرة
        $data = str_replace(['<script', '</script>', 'javascript:', 'vbscript:', 'onload=', 'onerror='], '', $data);
        
        // تنظيف HTML
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        
        return $data;
    }
    
    /**
     * التحقق من SQL Injection
     */
    public static function detectSQLInjection($data) {
        $patterns = [
            '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b)/i',
            '/(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/i',
            '/[\'";]/',
            '/--/',
            '/\/\*.*\*\//'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $data)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * تسجيل نشاط مشبوه
     */
    public static function logSuspiciousActivity($type, $description, $data = []) {
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("INSERT INTO activity_logs (action, description, ip_address, user_agent, request_data, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $stmt->execute([
                'suspicious_' . $type,
                $description,
                self::getClientIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                json_encode($data)
            ]);
        } catch (Exception $e) {
            error_log("Failed to log suspicious activity: " . $e->getMessage());
        }
    }
    
    /**
     * فحص الملفات المرفوعة
     */
    public static function validateUploadedFile($file, $allowedTypes, $maxSize) {
        // فحص وجود الملف
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            return ['valid' => false, 'error' => 'No file uploaded'];
        }
        
        // فحص حجم الملف
        if ($file['size'] > $maxSize) {
            return ['valid' => false, 'error' => 'File size exceeds limit'];
        }
        
        // فحص نوع الملف
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedTypes)) {
            return ['valid' => false, 'error' => 'File type not allowed'];
        }
        
        // فحص MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'mp4' => 'video/mp4',
            'webm' => 'video/webm',
            'ogg' => 'video/ogg'
        ];
        
        if (isset($allowedMimes[$extension]) && $mimeType !== $allowedMimes[$extension]) {
            return ['valid' => false, 'error' => 'File type mismatch'];
        }
        
        return ['valid' => true];
    }
}

/**
 * ==========================================
 * 🔑 فئة JWT للمصادقة
 * ==========================================
 */
class JWT {
    
    /**
     * إنشاء JWT token
     */
    public static function encode($payload, $key = null) {
        $key = $key ?: JWT_SECRET_KEY;
        
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode($payload);
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $key, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }
    
    /**
     * فك تشفير JWT token
     */
    public static function decode($jwt, $key = null) {
        $key = $key ?: JWT_SECRET_KEY;
        
        $tokenParts = explode('.', $jwt);
        if (count($tokenParts) !== 3) {
            return false;
        }
        
        $header = base64_decode(str_replace(['-', '_'], ['+', '/'], $tokenParts[0]));
        $payload = base64_decode(str_replace(['-', '_'], ['+', '/'], $tokenParts[1]));
        $signatureProvided = $tokenParts[2];
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $key, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        if (!hash_equals($base64Signature, $signatureProvided)) {
            return false;
        }
        
        $payloadData = json_decode($payload, true);
        
        // فحص انتهاء الصلاحية
        if (isset($payloadData['exp']) && $payloadData['exp'] < time()) {
            return false;
        }
        
        return $payloadData;
    }
}

// تهيئة الأمان عند تحميل الملف
Security::initialize();

?>
