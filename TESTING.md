# 🧪 دليل الاختبارات - منصة البث العربية

## 🎯 **نظرة عامة**

هذا الدليل يوضح كيفية تشغيل وكتابة الاختبارات لمنصة البث العربية. نستخدم مجموعة شاملة من أدوات الاختبار لضمان جودة وموثوقية المنصة.

### **أنواع الاختبارات**
- **Unit Tests**: اختبارات الوحدة
- **Integration Tests**: اختبارات التكامل
- **Feature Tests**: اختبارات الميزات
- **Browser Tests**: اختبارات المتصفح
- **API Tests**: اختبارات API
- **Performance Tests**: اختبارات الأداء
- **Security Tests**: اختبارات الأمان

---

## 🚀 **تشغيل الاختبارات**

### **الأوامر السريعة**

```bash
# تشغيل جميع الاختبارات
make test

# تشغيل اختبارات محددة
make test-unit          # اختبارات الوحدة
make test-integration   # اختبارات التكامل
make test-feature       # اختبارات الميزات
make test-browser       # اختبارات المتصفح
make test-api          # اختبارات API
make test-performance  # اختبارات الأداء
make test-security     # اختبارات الأمان

# تشغيل مع تقرير التغطية
make test-coverage

# تشغيل في وضع المراقبة
make test-watch
```

### **تشغيل اختبارات محددة**

```bash
# PHP Tests
vendor/bin/phpunit tests/Unit/UserTest.php
vendor/bin/phpunit --filter testUserCanLogin

# JavaScript Tests
npm test -- --testNamePattern="User Login"
npm test -- tests/components/VideoPlayer.test.js

# End-to-End Tests
npm run test:e2e
npm run test:e2e -- --spec="login.spec.js"
```

---

## 🔧 **إعداد بيئة الاختبار**

### **متطلبات النظام**

```bash
# تثبيت التبعيات
composer install
npm install

# إعداد قاعدة بيانات الاختبار
cp .env.testing.example .env.testing
php artisan key:generate --env=testing
php artisan migrate --env=testing

# إعداد Selenium للاختبارات المتصفح
docker run -d -p 4444:4444 selenium/standalone-chrome
```

### **إعدادات البيئة**

```env
# .env.testing
APP_ENV=testing
DB_CONNECTION=sqlite
DB_DATABASE=:memory:
CACHE_DRIVER=array
SESSION_DRIVER=array
QUEUE_CONNECTION=sync
MAIL_MAILER=array
```

---

## 📝 **كتابة الاختبارات**

### **اختبارات الوحدة (Unit Tests)**

```php
<?php
// tests/Unit/UserServiceTest.php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\UserService;
use App\Models\User;

class UserServiceTest extends TestCase
{
    private UserService $userService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->userService = new UserService();
    }

    /** @test */
    public function it_can_create_user()
    {
        // Arrange
        $userData = [
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'password' => 'password123'
        ];

        // Act
        $user = $this->userService->createUser($userData);

        // Assert
        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('أحمد محمد', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
    }

    /** @test */
    public function it_validates_email_format()
    {
        // Arrange
        $userData = [
            'name' => 'أحمد محمد',
            'email' => 'invalid-email',
            'password' => 'password123'
        ];

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->userService->createUser($userData);
    }
}
```

### **اختبارات التكامل (Integration Tests)**

```php
<?php
// tests/Integration/MovieControllerTest.php

namespace Tests\Integration;

use Tests\TestCase;
use App\Models\User;
use App\Models\Movie;

class MovieControllerTest extends TestCase
{
    /** @test */
    public function authenticated_user_can_view_movies()
    {
        // Arrange
        $user = User::factory()->create();
        $movies = Movie::factory()->count(3)->create();

        // Act
        $response = $this->actingAs($user)
                        ->get('/api/movies');

        // Assert
        $response->assertStatus(200)
                ->assertJsonCount(3, 'data.movies')
                ->assertJsonStructure([
                    'data' => [
                        'movies' => [
                            '*' => ['id', 'title', 'description', 'poster']
                        ]
                    ]
                ]);
    }
}
```

### **اختبارات الميزات (Feature Tests)**

```php
<?php
// tests/Feature/UserRegistrationTest.php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UserRegistrationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function user_can_register_with_valid_data()
    {
        // Act
        $response = $this->post('/register', [
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123'
        ]);

        // Assert
        $response->assertRedirect('/dashboard');
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>'
        ]);
    }
}
```

### **اختبارات JavaScript**

```javascript
// tests/components/VideoPlayer.test.js

import { render, screen, fireEvent } from '@testing-library/react';
import VideoPlayer from '../../src/components/VideoPlayer';

describe('VideoPlayer Component', () => {
    test('renders video player with controls', () => {
        // Arrange
        const videoUrl = 'https://example.com/video.mp4';
        
        // Act
        render(<VideoPlayer src={videoUrl} />);
        
        // Assert
        expect(screen.getByRole('button', { name: /play/i })).toBeInTheDocument();
        expect(screen.getByRole('slider', { name: /volume/i })).toBeInTheDocument();
    });

    test('plays video when play button is clicked', () => {
        // Arrange
        const videoUrl = 'https://example.com/video.mp4';
        render(<VideoPlayer src={videoUrl} />);
        
        // Act
        const playButton = screen.getByRole('button', { name: /play/i });
        fireEvent.click(playButton);
        
        // Assert
        expect(screen.getByRole('button', { name: /pause/i })).toBeInTheDocument();
    });
});
```

### **اختبارات المتصفح (Browser Tests)**

```javascript
// tests/e2e/login.spec.js

const { test, expect } = require('@playwright/test');

test.describe('User Login', () => {
    test('user can login with valid credentials', async ({ page }) => {
        // Navigate to login page
        await page.goto('/login');
        
        // Fill login form
        await page.fill('[data-testid="email"]', '<EMAIL>');
        await page.fill('[data-testid="password"]', 'password123');
        
        // Submit form
        await page.click('[data-testid="login-button"]');
        
        // Assert redirect to dashboard
        await expect(page).toHaveURL('/dashboard');
        await expect(page.locator('[data-testid="user-name"]')).toContainText('أحمد محمد');
    });

    test('shows error for invalid credentials', async ({ page }) => {
        await page.goto('/login');
        
        await page.fill('[data-testid="email"]', '<EMAIL>');
        await page.fill('[data-testid="password"]', 'wrongpassword');
        await page.click('[data-testid="login-button"]');
        
        await expect(page.locator('[data-testid="error-message"]'))
            .toContainText('بيانات الدخول غير صحيحة');
    });
});
```

---

## 📊 **تقارير التغطية**

### **إنشاء تقرير التغطية**

```bash
# PHP Coverage
vendor/bin/phpunit --coverage-html coverage/php

# JavaScript Coverage
npm run test:coverage

# تقرير شامل
make coverage-report
```

### **عرض التقارير**

```bash
# فتح تقرير PHP
open coverage/php/index.html

# فتح تقرير JavaScript
open coverage/js/lcov-report/index.html
```

---

## 🔍 **اختبارات API**

### **اختبار نقاط النهاية**

```javascript
// tests/api/movies.test.js

const request = require('supertest');
const app = require('../../app');

describe('Movies API', () => {
    let authToken;

    beforeAll(async () => {
        // Get authentication token
        const response = await request(app)
            .post('/api/auth/login')
            .send({
                email: '<EMAIL>',
                password: 'password123'
            });
        
        authToken = response.body.data.token;
    });

    test('GET /api/movies returns list of movies', async () => {
        const response = await request(app)
            .get('/api/movies')
            .set('Authorization', `Bearer ${authToken}`)
            .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.movies).toBeInstanceOf(Array);
    });

    test('POST /api/movies creates new movie', async () => {
        const movieData = {
            title: 'فيلم اختبار',
            description: 'وصف الفيلم',
            duration: 120,
            year: 2024
        };

        const response = await request(app)
            .post('/api/movies')
            .set('Authorization', `Bearer ${authToken}`)
            .send(movieData)
            .expect(201);

        expect(response.body.data.movie.title).toBe('فيلم اختبار');
    });
});
```

---

## ⚡ **اختبارات الأداء**

### **اختبار تحميل الصفحات**

```javascript
// tests/performance/page-load.test.js

const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');

describe('Page Performance', () => {
    test('homepage loads within acceptable time', async () => {
        const chrome = await chromeLauncher.launch({chromeFlags: ['--headless']});
        
        const options = {
            logLevel: 'info',
            output: 'json',
            onlyCategories: ['performance'],
            port: chrome.port,
        };

        const runnerResult = await lighthouse('http://localhost:3000', options);
        
        const performanceScore = runnerResult.lhr.categories.performance.score * 100;
        
        expect(performanceScore).toBeGreaterThan(80);
        
        await chrome.kill();
    });
});
```

### **اختبار الحمولة**

```javascript
// tests/performance/load.test.js

import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
    stages: [
        { duration: '2m', target: 100 }, // ramp up
        { duration: '5m', target: 100 }, // stay at 100 users
        { duration: '2m', target: 200 }, // ramp up to 200 users
        { duration: '5m', target: 200 }, // stay at 200 users
        { duration: '2m', target: 0 },   // ramp down
    ],
};

export default function () {
    let response = http.get('https://api.streaming-platform.com/movies');
    
    check(response, {
        'status is 200': (r) => r.status === 200,
        'response time < 500ms': (r) => r.timings.duration < 500,
    });
    
    sleep(1);
}
```

---

## 🔒 **اختبارات الأمان**

### **اختبار الثغرات الأمنية**

```php
<?php
// tests/Security/SecurityTest.php

namespace Tests\Security;

use Tests\TestCase;

class SecurityTest extends TestCase
{
    /** @test */
    public function it_prevents_sql_injection()
    {
        $maliciousInput = "'; DROP TABLE users; --";
        
        $response = $this->get("/search?q=" . urlencode($maliciousInput));
        
        $response->assertStatus(200);
        $this->assertDatabaseHas('users', ['id' => 1]); // Table should still exist
    }

    /** @test */
    public function it_prevents_xss_attacks()
    {
        $maliciousScript = '<script>alert("XSS")</script>';
        
        $response = $this->post('/comments', [
            'content' => $maliciousScript
        ]);
        
        $response->assertStatus(422); // Should be rejected
    }

    /** @test */
    public function it_requires_authentication_for_protected_routes()
    {
        $response = $this->get('/admin/dashboard');
        
        $response->assertRedirect('/login');
    }
}
```

---

## 📱 **اختبارات التطبيق الجوال**

### **اختبارات Flutter**

```dart
// test/widget_test.dart

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:streaming_app/main.dart';

void main() {
  group('Login Widget Tests', () {
    testWidgets('Login form displays correctly', (WidgetTester tester) async {
      // Build our app and trigger a frame
      await tester.pumpWidget(MyApp());

      // Navigate to login page
      await tester.tap(find.byKey(Key('login_button')));
      await tester.pumpAndSettle();

      // Verify login form elements
      expect(find.byKey(Key('email_field')), findsOneWidget);
      expect(find.byKey(Key('password_field')), findsOneWidget);
      expect(find.byKey(Key('submit_button')), findsOneWidget);
    });

    testWidgets('Shows error for invalid login', (WidgetTester tester) async {
      await tester.pumpWidget(MyApp());

      // Enter invalid credentials
      await tester.enterText(find.byKey(Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(Key('password_field')), 'wrongpassword');
      
      // Submit form
      await tester.tap(find.byKey(Key('submit_button')));
      await tester.pumpAndSettle();

      // Verify error message
      expect(find.text('بيانات الدخول غير صحيحة'), findsOneWidget);
    });
  });
}
```

---

## 🎯 **أفضل الممارسات**

### **تنظيم الاختبارات**

```
tests/
├── Unit/           # اختبارات الوحدة
├── Integration/    # اختبارات التكامل
├── Feature/        # اختبارات الميزات
├── Browser/        # اختبارات المتصفح
├── API/           # اختبارات API
├── Performance/   # اختبارات الأداء
├── Security/      # اختبارات الأمان
└── Helpers/       # مساعدات الاختبار
```

### **قواعد كتابة الاختبارات**

1. **اتبع نمط AAA**: Arrange, Act, Assert
2. **اكتب اختبارات مستقلة**: كل اختبار يجب أن يعمل بمفرده
3. **استخدم أسماء وصفية**: وضح ما يختبره الاختبار
4. **اختبر حالة واحدة**: اختبار واحد لكل سيناريو
5. **استخدم البيانات الوهمية**: لا تعتمد على بيانات حقيقية

### **تحسين الأداء**

```bash
# تشغيل الاختبارات بالتوازي
vendor/bin/phpunit --parallel

# استخدام قاعدة بيانات في الذاكرة
DB_CONNECTION=sqlite
DB_DATABASE=:memory:

# تخطي الاختبارات البطيئة في التطوير
vendor/bin/phpunit --exclude-group slow
```

---

## 📈 **التكامل المستمر**

### **GitHub Actions**

```yaml
# .github/workflows/tests.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: 8.1
        
    - name: Install dependencies
      run: composer install
      
    - name: Run tests
      run: vendor/bin/phpunit --coverage-clover coverage.xml
      
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

---

## 📞 **الدعم والمساعدة**

- **الوثائق**: https://docs.streaming-platform.com/testing
- **GitHub Issues**: https://github.com/streaming-platform/issues
- **Discord**: #testing قناة في Discord
- **البريد الإلكتروني**: <EMAIL>

---

**🧪 دليل شامل للاختبارات في منصة البث العربية**

آخر تحديث: 15 يناير 2024
