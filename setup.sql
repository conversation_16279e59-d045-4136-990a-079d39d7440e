-- 🗄️ إعداد قاعدة البيانات لمنصة البث
-- تشغيل هذا الملف لإنشاء قاعدة البيانات والجداول

-- إن<PERSON>اء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS streaming_platform 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE streaming_platform;

-- جدول المستخدمين
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    avatar TEXT,
    role ENUM('user', 'admin', 'moderator') DEFAULT 'user',
    subscription_type ENUM('free', 'basic', 'premium', 'vip') DEFAULT 'free',
    subscription_expires_at DATETIME,
    language VARCHAR(5) DEFAULT 'ar',
    timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
    status ENUM('active', 'inactive', 'suspended', 'locked') DEFAULT 'active',
    email_verified_at DATETIME,
    last_login_at DATETIME,
    last_login_ip VARCHAR(45),
    failed_login_attempts INT DEFAULT 0,
    last_failed_login DATETIME,
    locked_at DATETIME,
    locked_reason VARCHAR(255),
    marketing_consent BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_subscription (subscription_type, subscription_expires_at),
    INDEX idx_created_at (created_at)
);

-- جدول المحتوى (الأفلام والمسلسلات)
CREATE TABLE content (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    synopsis TEXT,
    type ENUM('movie', 'series', 'documentary', 'live') NOT NULL,
    genre VARCHAR(255),
    subgenre VARCHAR(255),
    release_date DATE,
    duration INT, -- بالدقائق
    language VARCHAR(100),
    country VARCHAR(100),
    director VARCHAR(255),
    producer VARCHAR(255),
    writer VARCHAR(255),
    cast TEXT,
    our_rating DECIMAL(3,1),
    imdb_rating DECIMAL(3,1),
    age_rating VARCHAR(10),
    poster TEXT,
    banner TEXT,
    trailer_url TEXT,
    video_url TEXT,
    video_quality ENUM('480p', '720p', '1080p', '4K') DEFAULT '1080p',
    file_size BIGINT,
    subtitles JSON,
    audio_tracks JSON,
    tags JSON,
    view_count INT DEFAULT 0,
    download_count INT DEFAULT 0,
    like_count INT DEFAULT 0,
    comment_count INT DEFAULT 0,
    subscription_required ENUM('free', 'basic', 'premium', 'vip') DEFAULT 'free',
    is_featured BOOLEAN DEFAULT FALSE,
    is_trending BOOLEAN DEFAULT FALSE,
    is_new BOOLEAN DEFAULT TRUE,
    status ENUM('draft', 'published', 'archived', 'deleted') DEFAULT 'draft',
    published_at DATETIME,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_title (title),
    INDEX idx_slug (slug),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_genre (genre),
    INDEX idx_rating (our_rating),
    INDEX idx_featured (is_featured),
    INDEX idx_trending (is_trending),
    INDEX idx_published (published_at),
    INDEX idx_subscription (subscription_required),
    FULLTEXT idx_search (title, description),
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول المواسم (للمسلسلات)
CREATE TABLE seasons (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content_id INT NOT NULL,
    season_number INT NOT NULL,
    title VARCHAR(255),
    description TEXT,
    poster TEXT,
    release_date DATE,
    episode_count INT DEFAULT 0,
    status ENUM('upcoming', 'airing', 'completed', 'cancelled') DEFAULT 'upcoming',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_season (content_id, season_number),
    INDEX idx_content_id (content_id),
    INDEX idx_season_number (season_number),
    
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE
);

-- جدول الحلقات
CREATE TABLE episodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    season_id INT NOT NULL,
    episode_number INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    duration INT, -- بالدقائق
    video_url TEXT,
    thumbnail TEXT,
    air_date DATE,
    view_count INT DEFAULT 0,
    status ENUM('upcoming', 'available', 'unavailable') DEFAULT 'upcoming',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_episode (season_id, episode_number),
    INDEX idx_season_id (season_id),
    INDEX idx_episode_number (episode_number),
    INDEX idx_status (status),
    
    FOREIGN KEY (season_id) REFERENCES seasons(id) ON DELETE CASCADE
);

-- جدول جلسات المستخدمين
CREATE TABLE user_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    session_token VARCHAR(128) NOT NULL,
    device_type ENUM('desktop', 'mobile', 'tablet') DEFAULT 'desktop',
    device_name VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_token (session_token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول المفضلة
CREATE TABLE favorites (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_favorite (user_id, content_id),
    INDEX idx_user_id (user_id),
    INDEX idx_content_id (content_id),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE
);

-- جدول قائمة المشاهدة
CREATE TABLE watchlist (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_watchlist (user_id, content_id),
    INDEX idx_user_id (user_id),
    INDEX idx_content_id (content_id),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE
);

-- جدول مشاهدات المحتوى
CREATE TABLE content_views (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    content_id INT NOT NULL,
    episode_id INT,
    watch_time INT DEFAULT 0, -- بالثواني
    total_duration INT DEFAULT 0, -- بالثواني
    progress_percentage DECIMAL(5,2) DEFAULT 0,
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_content_id (content_id),
    INDEX idx_episode_id (episode_id),
    INDEX idx_viewed_at (viewed_at),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    FOREIGN KEY (episode_id) REFERENCES episodes(id) ON DELETE CASCADE
);

-- جدول التقييمات
CREATE TABLE content_ratings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_id INT NOT NULL,
    rating DECIMAL(3,1) NOT NULL,
    review TEXT,
    is_spoiler BOOLEAN DEFAULT FALSE,
    helpful_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_rating (user_id, content_id),
    INDEX idx_user_id (user_id),
    INDEX idx_content_id (content_id),
    INDEX idx_rating (rating),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE
);

-- جدول التعليقات
CREATE TABLE comments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_id INT NOT NULL,
    parent_id INT,
    comment TEXT NOT NULL,
    is_spoiler BOOLEAN DEFAULT FALSE,
    like_count INT DEFAULT 0,
    dislike_count INT DEFAULT 0,
    status ENUM('pending', 'approved', 'rejected', 'hidden') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_content_id (content_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE
);

-- جدول الإعدادات
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_key (setting_key)
);

-- جدول سجلات البحث
CREATE TABLE search_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    search_query VARCHAR(255),
    search_filters JSON,
    results_count INT DEFAULT 0,
    total_results INT DEFAULT 0,
    page INT DEFAULT 1,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_search_query (search_query),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول أنشطة المستخدمين
CREATE TABLE user_activities (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    activity_type VARCHAR(50) NOT NULL,
    activity_data JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_activity_type (activity_type),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول سجلات الأمان
CREATE TABLE security_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_type VARCHAR(50) NOT NULL,
    event_data JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_event_type (event_type),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_at (created_at)
);

-- إدراج الإعدادات الافتراضية
INSERT INTO settings (setting_key, setting_value, setting_type, description) VALUES
('site_name', 'منصة البث', 'string', 'اسم الموقع'),
('site_description', 'منصة بث الأفلام والمسلسلات', 'string', 'وصف الموقع'),
('registration_enabled', 'true', 'boolean', 'تفعيل التسجيل'),
('require_email_verification', 'true', 'boolean', 'طلب تأكيد البريد الإلكتروني'),
('notify_login', 'false', 'boolean', 'إشعار تسجيل الدخول'),
('max_sessions_per_user', '5', 'number', 'عدد الجلسات المسموح للمستخدم'),
('default_language', 'ar', 'string', 'اللغة الافتراضية'),
('default_timezone', 'Asia/Riyadh', 'string', 'المنطقة الزمنية الافتراضية');

-- إنشاء مستخدم إداري افتراضي
INSERT INTO users (
    first_name, last_name, email, password, role, status, email_verified_at
) VALUES (
    'المدير', 'العام', '<EMAIL>', 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password
    'admin', 'active', NOW()
);

-- إدراج محتوى تجريبي
INSERT INTO content (
    title, slug, description, type, genre, release_date, duration,
    our_rating, poster, video_url, subscription_required, status, published_at
) VALUES 
(
    'فيلم تجريبي', 'test-movie', 'هذا فيلم تجريبي لاختبار المنصة',
    'movie', 'أكشن,إثارة', '2024-01-01', 120,
    8.5, '/assets/images/default-poster.jpg', 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
    'free', 'published', NOW()
),
(
    'مسلسل تجريبي', 'test-series', 'هذا مسلسل تجريبي لاختبار المنصة',
    'series', 'دراما,كوميديا', '2024-01-01', NULL,
    9.0, '/assets/images/default-poster.jpg', NULL,
    'basic', 'published', NOW()
);

-- إضافة موسم للمسلسل التجريبي
INSERT INTO seasons (content_id, season_number, title, episode_count, status) 
VALUES (2, 1, 'الموسم الأول', 10, 'completed');

-- إضافة حلقات للموسم
INSERT INTO episodes (season_id, episode_number, title, description, duration, video_url, status) VALUES
(1, 1, 'الحلقة الأولى', 'الحلقة الافتتاحية', 45, 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4', 'available'),
(1, 2, 'الحلقة الثانية', 'تطور الأحداث', 45, 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4', 'available'),
(1, 3, 'الحلقة الثالثة', 'المزيد من الإثارة', 45, 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4', 'available');

COMMIT;
