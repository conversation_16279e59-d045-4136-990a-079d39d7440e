<?php
/**
 * 🎯 رأس الصفحة (Header)
 * يحتوي على شريط التنقل والقوائم الرئيسية
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}
?>

<header class="navbar">
    <div class="navbar-container">
        <!-- الشعار -->
        <a href="/" class="navbar-brand">
            <img src="assets/images/logo.png" alt="<?php echo SITE_NAME; ?>" class="navbar-logo">
            <span class="navbar-brand-text"><?php echo SITE_NAME; ?></span>
        </a>

        <!-- القائمة الرئيسية -->
        <nav class="navbar-nav" id="main-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="/" class="nav-link <?php echo $page === 'home' ? 'active' : ''; ?>">
                        <i class="fas fa-home"></i>
                        <span><?php echo $lang['home']; ?></span>
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a href="/movies" class="nav-link <?php echo $page === 'movies' ? 'active' : ''; ?>">
                        <i class="fas fa-film"></i>
                        <span><?php echo $lang['movies']; ?></span>
                        <i class="fas fa-chevron-down dropdown-icon"></i>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a href="/movies?genre=action" class="dropdown-link">أكشن</a></li>
                        <li><a href="/movies?genre=comedy" class="dropdown-link">كوميديا</a></li>
                        <li><a href="/movies?genre=drama" class="dropdown-link">دراما</a></li>
                        <li><a href="/movies?genre=horror" class="dropdown-link">رعب</a></li>
                        <li><a href="/movies?genre=romance" class="dropdown-link">رومانسي</a></li>
                        <li><a href="/movies?genre=thriller" class="dropdown-link">إثارة</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a href="/series" class="nav-link <?php echo $page === 'series' ? 'active' : ''; ?>">
                        <i class="fas fa-tv"></i>
                        <span><?php echo $lang['series']; ?></span>
                        <i class="fas fa-chevron-down dropdown-icon"></i>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a href="/series?genre=drama" class="dropdown-link">دراما</a></li>
                        <li><a href="/series?genre=comedy" class="dropdown-link">كوميديا</a></li>
                        <li><a href="/series?genre=crime" class="dropdown-link">جريمة</a></li>
                        <li><a href="/series?genre=documentary" class="dropdown-link">وثائقي</a></li>
                        <li><a href="/series?genre=animation" class="dropdown-link">رسوم متحركة</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a href="/trending" class="nav-link">
                        <i class="fas fa-fire"></i>
                        <span>الرائج</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/top-rated" class="nav-link">
                        <i class="fas fa-trophy"></i>
                        <span>الأعلى تقييماً</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- البحث -->
        <div class="navbar-search">
            <div class="search-container">
                <input type="text" 
                       class="search-input" 
                       placeholder="<?php echo $lang['search_placeholder']; ?>"
                       id="main-search">
                <button class="search-btn" type="button">
                    <i class="fas fa-search"></i>
                </button>
                <div class="search-suggestions" id="search-suggestions"></div>
            </div>
        </div>

        <!-- إجراءات المستخدم -->
        <div class="navbar-actions">
            <?php if (IS_LOGGED_IN): ?>
                <!-- إشعارات -->
                <div class="navbar-item dropdown">
                    <button class="navbar-btn notification-btn" id="notifications-btn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge" id="notification-count">3</span>
                    </button>
                    <div class="dropdown-menu notification-dropdown" id="notifications-dropdown">
                        <div class="dropdown-header">
                            <h4>الإشعارات</h4>
                            <button class="mark-all-read-btn">تعيين الكل كمقروء</button>
                        </div>
                        <div class="notification-list" id="notification-list">
                            <!-- سيتم تحميل الإشعارات هنا -->
                        </div>
                        <div class="dropdown-footer">
                            <a href="/notifications" class="view-all-btn">عرض جميع الإشعارات</a>
                        </div>
                    </div>
                </div>

                <!-- قائمة المستخدم -->
                <div class="navbar-item dropdown">
                    <button class="navbar-btn user-btn" id="user-menu-btn">
                        <img src="<?php echo getCurrentUser()['avatar'] ?? 'assets/images/default-avatar.png'; ?>" 
                             alt="الملف الشخصي" 
                             class="user-avatar">
                        <span class="user-name"><?php echo getCurrentUser()['first_name']; ?></span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="dropdown-menu user-dropdown" id="user-dropdown">
                        <div class="dropdown-header">
                            <div class="user-info">
                                <img src="<?php echo getCurrentUser()['avatar'] ?? 'assets/images/default-avatar.png'; ?>" 
                                     alt="الملف الشخصي" 
                                     class="user-avatar-large">
                                <div class="user-details">
                                    <h4><?php echo getCurrentUser()['first_name'] . ' ' . getCurrentUser()['last_name']; ?></h4>
                                    <p><?php echo getCurrentUser()['email']; ?></p>
                                    <span class="user-subscription">
                                        <?php echo ucfirst(getCurrentUser()['subscription_type']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="dropdown-body">
                            <a href="/profile" class="dropdown-link">
                                <i class="fas fa-user"></i>
                                <span>الملف الشخصي</span>
                            </a>
                            <a href="/favorites" class="dropdown-link">
                                <i class="fas fa-heart"></i>
                                <span>المفضلة</span>
                            </a>
                            <a href="/watchlist" class="dropdown-link">
                                <i class="fas fa-bookmark"></i>
                                <span>قائمة المشاهدة</span>
                            </a>
                            <a href="/history" class="dropdown-link">
                                <i class="fas fa-history"></i>
                                <span>سجل المشاهدة</span>
                            </a>
                            <a href="/downloads" class="dropdown-link">
                                <i class="fas fa-download"></i>
                                <span>التحميلات</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="/subscription" class="dropdown-link">
                                <i class="fas fa-crown"></i>
                                <span>الاشتراك</span>
                            </a>
                            <a href="/settings" class="dropdown-link">
                                <i class="fas fa-cog"></i>
                                <span>الإعدادات</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <?php if (isAdmin()): ?>
                            <a href="/admin" class="dropdown-link">
                                <i class="fas fa-shield-alt"></i>
                                <span>لوحة التحكم</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <?php endif; ?>
                            <button class="dropdown-link logout-btn" onclick="logout()">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>تسجيل الخروج</span>
                            </button>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- أزرار تسجيل الدخول والتسجيل -->
                <div class="navbar-item">
                    <a href="/auth/login" class="btn btn-outline btn-sm">
                        <i class="fas fa-sign-in-alt"></i>
                        <span><?php echo $lang['login']; ?></span>
                    </a>
                </div>
                <div class="navbar-item">
                    <a href="/auth/register" class="btn btn-primary btn-sm">
                        <i class="fas fa-user-plus"></i>
                        <span><?php echo $lang['register']; ?></span>
                    </a>
                </div>
            <?php endif; ?>

            <!-- تبديل اللغة -->
            <div class="navbar-item dropdown">
                <button class="navbar-btn language-btn" id="language-btn">
                    <i class="fas fa-globe"></i>
                    <span class="language-code"><?php echo strtoupper(CURRENT_LANGUAGE); ?></span>
                </button>
                <div class="dropdown-menu language-dropdown">
                    <?php foreach (SUPPORTED_LANGUAGES as $code => $name): ?>
                    <a href="?lang=<?php echo $code; ?>" 
                       class="dropdown-link <?php echo $code === CURRENT_LANGUAGE ? 'active' : ''; ?>">
                        <span class="language-flag flag-<?php echo $code; ?>"></span>
                        <span><?php echo $name; ?></span>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- تبديل الوضع الليلي/النهاري -->
            <div class="navbar-item">
                <button class="navbar-btn theme-toggle-btn" id="theme-toggle" title="تبديل الوضع">
                    <i class="fas fa-moon"></i>
                </button>
            </div>

            <!-- قائمة الجوال -->
            <div class="navbar-item mobile-only">
                <button class="navbar-btn mobile-menu-btn" id="mobile-menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- القائمة المحمولة -->
    <div class="mobile-menu" id="mobile-menu">
        <div class="mobile-menu-header">
            <h3>القائمة</h3>
            <button class="mobile-menu-close" id="mobile-menu-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mobile-menu-body">
            <nav class="mobile-nav">
                <a href="/" class="mobile-nav-link">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
                <a href="/movies" class="mobile-nav-link">
                    <i class="fas fa-film"></i>
                    <span>الأفلام</span>
                </a>
                <a href="/series" class="mobile-nav-link">
                    <i class="fas fa-tv"></i>
                    <span>المسلسلات</span>
                </a>
                <a href="/trending" class="mobile-nav-link">
                    <i class="fas fa-fire"></i>
                    <span>الرائج</span>
                </a>
                <a href="/top-rated" class="mobile-nav-link">
                    <i class="fas fa-trophy"></i>
                    <span>الأعلى تقييماً</span>
                </a>
                
                <?php if (IS_LOGGED_IN): ?>
                <div class="mobile-nav-divider"></div>
                <a href="/profile" class="mobile-nav-link">
                    <i class="fas fa-user"></i>
                    <span>الملف الشخصي</span>
                </a>
                <a href="/favorites" class="mobile-nav-link">
                    <i class="fas fa-heart"></i>
                    <span>المفضلة</span>
                </a>
                <a href="/watchlist" class="mobile-nav-link">
                    <i class="fas fa-bookmark"></i>
                    <span>قائمة المشاهدة</span>
                </a>
                <a href="/settings" class="mobile-nav-link">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
                <div class="mobile-nav-divider"></div>
                <button class="mobile-nav-link logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </button>
                <?php else: ?>
                <div class="mobile-nav-divider"></div>
                <a href="/auth/login" class="mobile-nav-link">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>تسجيل الدخول</span>
                </a>
                <a href="/auth/register" class="mobile-nav-link">
                    <i class="fas fa-user-plus"></i>
                    <span>إنشاء حساب</span>
                </a>
                <?php endif; ?>
            </nav>
        </div>
    </div>

    <!-- خلفية القائمة المحمولة -->
    <div class="mobile-menu-overlay" id="mobile-menu-overlay"></div>
</header>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initHeader();
});

function initHeader() {
    // تهيئة القوائم المنسدلة
    initDropdowns();
    
    // تهيئة البحث
    initHeaderSearch();
    
    // تهيئة الإشعارات
    initNotifications();
    
    // تهيئة القائمة المحمولة
    initMobileMenu();
    
    // تهيئة تبديل الوضع
    initThemeToggle();
    
    // تهيئة تبديل اللغة
    initLanguageToggle();
}

function initDropdowns() {
    document.querySelectorAll('.dropdown').forEach(dropdown => {
        const trigger = dropdown.querySelector('.nav-link, .navbar-btn');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        if (!trigger || !menu) return;
        
        trigger.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            // إغلاق القوائم الأخرى
            document.querySelectorAll('.dropdown-menu.show').forEach(otherMenu => {
                if (otherMenu !== menu) {
                    otherMenu.classList.remove('show');
                }
            });
            
            // تبديل القائمة الحالية
            menu.classList.toggle('show');
        });
    });
    
    // إغلاق القوائم عند النقر خارجها
    document.addEventListener('click', () => {
        document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
            menu.classList.remove('show');
        });
    });
}

function initHeaderSearch() {
    const searchInput = document.getElementById('main-search');
    const searchBtn = document.querySelector('.search-btn');
    const suggestions = document.getElementById('search-suggestions');
    
    if (!searchInput) return;
    
    // البحث أثناء الكتابة
    searchInput.addEventListener('input', APP.utils.debounce(async (e) => {
        const query = e.target.value.trim();
        
        if (query.length < 2) {
            suggestions.style.display = 'none';
            return;
        }
        
        try {
            const results = await APP.api.get('/api/search/suggestions', { q: query });
            showSearchSuggestions(results);
        } catch (error) {
            console.error('Search suggestions failed:', error);
        }
    }, 300));
    
    // تنفيذ البحث
    function performSearch() {
        const query = searchInput.value.trim();
        if (query) {
            window.location.href = `/search?q=${encodeURIComponent(query)}`;
        }
    }
    
    searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            performSearch();
        }
    });
    
    searchBtn.addEventListener('click', performSearch);
}

function showSearchSuggestions(results) {
    const suggestions = document.getElementById('search-suggestions');
    
    suggestions.innerHTML = '';
    
    results.forEach(result => {
        const item = document.createElement('div');
        item.className = 'search-suggestion-item';
        item.innerHTML = `
            <img src="${result.poster || 'assets/images/default-poster.jpg'}" 
                 alt="${result.title}" class="suggestion-poster">
            <div class="suggestion-info">
                <h4 class="suggestion-title">${result.title}</h4>
                <p class="suggestion-meta">${result.type === 'movie' ? 'فيلم' : 'مسلسل'} • ${result.year}</p>
            </div>
        `;
        
        item.addEventListener('click', () => {
            window.location.href = `/content/${result.id}`;
        });
        
        suggestions.appendChild(item);
    });
    
    suggestions.style.display = results.length > 0 ? 'block' : 'none';
}

function initNotifications() {
    const notificationBtn = document.getElementById('notifications-btn');
    const notificationDropdown = document.getElementById('notifications-dropdown');
    
    if (!notificationBtn) return;
    
    // تحميل الإشعارات
    loadNotifications();
    
    // تحديث الإشعارات كل دقيقة
    setInterval(loadNotifications, 60000);
}

async function loadNotifications() {
    try {
        const notifications = await APP.api.get('/api/notifications');
        updateNotificationUI(notifications);
    } catch (error) {
        console.error('Failed to load notifications:', error);
    }
}

function updateNotificationUI(notifications) {
    const countBadge = document.getElementById('notification-count');
    const notificationList = document.getElementById('notification-list');
    
    if (!countBadge || !notificationList) return;
    
    const unreadCount = notifications.filter(n => !n.is_read).length;
    
    if (unreadCount > 0) {
        countBadge.textContent = unreadCount;
        countBadge.style.display = 'block';
    } else {
        countBadge.style.display = 'none';
    }
    
    notificationList.innerHTML = '';
    
    notifications.slice(0, 5).forEach(notification => {
        const item = document.createElement('div');
        item.className = `notification-item ${notification.is_read ? '' : 'unread'}`;
        item.innerHTML = `
            <div class="notification-icon">
                <i class="fas fa-${getNotificationIcon(notification.type)}"></i>
            </div>
            <div class="notification-content">
                <h5 class="notification-title">${notification.title}</h5>
                <p class="notification-message">${notification.message}</p>
                <span class="notification-time">${formatTimeAgo(notification.created_at)}</span>
            </div>
        `;
        
        item.addEventListener('click', () => {
            markNotificationAsRead(notification.id);
        });
        
        notificationList.appendChild(item);
    });
}

function getNotificationIcon(type) {
    const icons = {
        'new_content': 'film',
        'subscription': 'crown',
        'reminder': 'bell',
        'promotion': 'tag',
        'system': 'cog'
    };
    return icons[type] || 'bell';
}

function formatTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) return 'الآن';
    if (diffInSeconds < 3600) return `منذ ${Math.floor(diffInSeconds / 60)} دقيقة`;
    if (diffInSeconds < 86400) return `منذ ${Math.floor(diffInSeconds / 3600)} ساعة`;
    return `منذ ${Math.floor(diffInSeconds / 86400)} يوم`;
}

async function markNotificationAsRead(notificationId) {
    try {
        await APP.api.post('/api/notifications/mark-read', { id: notificationId });
        loadNotifications();
    } catch (error) {
        console.error('Failed to mark notification as read:', error);
    }
}

function initMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileMenuClose = document.getElementById('mobile-menu-close');
    const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
    
    if (!mobileMenuBtn || !mobileMenu) return;
    
    function openMobileMenu() {
        mobileMenu.classList.add('show');
        mobileMenuOverlay.classList.add('show');
        document.body.style.overflow = 'hidden';
    }
    
    function closeMobileMenu() {
        mobileMenu.classList.remove('show');
        mobileMenuOverlay.classList.remove('show');
        document.body.style.overflow = '';
    }
    
    mobileMenuBtn.addEventListener('click', openMobileMenu);
    mobileMenuClose.addEventListener('click', closeMobileMenu);
    mobileMenuOverlay.addEventListener('click', closeMobileMenu);
}

function initThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    
    if (!themeToggle) return;
    
    // تحميل الوضع المحفوظ
    const savedTheme = localStorage.getItem('theme') || 'dark';
    document.body.className = savedTheme + '-mode';
    updateThemeIcon(savedTheme);
    
    themeToggle.addEventListener('click', () => {
        const currentTheme = document.body.classList.contains('dark-mode') ? 'dark' : 'light';
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.body.className = newTheme + '-mode';
        localStorage.setItem('theme', newTheme);
        updateThemeIcon(newTheme);
    });
}

function updateThemeIcon(theme) {
    const themeToggle = document.getElementById('theme-toggle');
    const icon = themeToggle.querySelector('i');
    
    if (theme === 'dark') {
        icon.className = 'fas fa-sun';
        themeToggle.title = 'تبديل للوضع النهاري';
    } else {
        icon.className = 'fas fa-moon';
        themeToggle.title = 'تبديل للوضع الليلي';
    }
}

function initLanguageToggle() {
    document.querySelectorAll('.language-dropdown .dropdown-link').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const lang = new URL(link.href).searchParams.get('lang');
            changeLanguage(lang);
        });
    });
}

function changeLanguage(lang) {
    // حفظ اللغة في الكوكيز
    document.cookie = `language=${lang}; max-age=31536000; path=/`;
    
    // إعادة تحميل الصفحة
    window.location.reload();
}

async function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        try {
            await APP.auth.logout();
        } catch (error) {
            console.error('Logout failed:', error);
            window.location.href = '/';
        }
    }
}
</script>
