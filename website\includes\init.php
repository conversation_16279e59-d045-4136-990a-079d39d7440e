<?php
/**
 * 🚀 ملف التهيئة الرئيسي لمنصة البث الشاملة
 * يتم تحميله في بداية كل صفحة لتهيئة النظام
 */

// تعريف ثابت للتحقق من الوصول الصحيح
define('STREAMING_PLATFORM', true);

// بدء تسجيل الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// تحميل ملف الإعدادات الأساسي
require_once dirname(__FILE__) . '/../config/config.php';

// تحميل فئة قاعدة البيانات
require_once dirname(__FILE__) . '/../config/database.php';

// تحميل الدوال المساعدة
require_once dirname(__FILE__) . '/functions.php';

// تحميل نظام الأمان
require_once dirname(__FILE__) . '/security.php';

/**
 * ==========================================
 * 🔧 فئة التطبيق الرئيسية
 * ==========================================
 */
class App {
    private static $instance = null;
    private $db;
    private $user = null;
    private $settings = [];
    
    /**
     * منشئ خاص لمنع إنشاء كائنات متعددة
     */
    private function __construct() {
        $this->initializeDatabase();
        $this->loadDynamicSettings();
        $this->initializeUser();
    }
    
    /**
     * الحصول على مثيل وحيد من التطبيق
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * تهيئة قاعدة البيانات
     */
    private function initializeDatabase() {
        try {
            $this->db = Database::getInstance();
        } catch (Exception $e) {
            $this->handleFatalError('Database connection failed: ' . $e->getMessage());
        }
    }
    
    /**
     * تحميل الإعدادات الديناميكية
     */
    private function loadDynamicSettings() {
        try {
            DynamicSettings::load();
        } catch (Exception $e) {
            logMessage('Failed to load dynamic settings: ' . $e->getMessage(), 'ERROR');
        }
    }
    
    /**
     * تهيئة المستخدم الحالي
     */
    private function initializeUser() {
        if (isLoggedIn()) {
            $this->user = getCurrentUser();
            $this->updateUserActivity();
        }
    }
    
    /**
     * تحديث نشاط المستخدم
     */
    private function updateUserActivity() {
        if ($this->user) {
            try {
                $stmt = $this->db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$this->user['id']]);
            } catch (Exception $e) {
                logMessage('Failed to update user activity: ' . $e->getMessage(), 'ERROR');
            }
        }
    }
    
    /**
     * الحصول على المستخدم الحالي
     */
    public function getUser() {
        return $this->user;
    }
    
    /**
     * الحصول على قاعدة البيانات
     */
    public function getDatabase() {
        return $this->db;
    }
    
    /**
     * معالجة الأخطاء الخطيرة
     */
    private function handleFatalError($message) {
        logMessage($message, 'FATAL');
        
        if (DEBUG_MODE) {
            die("Fatal Error: $message");
        } else {
            die("Service temporarily unavailable. Please try again later.");
        }
    }
    
    /**
     * تسجيل نشاط المستخدم
     */
    public function logActivity($action, $description = '', $data = []) {
        try {
            $userId = $this->user ? $this->user['id'] : null;
            $ip = Security::getClientIP();
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            $stmt = $this->db->prepare("
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $userId,
                $action,
                $description,
                $ip,
                $userAgent,
                json_encode($data)
            ]);
        } catch (Exception $e) {
            logMessage('Failed to log activity: ' . $e->getMessage(), 'ERROR');
        }
    }
}

/**
 * ==========================================
 * 🎯 فئة التوجيه (Router)
 * ==========================================
 */
class Router {
    private $routes = [];
    private $currentRoute = null;
    
    /**
     * إضافة مسار GET
     */
    public function get($pattern, $callback) {
        $this->addRoute('GET', $pattern, $callback);
    }
    
    /**
     * إضافة مسار POST
     */
    public function post($pattern, $callback) {
        $this->addRoute('POST', $pattern, $callback);
    }
    
    /**
     * إضافة مسار
     */
    private function addRoute($method, $pattern, $callback) {
        $this->routes[] = [
            'method' => $method,
            'pattern' => $pattern,
            'callback' => $callback
        ];
    }
    
    /**
     * تشغيل التوجيه
     */
    public function run() {
        $requestMethod = $_SERVER['REQUEST_METHOD'];
        $requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        foreach ($this->routes as $route) {
            if ($route['method'] === $requestMethod && $this->matchPattern($route['pattern'], $requestUri)) {
                $this->currentRoute = $route;
                return call_user_func($route['callback']);
            }
        }
        
        // إذا لم يتم العثور على مسار
        $this->handle404();
    }
    
    /**
     * مطابقة النمط
     */
    private function matchPattern($pattern, $uri) {
        // تحويل النمط إلى regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $pattern);
        $pattern = '#^' . $pattern . '$#';
        
        return preg_match($pattern, $uri);
    }
    
    /**
     * معالجة 404
     */
    private function handle404() {
        http_response_code(404);
        include __DIR__ . '/../public/404.php';
        exit;
    }
}

/**
 * ==========================================
 * 🎨 فئة العرض (View)
 * ==========================================
 */
class View {
    private $data = [];
    private $layout = 'main';
    
    /**
     * تعيين البيانات
     */
    public function with($key, $value = null) {
        if (is_array($key)) {
            $this->data = array_merge($this->data, $key);
        } else {
            $this->data[$key] = $value;
        }
        return $this;
    }
    
    /**
     * تعيين التخطيط
     */
    public function layout($layout) {
        $this->layout = $layout;
        return $this;
    }
    
    /**
     * عرض القالب
     */
    public function render($template) {
        // استخراج البيانات
        extract($this->data);
        
        // بدء التخزين المؤقت للإخراج
        ob_start();
        
        // تحميل ملف القالب
        $templateFile = dirname(__FILE__) . "/../public/views/{$template}.php";
        if (file_exists($templateFile)) {
            include $templateFile;
        } else {
            throw new Exception("Template not found: {$template}");
        }
        
        // الحصول على محتوى القالب
        $content = ob_get_clean();
        
        // إذا كان هناك تخطيط، تحميله
        if ($this->layout) {
            $layoutFile = dirname(__FILE__) . "/../public/layouts/{$this->layout}.php";
            if (file_exists($layoutFile)) {
                include $layoutFile;
            } else {
                echo $content;
            }
        } else {
            echo $content;
        }
    }
}

/**
 * ==========================================
 * 🔧 دوال مساعدة للعرض
 * ==========================================
 */

/**
 * إنشاء كائن عرض جديد
 */
function view($template, $data = []) {
    return (new View())->with($data)->render($template);
}

/**
 * إرجاع استجابة JSON
 */
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * إرجاع استجابة خطأ JSON
 */
function jsonError($message, $statusCode = 400, $errors = []) {
    jsonResponse([
        'success' => false,
        'message' => $message,
        'errors' => $errors
    ], $statusCode);
}

/**
 * إرجاع استجابة نجاح JSON
 */
function jsonSuccess($data = [], $message = 'Success') {
    jsonResponse([
        'success' => true,
        'message' => $message,
        'data' => $data
    ]);
}

/**
 * ==========================================
 * 🚀 تهيئة التطبيق
 * ==========================================
 */

// إنشاء مثيل التطبيق
$app = App::getInstance();

// تعيين معالج الأخطاء المخصص
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    $errorMessage = "Error: $message in $file on line $line";
    logMessage($errorMessage, 'ERROR');
    
    if (DEBUG_MODE) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<strong>Error:</strong> $message<br>";
        echo "<strong>File:</strong> $file<br>";
        echo "<strong>Line:</strong> $line";
        echo "</div>";
    }
    
    return true;
});

// تعيين معالج الاستثناءات المخصص
set_exception_handler(function($exception) {
    $errorMessage = "Uncaught exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine();
    logMessage($errorMessage, 'ERROR');
    
    if (DEBUG_MODE) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<strong>Uncaught Exception:</strong> " . $exception->getMessage() . "<br>";
        echo "<strong>File:</strong> " . $exception->getFile() . "<br>";
        echo "<strong>Line:</strong> " . $exception->getLine() . "<br>";
        echo "<strong>Stack Trace:</strong><pre>" . $exception->getTraceAsString() . "</pre>";
        echo "</div>";
    } else {
        echo "An error occurred. Please try again later.";
    }
});

// تسجيل بداية الطلب
if (LOGGING_ENABLED) {
    $app->logActivity('page_request', $_SERVER['REQUEST_URI'], [
        'method' => $_SERVER['REQUEST_METHOD'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'referer' => $_SERVER['HTTP_REFERER'] ?? ''
    ]);
}

?>
