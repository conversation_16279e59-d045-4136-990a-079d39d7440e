<?php
/**
 * 🚀 الصفحة الرئيسية المبسطة
 * نقطة دخول آمنة للمنصة
 */

// تعريف ثابت المنصة
define('STREAMING_PLATFORM', true);

// إعدادات أساسية
define('DB_HOST', 'localhost');
define('DB_NAME', 'streaming_platform');
define('DB_USER', 'root');
define('DB_PASS', '');
define('SITE_NAME', 'منصة البث');
define('DEBUG_MODE', true);
define('CURRENT_LANGUAGE', 'ar');
define('IS_LOGGED_IN', false);

// بدء الجلسة
session_start();

// محاولة الاتصال بقاعدة البيانات
$dbConnected = false;
$dbError = '';
$content = [];

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnected = true;
    
    // الحصول على المحتوى التجريبي
    $stmt = $pdo->query("SELECT * FROM content WHERE status = 'published' ORDER BY created_at DESC LIMIT 6");
    $content = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $dbError = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - منصة البث الشاملة</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: white;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 30px;
            list-style: none;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #ffd700;
        }

        .hero {
            text-align: center;
            padding: 80px 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #fff, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: rgba(255, 215, 0, 0.9);
            color: #333;
            border-color: transparent;
        }

        .btn-primary:hover {
            background: #ffd700;
            color: #000;
        }

        .content-section {
            max-width: 1200px;
            margin: 0 auto;
            padding: 60px 20px;
        }

        .section-title {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 50px;
            color: white;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            overflow: hidden;
            transition: transform 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .content-card:hover {
            transform: translateY(-10px);
        }

        .card-image {
            height: 200px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
        }

        .card-content {
            padding: 20px;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .card-description {
            opacity: 0.8;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .card-meta {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            opacity: 0.7;
        }

        .features {
            background: rgba(0, 0, 0, 0.2);
            padding: 80px 20px;
        }

        .features-grid {
            max-width: 1000px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
        }

        .feature {
            text-align: center;
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }

        .feature h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .feature p {
            opacity: 0.8;
            line-height: 1.6;
        }

        .footer {
            background: rgba(0, 0, 0, 0.5);
            text-align: center;
            padding: 40px 20px;
            margin-top: 80px;
        }

        .error-banner {
            background: rgba(220, 53, 69, 0.9);
            color: white;
            padding: 15px;
            text-align: center;
            margin-bottom: 20px;
        }

        .success-banner {
            background: rgba(40, 167, 69, 0.9);
            color: white;
            padding: 15px;
            text-align: center;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <a href="/" class="logo">🎬 <?php echo SITE_NAME; ?></a>
            <ul class="nav-links">
                <li><a href="/">الرئيسية</a></li>
                <li><a href="/movies">الأفلام</a></li>
                <li><a href="/series">المسلسلات</a></li>
                <li><a href="/search">البحث</a></li>
                <li><a href="/login">تسجيل الدخول</a></li>
            </ul>
        </nav>
    </header>

    <!-- Database Status -->
    <?php if (!$dbConnected): ?>
    <div class="error-banner">
        ⚠️ قاعدة البيانات غير متصلة. يرجى إعداد قاعدة البيانات أولاً.
        <a href="test.php" style="color: #fff; text-decoration: underline;">اختبار النظام</a>
    </div>
    <?php else: ?>
    <div class="success-banner">
        ✅ المنصة جاهزة للاستخدام! عدد المحتوى المتاح: <?php echo count($content); ?>
    </div>
    <?php endif; ?>

    <!-- Hero Section -->
    <section class="hero">
        <h1>مرحباً بك في منصة البث</h1>
        <p>
            استمتع بمشاهدة آلاف الأفلام والمسلسلات بجودة عالية
            <br>
            مع تجربة مشاهدة لا مثيل لها
        </p>
        
        <div class="cta-buttons">
            <?php if ($dbConnected): ?>
            <a href="#content" class="btn btn-primary">🎬 تصفح المحتوى</a>
            <a href="test.php" class="btn">🧪 اختبار النظام</a>
            <?php else: ?>
            <a href="test.php" class="btn btn-primary">🔧 إعداد النظام</a>
            <a href="quick-start.html" class="btn">📖 العرض التوضيحي</a>
            <?php endif; ?>
        </div>
    </section>

    <!-- Content Section -->
    <?php if ($dbConnected && !empty($content)): ?>
    <section id="content" class="content-section">
        <h2 class="section-title">المحتوى المتاح</h2>
        
        <div class="content-grid">
            <?php foreach ($content as $item): ?>
            <div class="content-card">
                <div class="card-image">
                    <?php echo $item['type'] === 'movie' ? '🎬' : '📺'; ?>
                </div>
                <div class="card-content">
                    <h3 class="card-title"><?php echo htmlspecialchars($item['title']); ?></h3>
                    <p class="card-description"><?php echo htmlspecialchars(substr($item['description'] ?? '', 0, 100)); ?>...</p>
                    <div class="card-meta">
                        <span><?php echo $item['type'] === 'movie' ? 'فيلم' : 'مسلسل'; ?></span>
                        <span><?php echo $item['duration'] ? $item['duration'] . ' دقيقة' : 'متعدد الحلقات'; ?></span>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </section>
    <?php endif; ?>

    <!-- Features Section -->
    <section class="features">
        <h2 class="section-title">لماذا تختار منصتنا؟</h2>
        
        <div class="features-grid">
            <div class="feature">
                <div class="feature-icon">🎬</div>
                <h3>مشغل فيديو متطور</h3>
                <p>مشغل فيديو احترافي مع دعم جودات متعددة وترجمات</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🔍</div>
                <h3>بحث ذكي</h3>
                <p>ابحث عن المحتوى المفضل لديك بسهولة مع فلاتر متقدمة</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📱</div>
                <h3>تطبيق جوال</h3>
                <p>استمتع بالمشاهدة على جميع الأجهزة مع تطبيقنا المتطور</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🔐</div>
                <h3>أمان متقدم</h3>
                <p>حماية شاملة لبياناتك مع أحدث تقنيات الأمان</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <p>&copy; 2024 <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
        <p>تم تطوير هذا المشروع بـ ❤️ لمجتمع المطورين العرب</p>
    </footer>

    <script>
        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التمرير السلس
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // تأثير الظهور التدريجي للبطاقات
            const cards = document.querySelectorAll('.content-card, .feature');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });

        console.log('🎬 منصة البث - الصفحة الرئيسية');
        console.log('حالة قاعدة البيانات:', <?php echo $dbConnected ? 'true' : 'false'; ?>);
        console.log('عدد المحتوى:', <?php echo count($content); ?>);
    </script>
</body>
</html>
