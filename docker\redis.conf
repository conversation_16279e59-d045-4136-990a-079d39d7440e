# 🔴 إعدادات Redis محسنة لمنصة البث العربية

# الإعدادات الأساسية
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 60

# إعدادات الذاكرة
maxmemory 128mb
maxmemory-policy allkeys-lru

# إعدادات الحفظ
save 900 1
save 300 10
save 60 10000

# إعدادات RDB
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# إعدادات AOF
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# إعدادات الأداء
tcp-backlog 511
databases 16
lua-time-limit 5000

# إعدادات العملاء
maxclients 10000

# إعدادات السجلات
loglevel notice
logfile ""

# إعدادات الأمان (اختياري)
# requirepass your_redis_password

# إعدادات الشبكة
tcp-nodelay yes

# إعدادات متقدمة
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000

# إعدادات العمليات النشطة
activerehashing yes
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# إعدادات التردد
hz 10

# إعدادات الذاكرة المتقدمة
dynamic-hz yes
aof-rewrite-incremental-fsync yes
