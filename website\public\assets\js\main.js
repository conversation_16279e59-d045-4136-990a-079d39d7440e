/**
 * 🚀 ملف JavaScript الرئيسي لمنصة البث الشاملة
 * يحتوي على جميع الوظائف الأساسية للموقع
 */

// ==========================================
// 🌐 المتغيرات العامة
// ==========================================
const APP = {
    config: window.APP_CONFIG || {},
    cache: new Map(),
    eventListeners: new Map(),
    components: new Map(),
    utils: {},
    api: {},
    ui: {},
    player: {},
    search: {},
    auth: {}
};

// ==========================================
// 🔧 الدوال المساعدة العامة
// ==========================================
APP.utils = {
    /**
     * تأخير التنفيذ (Debounce)
     */
    debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func(...args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func(...args);
        };
    },

    /**
     * تحديد معدل التنفيذ (Throttle)
     */
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * تنسيق الوقت
     */
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    },

    /**
     * تنسيق حجم الملف
     */
    formatFileSize(bytes) {
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        if (bytes === 0) return '0 B';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    },

    /**
     * إنشاء عنصر HTML
     */
    createElement(tag, attributes = {}, children = []) {
        const element = document.createElement(tag);
        
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'className') {
                element.className = value;
            } else if (key === 'innerHTML') {
                element.innerHTML = value;
            } else if (key.startsWith('data-')) {
                element.setAttribute(key, value);
            } else {
                element[key] = value;
            }
        });
        
        children.forEach(child => {
            if (typeof child === 'string') {
                element.appendChild(document.createTextNode(child));
            } else {
                element.appendChild(child);
            }
        });
        
        return element;
    },

    /**
     * تحديد نوع الجهاز
     */
    getDeviceType() {
        const userAgent = navigator.userAgent;
        if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
            return 'tablet';
        }
        if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
            return 'mobile';
        }
        return 'desktop';
    },

    /**
     * التحقق من دعم الميزة
     */
    supportsFeature(feature) {
        const features = {
            localStorage: () => {
                try {
                    localStorage.setItem('test', 'test');
                    localStorage.removeItem('test');
                    return true;
                } catch (e) {
                    return false;
                }
            },
            serviceWorker: () => 'serviceWorker' in navigator,
            webGL: () => {
                try {
                    const canvas = document.createElement('canvas');
                    return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
                } catch (e) {
                    return false;
                }
            },
            fullscreen: () => document.fullscreenEnabled || document.webkitFullscreenEnabled || document.mozFullScreenEnabled,
            pictureInPicture: () => 'pictureInPictureEnabled' in document
        };
        
        return features[feature] ? features[feature]() : false;
    }
};

// ==========================================
// 🌐 إدارة API
// ==========================================
APP.api = {
    /**
     * إرسال طلب HTTP
     */
    async request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-Token': APP.config.csrfToken
            }
        };

        const config = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            }
            
            return await response.text();
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    },

    /**
     * GET request
     */
    get(url, params = {}) {
        const urlParams = new URLSearchParams(params);
        const fullUrl = urlParams.toString() ? `${url}?${urlParams}` : url;
        return this.request(fullUrl);
    },

    /**
     * POST request
     */
    post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },

    /**
     * PUT request
     */
    put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },

    /**
     * DELETE request
     */
    delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }
};

// ==========================================
// 🎨 إدارة واجهة المستخدم
// ==========================================
APP.ui = {
    /**
     * عرض إشعار
     */
    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notification-container') || this.createNotificationContainer();
        
        const notification = APP.utils.createElement('div', {
            className: `notification notification-${type} fade-in`,
            innerHTML: `
                <div class="notification-content">
                    <span class="notification-message">${message}</span>
                    <button class="notification-close" aria-label="إغلاق">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `
        });

        container.appendChild(notification);

        // إغلاق تلقائي
        if (duration > 0) {
            setTimeout(() => {
                this.hideNotification(notification);
            }, duration);
        }

        // إغلاق يدوي
        notification.querySelector('.notification-close').addEventListener('click', () => {
            this.hideNotification(notification);
        });

        return notification;
    },

    /**
     * إخفاء إشعار
     */
    hideNotification(notification) {
        notification.style.animation = 'slideOutRight 0.3s ease-in-out';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    },

    /**
     * إنشاء حاوية الإشعارات
     */
    createNotificationContainer() {
        const container = APP.utils.createElement('div', {
            id: 'notification-container',
            className: 'notification-container'
        });
        document.body.appendChild(container);
        return container;
    },

    /**
     * عرض مودال
     */
    showModal(content, options = {}) {
        const modal = APP.utils.createElement('div', {
            className: 'modal-overlay',
            innerHTML: `
                <div class="modal-container">
                    <div class="modal-header">
                        <h3 class="modal-title">${options.title || ''}</h3>
                        <button class="modal-close" aria-label="إغلاق">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    ${options.footer ? `<div class="modal-footer">${options.footer}</div>` : ''}
                </div>
            `
        });

        document.body.appendChild(modal);
        document.body.style.overflow = 'hidden';

        // إغلاق المودال
        const closeModal = () => {
            document.body.style.overflow = '';
            modal.remove();
        };

        modal.querySelector('.modal-close').addEventListener('click', closeModal);
        modal.addEventListener('click', (e) => {
            if (e.target === modal) closeModal();
        });

        // إغلاق بمفتاح Escape
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                closeModal();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);

        return modal;
    },

    /**
     * عرض مؤشر التحميل
     */
    showLoader(target = document.body) {
        const loader = APP.utils.createElement('div', {
            className: 'loader-overlay',
            innerHTML: `
                <div class="loader">
                    <div class="loader-spinner"></div>
                    <p class="loader-text">جاري التحميل...</p>
                </div>
            `
        });

        target.appendChild(loader);
        return loader;
    },

    /**
     * إخفاء مؤشر التحميل
     */
    hideLoader(loader) {
        if (loader && loader.parentNode) {
            loader.parentNode.removeChild(loader);
        }
    },

    /**
     * تأكيد العملية
     */
    confirm(message, onConfirm, onCancel) {
        const modal = this.showModal(`
            <p class="confirm-message">${message}</p>
        `, {
            title: 'تأكيد',
            footer: `
                <button class="btn btn-secondary" id="cancel-btn">إلغاء</button>
                <button class="btn btn-danger" id="confirm-btn">تأكيد</button>
            `
        });

        modal.querySelector('#confirm-btn').addEventListener('click', () => {
            modal.remove();
            document.body.style.overflow = '';
            if (onConfirm) onConfirm();
        });

        modal.querySelector('#cancel-btn').addEventListener('click', () => {
            modal.remove();
            document.body.style.overflow = '';
            if (onCancel) onCancel();
        });
    }
};

// ==========================================
// 🔍 إدارة البحث
// ==========================================
APP.search = {
    searchInput: null,
    suggestionsContainer: null,
    searchHistory: [],

    /**
     * تهيئة البحث
     */
    init() {
        this.searchInput = document.querySelector('.search-input');
        this.suggestionsContainer = document.querySelector('.search-suggestions');
        
        if (!this.searchInput) return;

        this.loadSearchHistory();
        this.bindEvents();
    },

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // البحث أثناء الكتابة
        this.searchInput.addEventListener('input', APP.utils.debounce((e) => {
            this.handleSearch(e.target.value);
        }, 300));

        // إرسال البحث
        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.performSearch(e.target.value);
            }
        });

        // إخفاء الاقتراحات عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-container')) {
                this.hideSuggestions();
            }
        });
    },

    /**
     * معالجة البحث
     */
    async handleSearch(query) {
        if (query.length < 2) {
            this.hideSuggestions();
            return;
        }

        try {
            const suggestions = await APP.api.get('/api/search/suggestions', { q: query });
            this.showSuggestions(suggestions);
        } catch (error) {
            console.error('Search failed:', error);
        }
    },

    /**
     * عرض الاقتراحات
     */
    showSuggestions(suggestions) {
        if (!this.suggestionsContainer) return;

        this.suggestionsContainer.innerHTML = '';
        
        suggestions.forEach(suggestion => {
            const item = APP.utils.createElement('div', {
                className: 'search-suggestion',
                innerHTML: `
                    <i class="fas fa-search"></i>
                    <span>${suggestion.title}</span>
                `
            });

            item.addEventListener('click', () => {
                this.performSearch(suggestion.title);
            });

            this.suggestionsContainer.appendChild(item);
        });

        this.suggestionsContainer.style.display = 'block';
    },

    /**
     * إخفاء الاقتراحات
     */
    hideSuggestions() {
        if (this.suggestionsContainer) {
            this.suggestionsContainer.style.display = 'none';
        }
    },

    /**
     * تنفيذ البحث
     */
    performSearch(query) {
        if (!query.trim()) return;

        this.addToHistory(query);
        this.hideSuggestions();
        
        // الانتقال لصفحة النتائج
        window.location.href = `/search?q=${encodeURIComponent(query)}`;
    },

    /**
     * إضافة للتاريخ
     */
    addToHistory(query) {
        this.searchHistory = this.searchHistory.filter(item => item !== query);
        this.searchHistory.unshift(query);
        this.searchHistory = this.searchHistory.slice(0, 10);
        this.saveSearchHistory();
    },

    /**
     * تحميل تاريخ البحث
     */
    loadSearchHistory() {
        if (APP.utils.supportsFeature('localStorage')) {
            const history = localStorage.getItem('searchHistory');
            this.searchHistory = history ? JSON.parse(history) : [];
        }
    },

    /**
     * حفظ تاريخ البحث
     */
    saveSearchHistory() {
        if (APP.utils.supportsFeature('localStorage')) {
            localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
        }
    }
};

// ==========================================
// 🔐 إدارة المصادقة
// ==========================================
APP.auth = {
    /**
     * تسجيل الدخول
     */
    async login(email, password, rememberMe = false) {
        try {
            const response = await APP.api.post('/api/auth/login', {
                email,
                password,
                remember_me: rememberMe
            });

            if (response.success) {
                APP.ui.showNotification('تم تسجيل الدخول بنجاح', 'success');
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1000);
            } else {
                APP.ui.showNotification(response.error || 'خطأ في تسجيل الدخول', 'error');
            }

            return response;
        } catch (error) {
            APP.ui.showNotification('خطأ في الاتصال', 'error');
            throw error;
        }
    },

    /**
     * تسجيل الخروج
     */
    async logout() {
        try {
            await APP.api.post('/api/auth/logout');
            APP.ui.showNotification('تم تسجيل الخروج بنجاح', 'success');
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        } catch (error) {
            console.error('Logout failed:', error);
        }
    },

    /**
     * التسجيل
     */
    async register(userData) {
        try {
            const response = await APP.api.post('/api/auth/register', userData);

            if (response.success) {
                APP.ui.showNotification('تم إنشاء الحساب بنجاح', 'success');
                if (response.requires_verification) {
                    APP.ui.showNotification('يرجى تأكيد البريد الإلكتروني', 'info');
                }
            } else {
                APP.ui.showNotification(response.error || 'خطأ في إنشاء الحساب', 'error');
            }

            return response;
        } catch (error) {
            APP.ui.showNotification('خطأ في الاتصال', 'error');
            throw error;
        }
    }
};

// ==========================================
// 🎬 مشغل الفيديو
// ==========================================
APP.player = {
    video: null,
    controls: null,
    isPlaying: false,
    currentTime: 0,
    duration: 0,

    /**
     * تهيئة المشغل
     */
    init(videoElement) {
        this.video = videoElement;
        this.controls = videoElement.parentElement.querySelector('.video-controls');
        
        if (!this.video || !this.controls) return;

        this.bindEvents();
        this.updateControls();
    },

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // أحداث الفيديو
        this.video.addEventListener('loadedmetadata', () => {
            this.duration = this.video.duration;
            this.updateDuration();
        });

        this.video.addEventListener('timeupdate', () => {
            this.currentTime = this.video.currentTime;
            this.updateProgress();
        });

        this.video.addEventListener('play', () => {
            this.isPlaying = true;
            this.updatePlayButton();
        });

        this.video.addEventListener('pause', () => {
            this.isPlaying = false;
            this.updatePlayButton();
        });

        // أزرار التحكم
        this.controls.querySelector('.play-btn').addEventListener('click', () => {
            this.togglePlay();
        });

        this.controls.querySelector('.fullscreen-btn').addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // شريط التقدم
        const progressBar = this.controls.querySelector('.video-progress');
        progressBar.addEventListener('click', (e) => {
            const rect = progressBar.getBoundingClientRect();
            const percent = (e.clientX - rect.left) / rect.width;
            this.seekTo(percent * this.duration);
        });
    },

    /**
     * تشغيل/إيقاف
     */
    togglePlay() {
        if (this.isPlaying) {
            this.video.pause();
        } else {
            this.video.play();
        }
    },

    /**
     * الانتقال لوقت محدد
     */
    seekTo(time) {
        this.video.currentTime = time;
    },

    /**
     * ملء الشاشة
     */
    toggleFullscreen() {
        if (document.fullscreenElement) {
            document.exitFullscreen();
        } else {
            this.video.parentElement.requestFullscreen();
        }
    },

    /**
     * تحديث أزرار التحكم
     */
    updateControls() {
        this.updatePlayButton();
        this.updateProgress();
        this.updateDuration();
    },

    /**
     * تحديث زر التشغيل
     */
    updatePlayButton() {
        const playBtn = this.controls.querySelector('.play-btn i');
        playBtn.className = this.isPlaying ? 'fas fa-pause' : 'fas fa-play';
    },

    /**
     * تحديث شريط التقدم
     */
    updateProgress() {
        const progressBar = this.controls.querySelector('.video-progress-bar');
        const percent = (this.currentTime / this.duration) * 100;
        progressBar.style.width = `${percent}%`;

        // تحديث الوقت الحالي
        const currentTimeEl = this.controls.querySelector('.current-time');
        if (currentTimeEl) {
            currentTimeEl.textContent = APP.utils.formatTime(this.currentTime);
        }
    },

    /**
     * تحديث المدة الإجمالية
     */
    updateDuration() {
        const durationEl = this.controls.querySelector('.duration');
        if (durationEl) {
            durationEl.textContent = APP.utils.formatTime(this.duration);
        }
    }
};

// ==========================================
// 🚀 تهيئة التطبيق
// ==========================================
document.addEventListener('DOMContentLoaded', () => {
    // إخفاء شاشة التحميل
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
        setTimeout(() => {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 300);
        }, 1000);
    }

    // تهيئة المكونات
    APP.search.init();

    // تهيئة مشغلات الفيديو
    document.querySelectorAll('.video-player video').forEach(video => {
        APP.player.init(video);
    });

    // زر العودة للأعلى
    const backToTopBtn = document.getElementById('back-to-top');
    if (backToTopBtn) {
        window.addEventListener('scroll', APP.utils.throttle(() => {
            if (window.pageYOffset > 300) {
                backToTopBtn.style.display = 'block';
            } else {
                backToTopBtn.style.display = 'none';
            }
        }, 100));

        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }

    // موافقة الكوكيز
    const cookieConsent = document.getElementById('cookie-consent');
    if (cookieConsent) {
        document.getElementById('accept-cookies').addEventListener('click', () => {
            document.cookie = 'cookie_consent=accepted; max-age=31536000; path=/';
            cookieConsent.style.display = 'none';
        });

        document.getElementById('decline-cookies').addEventListener('click', () => {
            cookieConsent.style.display = 'none';
        });
    }

    // تهيئة النماذج
    document.querySelectorAll('form[data-ajax]').forEach(form => {
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            try {
                const response = await APP.api.post(form.action, data);
                
                if (response.success) {
                    APP.ui.showNotification(response.message || 'تم الحفظ بنجاح', 'success');
                } else {
                    APP.ui.showNotification(response.error || 'حدث خطأ', 'error');
                }
            } catch (error) {
                APP.ui.showNotification('خطأ في الاتصال', 'error');
            }
        });
    });

    // تهيئة الرسوم المتحركة
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, observerOptions);

    document.querySelectorAll('.content-card, .card').forEach(el => {
        observer.observe(el);
    });

    console.log('🎬 منصة البث الشاملة - تم تحميل التطبيق بنجاح!');
});

// ==========================================
// 🌐 تصدير للاستخدام العام
// ==========================================
window.APP = APP;
