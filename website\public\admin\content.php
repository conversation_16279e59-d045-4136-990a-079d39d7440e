<?php
/**
 * 🎬 إدارة المحتوى
 * صفحة إدارة الأفلام والمسلسلات
 */

// تعريف ثابت المنصة
define('STREAMING_PLATFORM', true);

// تحميل ملف التهيئة
require_once dirname(__DIR__) . '/../includes/init.php';

// التحقق من صلاحيات الإدارة
if (!IS_LOGGED_IN || (getCurrentUser()['role'] ?? '') !== 'admin') {
    header('Location: /login');
    exit;
}

// معالجة الطلبات
$action = $_GET['action'] ?? 'list';
$contentId = $_GET['id'] ?? null;
$message = '';
$messageType = '';

// معالجة حذف المحتوى
if ($action === 'delete' && $contentId && $_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        global $db;
        $result = $db->delete('content', 'id = ?', [$contentId]);
        if ($result) {
            $message = 'تم حذف المحتوى بنجاح';
            $messageType = 'success';
        } else {
            $message = 'فشل في حذف المحتوى';
            $messageType = 'error';
        }
    } catch (Exception $e) {
        $message = 'حدث خطأ: ' . $e->getMessage();
        $messageType = 'error';
    }
    $action = 'list';
}

// معالجة تحديث حالة المحتوى
if ($action === 'toggle_status' && $contentId && $_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        global $db;
        $content = $db->selectOne("SELECT status FROM content WHERE id = ?", [$contentId]);
        if ($content) {
            $newStatus = $content['status'] === 'published' ? 'draft' : 'published';
            $db->update('content', ['status' => $newStatus], ['id' => $contentId]);
            $message = 'تم تحديث حالة المحتوى بنجاح';
            $messageType = 'success';
        }
    } catch (Exception $e) {
        $message = 'حدث خطأ: ' . $e->getMessage();
        $messageType = 'error';
    }
    $action = 'list';
}

// الحصول على قائمة المحتوى
$content = [];
$totalContent = 0;
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;
$search = $_GET['search'] ?? '';
$filter = $_GET['filter'] ?? '';

if (DB_CONNECTED) {
    try {
        global $db;
        
        // بناء الاستعلام
        $whereConditions = [];
        $params = [];
        
        if ($search) {
            $whereConditions[] = "title LIKE ?";
            $params[] = "%$search%";
        }
        
        if ($filter) {
            $whereConditions[] = "type = ?";
            $params[] = $filter;
        }
        
        $whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // عدد النتائج الإجمالي
        $countQuery = "SELECT COUNT(*) as total FROM content $whereClause";
        $totalResult = $db->selectOne($countQuery, $params);
        $totalContent = $totalResult['total'] ?? 0;
        
        // الحصول على المحتوى
        $contentQuery = "SELECT * FROM content $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
        $content = $db->select($contentQuery, $params);
        
    } catch (Exception $e) {
        error_log("خطأ في تحميل المحتوى: " . $e->getMessage());
    }
}

$totalPages = ceil($totalContent / $limit);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المحتوى - <?php echo SITE_NAME; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <meta name="csrf-token" content="<?php echo csrf_token(); ?>">
    
    <style>
        .content-thumbnail {
            width: 60px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        .content-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .filter-bar {
            background: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            box-shadow: var(--admin-shadow);
            margin-bottom: 1.5rem;
            border: 1px solid var(--admin-border);
        }
        
        .filter-form {
            display: flex;
            gap: 1rem;
            align-items: end;
            flex-wrap: wrap;
        }
        
        .filter-group {
            flex: 1;
            min-width: 200px;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        
        .pagination a,
        .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid var(--admin-border);
            border-radius: 0.375rem;
            text-decoration: none;
            color: var(--admin-dark);
        }
        
        .pagination a:hover {
            background: var(--admin-light);
        }
        
        .pagination .current {
            background: var(--admin-primary);
            color: white;
            border-color: var(--admin-primary);
        }
        
        .bulk-actions {
            background: var(--admin-light);
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            display: none;
        }
        
        .bulk-actions.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- الشريط الجانبي -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <a href="/admin" class="sidebar-logo">
                    🎬 <?php echo SITE_NAME; ?>
                </a>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">الرئيسية</div>
                    <a href="/admin" class="nav-item">
                        <i class="fas fa-tachometer-alt"></i>
                        لوحة التحكم
                    </a>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">المحتوى</div>
                    <a href="/admin/content.php" class="nav-item active">
                        <i class="fas fa-film"></i>
                        إدارة المحتوى
                    </a>
                    <a href="/admin/content-add.php" class="nav-item">
                        <i class="fas fa-plus"></i>
                        إضافة محتوى
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- المحتوى الرئيسي -->
        <main class="admin-main">
            <!-- الرأس -->
            <header class="admin-header">
                <div class="header-content">
                    <h1 class="header-title">إدارة المحتوى</h1>
                    <div class="header-actions">
                        <a href="/admin/content-add.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            إضافة محتوى جديد
                        </a>
                    </div>
                </div>
            </header>
            
            <!-- المحتوى -->
            <div class="admin-content">
                <!-- رسائل التنبيه -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
                <?php endif; ?>
                
                <!-- شريط الفلترة والبحث -->
                <div class="filter-bar">
                    <form method="GET" class="filter-form">
                        <div class="filter-group">
                            <label class="form-label">البحث</label>
                            <input type="text" name="search" class="form-control" 
                                   placeholder="ابحث عن المحتوى..." 
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        
                        <div class="filter-group">
                            <label class="form-label">النوع</label>
                            <select name="filter" class="form-control form-select">
                                <option value="">جميع الأنواع</option>
                                <option value="movie" <?php echo $filter === 'movie' ? 'selected' : ''; ?>>أفلام</option>
                                <option value="series" <?php echo $filter === 'series' ? 'selected' : ''; ?>>مسلسلات</option>
                                <option value="documentary" <?php echo $filter === 'documentary' ? 'selected' : ''; ?>>وثائقي</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                                بحث
                            </button>
                            <a href="/admin/content.php" class="btn btn-outline">
                                <i class="fas fa-times"></i>
                                مسح
                            </a>
                        </div>
                    </form>
                </div>
                
                <!-- إجراءات مجمعة -->
                <div class="bulk-actions" id="bulkActions">
                    <div class="d-flex justify-content-between align-items-center">
                        <span id="selectedCount">0 عنصر محدد</span>
                        <div>
                            <button class="btn btn-warning btn-sm" onclick="bulkAction('publish')">
                                <i class="fas fa-eye"></i>
                                نشر المحدد
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="bulkAction('draft')">
                                <i class="fas fa-eye-slash"></i>
                                إخفاء المحدد
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="bulkAction('delete')">
                                <i class="fas fa-trash"></i>
                                حذف المحدد
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- جدول المحتوى -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            قائمة المحتوى (<?php echo number_format($totalContent); ?> عنصر)
                        </h3>
                    </div>
                    
                    <div class="card-body">
                        <?php if (!empty($content)): ?>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                        </th>
                                        <th width="80">صورة</th>
                                        <th>العنوان</th>
                                        <th>النوع</th>
                                        <th>الحالة</th>
                                        <th>المشاهدات</th>
                                        <th>تاريخ الإضافة</th>
                                        <th width="150">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($content as $item): ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="content-checkbox" 
                                                   value="<?php echo $item['id']; ?>" 
                                                   onchange="updateBulkActions()">
                                        </td>
                                        <td>
                                            <?php if ($item['poster']): ?>
                                            <img src="<?php echo htmlspecialchars($item['poster']); ?>" 
                                                 alt="<?php echo htmlspecialchars($item['title']); ?>"
                                                 class="content-thumbnail">
                                            <?php else: ?>
                                            <div class="content-thumbnail" style="background: #f0f0f0; display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-image" style="color: #ccc;"></i>
                                            </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($item['title']); ?></strong>
                                                <?php if ($item['description']): ?>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars(substr($item['description'], 0, 100)); ?>...
                                                </small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge badge-primary">
                                                <?php 
                                                $types = [
                                                    'movie' => 'فيلم',
                                                    'series' => 'مسلسل',
                                                    'documentary' => 'وثائقي'
                                                ];
                                                echo $types[$item['type']] ?? $item['type'];
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo $item['status'] === 'published' ? 'success' : 'warning'; ?>">
                                                <?php echo $item['status'] === 'published' ? 'منشور' : 'مسودة'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <i class="fas fa-eye"></i>
                                            <?php echo number_format($item['view_count'] ?? 0); ?>
                                        </td>
                                        <td>
                                            <?php echo date('Y-m-d', strtotime($item['created_at'])); ?>
                                        </td>
                                        <td>
                                            <div class="content-actions">
                                                <a href="/content/<?php echo $item['id']; ?>" 
                                                   class="btn btn-sm btn-outline" 
                                                   title="عرض" target="_blank">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="/admin/content-edit.php?id=<?php echo $item['id']; ?>" 
                                                   class="btn btn-sm btn-primary" 
                                                   title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form method="POST" style="display: inline;" 
                                                      onsubmit="return confirm('تغيير حالة النشر؟')">
                                                    <input type="hidden" name="action" value="toggle_status">
                                                    <input type="hidden" name="id" value="<?php echo $item['id']; ?>">
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-<?php echo $item['status'] === 'published' ? 'warning' : 'success'; ?>" 
                                                            title="<?php echo $item['status'] === 'published' ? 'إخفاء' : 'نشر'; ?>">
                                                        <i class="fas fa-<?php echo $item['status'] === 'published' ? 'eye-slash' : 'eye'; ?>"></i>
                                                    </button>
                                                </form>
                                                <form method="POST" style="display: inline;" 
                                                      onsubmit="return confirm('هل أنت متأكد من حذف هذا المحتوى؟')">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="id" value="<?php echo $item['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- ترقيم الصفحات -->
                        <?php if ($totalPages > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo urlencode($filter); ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <?php if ($i === $page): ?>
                            <span class="current"><?php echo $i; ?></span>
                            <?php else: ?>
                            <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo urlencode($filter); ?>">
                                <?php echo $i; ?>
                            </a>
                            <?php endif; ?>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                            <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo urlencode($filter); ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php else: ?>
                        <div class="text-center" style="padding: 3rem;">
                            <i class="fas fa-film" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                            <h3>لا يوجد محتوى</h3>
                            <p>لم يتم العثور على أي محتوى مطابق للبحث</p>
                            <a href="/admin/content-add.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                إضافة محتوى جديد
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- JavaScript -->
    <script src="/assets/js/admin.js"></script>
    
    <script>
        // إدارة التحديد المجمع
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.content-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
            
            updateBulkActions();
        }
        
        function updateBulkActions() {
            const checkboxes = document.querySelectorAll('.content-checkbox:checked');
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.getElementById('selectedCount');
            
            if (checkboxes.length > 0) {
                bulkActions.classList.add('show');
                selectedCount.textContent = `${checkboxes.length} عنصر محدد`;
            } else {
                bulkActions.classList.remove('show');
            }
        }
        
        function bulkAction(action) {
            const checkboxes = document.querySelectorAll('.content-checkbox:checked');
            const ids = Array.from(checkboxes).map(cb => cb.value);
            
            if (ids.length === 0) {
                alert('يرجى تحديد عنصر واحد على الأقل');
                return;
            }
            
            let confirmMessage = '';
            switch (action) {
                case 'publish':
                    confirmMessage = `نشر ${ids.length} عنصر؟`;
                    break;
                case 'draft':
                    confirmMessage = `إخفاء ${ids.length} عنصر؟`;
                    break;
                case 'delete':
                    confirmMessage = `حذف ${ids.length} عنصر نهائياً؟`;
                    break;
            }
            
            if (confirm(confirmMessage)) {
                // تنفيذ الإجراء المجمع
                console.log(`تنفيذ ${action} على العناصر:`, ids);
                // هنا يمكن إضافة كود AJAX لتنفيذ الإجراء
            }
        }
    </script>
</body>
</html>
