# 📚 وثائق API - منصة البث العربية

## 🌐 **نظرة عامة**

API منصة البث العربية هو RESTful API يوفر وصولاً شاملاً لجميع ميزات المنصة. يدعم JSON و XML ويتضمن مصادقة آمنة ومعدل محدود للطلبات.

### **معلومات أساسية**
- **Base URL**: `https://api.streaming-platform.com/v1`
- **Protocol**: HTTPS only
- **Format**: JSON (default), XML
- **Authentication**: JWT Bearer Token
- **Rate Limit**: 1000 requests/hour
- **Versioning**: URL-based (/v1, /v2, etc.)

---

## 🔐 **المصادقة والأمان**

### **الحصول على Token**

```http
POST /auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "secure_password"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expires_in": 3600,
        "user": {
            "id": 1,
            "name": "أحمد محمد",
            "email": "<EMAIL>"
        }
    }
}
```

### **استخدام Token**

```http
GET /movies
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

---

## 🎬 **Movies API**

### **جلب جميع الأفلام**

```http
GET /movies
```

**Parameters:**
- `page` (int): رقم الصفحة (افتراضي: 1)
- `limit` (int): عدد النتائج (افتراضي: 20, أقصى: 100)
- `genre` (string): تصفية حسب النوع
- `year` (int): تصفية حسب السنة
- `rating` (float): تصفية حسب التقييم
- `sort` (string): ترتيب (title, year, rating, created_at)
- `order` (string): اتجاه الترتيب (asc, desc)

**Response:**
```json
{
    "success": true,
    "data": {
        "movies": [
            {
                "id": 1,
                "title": "فيلم الأكشن الجديد",
                "slug": "action-movie-2024",
                "description": "وصف الفيلم...",
                "poster": "https://cdn.streaming-platform.com/posters/movie1.jpg",
                "trailer": "https://cdn.streaming-platform.com/trailers/movie1.mp4",
                "duration": 120,
                "year": 2024,
                "rating": 8.5,
                "genre": "أكشن",
                "director": "أحمد محمد",
                "cast": ["محمد علي", "فاطمة أحمد"],
                "created_at": "2024-01-15T10:00:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 10,
            "total_items": 200,
            "per_page": 20
        }
    }
}
```

### **جلب فيلم محدد**

```http
GET /movies/{id}
```

### **إضافة فيلم جديد**

```http
POST /movies
Authorization: Bearer {token}
Content-Type: application/json

{
    "title": "فيلم جديد",
    "description": "وصف الفيلم",
    "duration": 120,
    "year": 2024,
    "genre": "دراما",
    "director": "المخرج",
    "cast": ["ممثل 1", "ممثل 2"]
}
```

### **تحديث فيلم**

```http
PUT /movies/{id}
Authorization: Bearer {token}
```

### **حذف فيلم**

```http
DELETE /movies/{id}
Authorization: Bearer {token}
```

---

## 📺 **Series API**

### **جلب جميع المسلسلات**

```http
GET /series
```

### **جلب حلقات مسلسل**

```http
GET /series/{id}/episodes
```

**Response:**
```json
{
    "success": true,
    "data": {
        "episodes": [
            {
                "id": 1,
                "title": "الحلقة الأولى",
                "episode_number": 1,
                "season_number": 1,
                "description": "وصف الحلقة",
                "duration": 45,
                "video_url": "https://cdn.streaming-platform.com/episodes/ep1.mp4",
                "thumbnail": "https://cdn.streaming-platform.com/thumbnails/ep1.jpg"
            }
        ]
    }
}
```

---

## 👤 **Users API**

### **جلب ملف المستخدم**

```http
GET /user/profile
Authorization: Bearer {token}
```

### **تحديث الملف الشخصي**

```http
PUT /user/profile
Authorization: Bearer {token}

{
    "name": "الاسم الجديد",
    "email": "<EMAIL>",
    "phone": "+966501234567"
}
```

### **جلب المفضلة**

```http
GET /user/favorites
Authorization: Bearer {token}
```

### **إضافة للمفضلة**

```http
POST /user/favorites
Authorization: Bearer {token}

{
    "type": "movie",
    "item_id": 1
}
```

---

## 🔍 **Search API**

### **البحث العام**

```http
GET /search?q={query}
```

**Parameters:**
- `q` (string): نص البحث
- `type` (string): نوع المحتوى (movies, series, all)
- `page` (int): رقم الصفحة
- `limit` (int): عدد النتائج

**Response:**
```json
{
    "success": true,
    "data": {
        "results": [
            {
                "type": "movie",
                "id": 1,
                "title": "فيلم الأكشن",
                "poster": "poster_url",
                "rating": 8.5,
                "year": 2024
            }
        ],
        "total": 50,
        "query": "أكشن"
    }
}
```

### **البحث المتقدم**

```http
POST /search/advanced

{
    "query": "أكشن",
    "filters": {
        "genre": ["أكشن", "إثارة"],
        "year_range": [2020, 2024],
        "rating_min": 7.0,
        "duration_range": [90, 180]
    }
}
```

---

## 📊 **Analytics API**

### **إحصائيات المشاهدة**

```http
GET /analytics/views
Authorization: Bearer {admin_token}
```

### **تسجيل مشاهدة**

```http
POST /analytics/track

{
    "type": "movie",
    "item_id": 1,
    "duration_watched": 3600,
    "quality": "1080p"
}
```

---

## 💬 **Comments API**

### **جلب التعليقات**

```http
GET /movies/{id}/comments
```

### **إضافة تعليق**

```http
POST /movies/{id}/comments
Authorization: Bearer {token}

{
    "content": "تعليق رائع على الفيلم",
    "rating": 5
}
```

---

## 🔔 **Notifications API**

### **جلب الإشعارات**

```http
GET /notifications
Authorization: Bearer {token}
```

### **تحديد إشعار كمقروء**

```http
PUT /notifications/{id}/read
Authorization: Bearer {token}
```

---

## 📱 **Mobile API**

### **تسجيل جهاز للإشعارات**

```http
POST /mobile/register-device
Authorization: Bearer {token}

{
    "device_token": "fcm_token_here",
    "platform": "android",
    "app_version": "1.0.0"
}
```

---

## 🎵 **Playlists API**

### **جلب قوائم التشغيل**

```http
GET /playlists
Authorization: Bearer {token}
```

### **إنشاء قائمة تشغيل**

```http
POST /playlists
Authorization: Bearer {token}

{
    "name": "قائمة الأفلام المفضلة",
    "description": "مجموعة من أفضل الأفلام",
    "is_public": true
}
```

---

## 📈 **Status Codes**

| Code | Description |
|------|-------------|
| 200 | نجح الطلب |
| 201 | تم الإنشاء بنجاح |
| 400 | خطأ في البيانات المرسلة |
| 401 | غير مصرح |
| 403 | ممنوع |
| 404 | غير موجود |
| 422 | خطأ في التحقق |
| 429 | تجاوز الحد المسموح |
| 500 | خطأ في الخادم |

---

## 🚫 **Error Handling**

```json
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "البيانات المرسلة غير صحيحة",
        "details": {
            "email": ["البريد الإلكتروني مطلوب"],
            "password": ["كلمة المرور قصيرة جداً"]
        }
    }
}
```

---

## 🔄 **Rate Limiting**

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

---

## 📝 **Webhooks**

### **تسجيل Webhook**

```http
POST /webhooks
Authorization: Bearer {token}

{
    "url": "https://your-app.com/webhook",
    "events": ["movie.created", "user.registered"],
    "secret": "webhook_secret"
}
```

### **الأحداث المتاحة**
- `movie.created`
- `movie.updated`
- `user.registered`
- `comment.created`
- `rating.added`

---

## 🧪 **Testing**

### **Sandbox Environment**
- **Base URL**: `https://api-sandbox.streaming-platform.com/v1`
- **Test Token**: `test_token_12345`

### **Postman Collection**
[تحميل مجموعة Postman](https://api.streaming-platform.com/postman-collection.json)

---

## 📞 **الدعم**

- **الوثائق التفاعلية**: https://docs.streaming-platform.com
- **GitHub**: https://github.com/streaming-platform/api
- **البريد الإلكتروني**: <EMAIL>
- **Discord**: https://discord.gg/streaming-platform

---

**📚 وثائق API شاملة ومحدثة باستمرار لمنصة البث العربية**

آخر تحديث: 15 يناير 2024
