@echo off
echo ========================================
echo 🗄️ إعداد قاعدة البيانات - منصة البث الشاملة
echo ========================================
echo.

:: التحقق من وجود MySQL
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL غير مثبت أو غير متاح
    echo يرجى تثبيت MySQL أو تشغيل XAMPP
    pause
    exit /b 1
)

echo ✅ تم العثور على MySQL
echo.

:: طلب بيانات الاتصال
set /p DB_HOST="أدخل عنوان الخادم (افتراضي: localhost): "
if "%DB_HOST%"=="" set DB_HOST=localhost

set /p DB_USER="أدخل اسم المستخدم (افتراضي: root): "
if "%DB_USER%"=="" set DB_USER=root

set /p DB_PASS="أدخل كلمة المرور (اتركها فارغة إذا لم تكن موجودة): "

echo.
echo 📋 بيانات الاتصال:
echo الخادم: %DB_HOST%
echo المستخدم: %DB_USER%
echo كلمة المرور: %DB_PASS%
echo.

set /p confirm="هل البيانات صحيحة؟ (y/n): "
if /i "%confirm%" neq "y" (
    echo تم الإلغاء
    pause
    exit /b 1
)

echo.
echo 🔧 إنشاء قاعدة البيانات...
echo ========================================

:: إنشاء قاعدة البيانات
if "%DB_PASS%"=="" (
    mysql -h %DB_HOST% -u %DB_USER% < website/database/schema.sql
) else (
    mysql -h %DB_HOST% -u %DB_USER% -p%DB_PASS% < website/database/schema.sql
)

if %errorlevel% neq 0 (
    echo ❌ فشل في إنشاء قاعدة البيانات
    echo تحقق من بيانات الاتصال وحاول مرة أخرى
    pause
    exit /b 1
)

echo ✅ تم إنشاء قاعدة البيانات بنجاح!
echo.

:: التحقق من وجود بيانات تجريبية
if exist "website/database/sample_data.sql" (
    set /p sample="هل تريد إدراج بيانات تجريبية؟ (y/n): "
    if /i "%sample%"=="y" (
        echo 📊 إدراج البيانات التجريبية...
        if "%DB_PASS%"=="" (
            mysql -h %DB_HOST% -u %DB_USER% streaming_platform < website/database/sample_data.sql
        ) else (
            mysql -h %DB_HOST% -u %DB_USER% -p%DB_PASS% streaming_platform < website/database/sample_data.sql
        )
        
        if %errorlevel% neq 0 (
            echo ⚠️  تحذير: فشل في إدراج البيانات التجريبية
        ) else (
            echo ✅ تم إدراج البيانات التجريبية بنجاح!
        )
    )
)

echo.
echo 📝 تحديث ملف التكوين...
echo ========================================

:: إنشاء ملف .env إذا لم يكن موجوداً
if not exist "website/.env" (
    echo # إعدادات قاعدة البيانات > website/.env
    echo DB_HOST=%DB_HOST% >> website/.env
    echo DB_NAME=streaming_platform >> website/.env
    echo DB_USER=%DB_USER% >> website/.env
    echo DB_PASS=%DB_PASS% >> website/.env
    echo. >> website/.env
    echo # إعدادات الموقع >> website/.env
    echo SITE_URL=http://localhost/streaming_platform >> website/.env
    echo SITE_NAME=منصة البث الشاملة >> website/.env
    echo. >> website/.env
    echo # إعدادات الأمان >> website/.env
    echo JWT_SECRET=your_jwt_secret_key_here >> website/.env
    echo ENCRYPTION_KEY=your_encryption_key_here >> website/.env
    
    echo ✅ تم إنشاء ملف .env
) else (
    echo ⚠️  ملف .env موجود بالفعل
    echo يرجى تحديث إعدادات قاعدة البيانات يدوياً
)

echo.
echo 🎉 تم إعداد قاعدة البيانات بنجاح!
echo ========================================
echo.
echo 📋 الخطوات التالية:
echo 1. تأكد من تشغيل خادم الويب (Apache)
echo 2. انسخ مجلد website إلى htdocs
echo 3. تصفح http://localhost/streaming_platform
echo 4. شغل التطبيق الجوال باستخدام run_project.bat
echo.

pause
