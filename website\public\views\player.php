<?php
/**
 * 🎬 مشغل الفيديو
 * صفحة مشاهدة الأفلام والمسلسلات
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}

// التحقق من وجود المحتوى
if (!isset($content) || !$content) {
    include 'views/404.php';
    return;
}

// التحقق من إمكانية الوصول
if (!canAccessContent($content)) {
    include 'views/access-denied.php';
    return;
}

// الحصول على معرف الحلقة (للمسلسلات)
$episodeId = isset($_GET['episode']) ? (int)$_GET['episode'] : null;
$currentEpisode = null;
$seasons = [];

if ($content['type'] === 'series') {
    $seasons = getContentSeasons($content['id']);
    
    if ($episodeId) {
        $currentEpisode = getEpisodeById($episodeId);
    } else {
        // الحصول على أول حلقة من أول موسم
        if (!empty($seasons)) {
            $firstSeason = $seasons[0];
            $episodes = getSeasonEpisodes($firstSeason['id']);
            if (!empty($episodes)) {
                $currentEpisode = $episodes[0];
                $episodeId = $currentEpisode['id'];
            }
        }
    }
}

// تحديد مصدر الفيديو
$videoUrl = $currentEpisode ? $currentEpisode['video_url'] : $content['video_url'];
$videoTitle = $currentEpisode ? 
    $content['title'] . ' - الموسم ' . $currentEpisode['season_number'] . ' الحلقة ' . $currentEpisode['episode_number'] :
    $content['title'];

// الحصول على الترجمات والمسارات الصوتية
$subtitles = $content['subtitles'] ? json_decode($content['subtitles'], true) : [];
$audioTracks = $content['audio_tracks'] ? json_decode($content['audio_tracks'], true) : [];

// تسجيل بداية المشاهدة
if (IS_LOGGED_IN) {
    logUserActivity('start_watching', [
        'content_id' => $content['id'],
        'episode_id' => $episodeId,
        'content_title' => $content['title']
    ]);
}
?>

<!DOCTYPE html>
<html lang="<?php echo CURRENT_LANGUAGE; ?>" dir="<?php echo CURRENT_LANGUAGE === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($videoTitle); ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">
    
    <!-- Custom Player CSS -->
    <link rel="stylesheet" href="/assets/css/player.css?v=<?php echo SITE_VERSION; ?>">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: 'Cairo', sans-serif;
            overflow: hidden;
        }
        
        .player-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: #000;
        }
        
        .video-js {
            width: 100%;
            height: 100%;
        }
        
        .player-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 10;
        }
        
        .player-header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to bottom, rgba(0,0,0,0.8), transparent);
            padding: 20px;
            pointer-events: auto;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .player-container:hover .player-header,
        .player-container.controls-visible .player-header {
            opacity: 1;
        }
        
        .player-title {
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
        
        .player-meta {
            color: rgba(255,255,255,0.8);
            font-size: 14px;
            margin-top: 5px;
        }
        
        .back-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.6);
            border: none;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s ease;
            pointer-events: auto;
        }
        
        .back-button:hover {
            background: rgba(0,0,0,0.8);
        }
        
        .episodes-sidebar {
            position: absolute;
            right: -400px;
            top: 0;
            bottom: 0;
            width: 400px;
            background: rgba(0,0,0,0.9);
            backdrop-filter: blur(10px);
            transition: right 0.3s ease;
            overflow-y: auto;
            pointer-events: auto;
            z-index: 20;
        }
        
        .episodes-sidebar.open {
            right: 0;
        }
        
        .episodes-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .episodes-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }
        
        .season-selector {
            margin-top: 10px;
        }
        
        .season-selector select {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            width: 100%;
        }
        
        .episodes-list {
            padding: 0;
        }
        
        .episode-item {
            display: flex;
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.05);
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .episode-item:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .episode-item.current {
            background: rgba(108, 92, 231, 0.3);
        }
        
        .episode-thumbnail {
            width: 80px;
            height: 45px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
            margin-left: 15px;
            overflow: hidden;
        }
        
        .episode-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .episode-info {
            flex: 1;
        }
        
        .episode-title {
            color: white;
            font-size: 14px;
            font-weight: 600;
            margin: 0 0 5px 0;
        }
        
        .episode-meta {
            color: rgba(255,255,255,0.6);
            font-size: 12px;
        }
        
        .episodes-toggle {
            position: absolute;
            bottom: 100px;
            right: 20px;
            background: rgba(0,0,0,0.6);
            border: none;
            color: white;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            transition: background 0.3s ease;
            pointer-events: auto;
            opacity: 0;
            transition: opacity 0.3s ease, background 0.3s ease;
        }
        
        .player-container:hover .episodes-toggle,
        .player-container.controls-visible .episodes-toggle {
            opacity: 1;
        }
        
        .episodes-toggle:hover {
            background: rgba(0,0,0,0.8);
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 30;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255,255,255,0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            text-align: center;
            z-index: 30;
        }
        
        .error-message h3 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .error-message p {
            font-size: 16px;
            opacity: 0.8;
        }
        
        .retry-button {
            background: #6c5ce7;
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
            transition: background 0.3s ease;
        }
        
        .retry-button:hover {
            background: #5a4fcf;
        }
    </style>
</head>
<body>
    <div class="player-container" id="player-container">
        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loading-overlay">
            <div class="loading-spinner"></div>
        </div>
        
        <!-- Video Player -->
        <video
            id="video-player"
            class="video-js vjs-default-skin"
            controls
            preload="auto"
            data-setup='{"fluid": true, "responsive": true}'
            poster="<?php echo $content['banner'] ?? $content['poster']; ?>">
            
            <!-- Video Source -->
            <source src="<?php echo $videoUrl; ?>" type="video/mp4">
            
            <!-- Subtitles -->
            <?php foreach ($subtitles as $index => $subtitle): ?>
            <track kind="subtitles" 
                   src="<?php echo $subtitle['url']; ?>" 
                   srclang="<?php echo $subtitle['language']; ?>" 
                   label="<?php echo $subtitle['label']; ?>"
                   <?php echo $subtitle['is_default'] ? 'default' : ''; ?>>
            <?php endforeach; ?>
            
            <p class="vjs-no-js">
                لمشاهدة هذا الفيديو يرجى تفعيل JavaScript، و
                <a href="https://videojs.com/html5-video-support/" target="_blank">
                    ترقية متصفحك
                </a>
            </p>
        </video>
        
        <!-- Player Overlay -->
        <div class="player-overlay">
            <!-- Header -->
            <div class="player-header">
                <h1 class="player-title"><?php echo htmlspecialchars($videoTitle); ?></h1>
                <div class="player-meta">
                    <?php if ($currentEpisode): ?>
                    الحلقة <?php echo $currentEpisode['episode_number']; ?> - <?php echo htmlspecialchars($currentEpisode['title']); ?>
                    <?php endif; ?>
                    
                    <?php if ($content['duration'] || ($currentEpisode && $currentEpisode['duration'])): ?>
                    • <?php echo $currentEpisode ? $currentEpisode['duration'] : $content['duration']; ?> دقيقة
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Back Button -->
            <button class="back-button" onclick="goBack()">
                <i class="fas fa-arrow-right"></i>
            </button>
            
            <!-- Episodes Toggle (for series) -->
            <?php if ($content['type'] === 'series' && !empty($seasons)): ?>
            <button class="episodes-toggle" onclick="toggleEpisodes()">
                <i class="fas fa-list"></i>
            </button>
            <?php endif; ?>
        </div>
        
        <!-- Episodes Sidebar -->
        <?php if ($content['type'] === 'series' && !empty($seasons)): ?>
        <div class="episodes-sidebar" id="episodes-sidebar">
            <div class="episodes-header">
                <h3 class="episodes-title">الحلقات</h3>
                <div class="season-selector">
                    <select id="season-selector" onchange="loadSeasonEpisodes(this.value)">
                        <?php foreach ($seasons as $season): ?>
                        <option value="<?php echo $season['id']; ?>">
                            الموسم <?php echo $season['season_number']; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            
            <div class="episodes-list" id="episodes-list">
                <!-- Episodes will be loaded here -->
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Error Message -->
        <div class="error-message" id="error-message" style="display: none;">
            <h3>خطأ في تحميل الفيديو</h3>
            <p>حدث خطأ أثناء تحميل الفيديو. يرجى المحاولة مرة أخرى.</p>
            <button class="retry-button" onclick="retryVideo()">إعادة المحاولة</button>
        </div>
    </div>

    <!-- Video.js JavaScript -->
    <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    
    <script>
        // متغيرات عامة
        let player;
        let watchStartTime = Date.now();
        let lastProgressUpdate = 0;
        let progressUpdateInterval;
        let isEpisodesSidebarOpen = false;
        
        // بيانات المحتوى
        const contentData = {
            id: <?php echo $content['id']; ?>,
            type: '<?php echo $content['type']; ?>',
            title: '<?php echo htmlspecialchars($content['title']); ?>',
            currentEpisodeId: <?php echo $episodeId ?? 'null'; ?>,
            seasons: <?php echo json_encode($seasons); ?>
        };
        
        // تهيئة المشغل
        document.addEventListener('DOMContentLoaded', function() {
            initializePlayer();
        });
        
        function initializePlayer() {
            // إخفاء شاشة التحميل
            setTimeout(() => {
                document.getElementById('loading-overlay').style.display = 'none';
            }, 1000);
            
            // تهيئة Video.js
            player = videojs('video-player', {
                controls: true,
                fluid: true,
                responsive: true,
                playbackRates: [0.5, 1, 1.25, 1.5, 2],
                plugins: {
                    hotkeys: {
                        volumeStep: 0.1,
                        seekStep: 10,
                        enableModifiersForNumbers: false
                    }
                }
            });
            
            // أحداث المشغل
            player.ready(function() {
                console.log('Player is ready');
                
                // تحميل آخر موضع مشاهدة
                loadWatchProgress();
                
                // بدء تتبع التقدم
                startProgressTracking();
            });
            
            player.on('play', function() {
                console.log('Video started playing');
                trackEvent('video_play', {
                    content_id: contentData.id,
                    episode_id: contentData.currentEpisodeId
                });
            });
            
            player.on('pause', function() {
                console.log('Video paused');
                updateWatchProgress();
            });
            
            player.on('ended', function() {
                console.log('Video ended');
                handleVideoEnd();
            });
            
            player.on('error', function() {
                console.error('Video error:', player.error());
                showErrorMessage();
            });
            
            // تحميل الحلقات للمسلسلات
            if (contentData.type === 'series' && contentData.seasons.length > 0) {
                loadSeasonEpisodes(contentData.seasons[0].id);
            }
            
            // إخفاء/إظهار عناصر التحكم
            let controlsTimeout;
            const container = document.getElementById('player-container');
            
            container.addEventListener('mousemove', function() {
                container.classList.add('controls-visible');
                clearTimeout(controlsTimeout);
                controlsTimeout = setTimeout(() => {
                    container.classList.remove('controls-visible');
                }, 3000);
            });
            
            // ملء الشاشة عند النقر المزدوج
            container.addEventListener('dblclick', function() {
                if (player.isFullscreen()) {
                    player.exitFullscreen();
                } else {
                    player.requestFullscreen();
                }
            });
        }
        
        function loadWatchProgress() {
            if (!<?php echo IS_LOGGED_IN ? 'true' : 'false'; ?>) return;
            
            fetch('/api/content/progress', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content_id: contentData.id,
                    episode_id: contentData.currentEpisodeId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.progress > 0) {
                    const resumeTime = data.progress;
                    if (resumeTime > 30) { // إذا كان التقدم أكثر من 30 ثانية
                        if (confirm(`هل تريد متابعة المشاهدة من ${formatTime(resumeTime)}؟`)) {
                            player.currentTime(resumeTime);
                        }
                    }
                }
            })
            .catch(error => console.error('Error loading progress:', error));
        }
        
        function startProgressTracking() {
            progressUpdateInterval = setInterval(() => {
                if (player && !player.paused()) {
                    const currentTime = player.currentTime();
                    if (currentTime - lastProgressUpdate >= 30) { // تحديث كل 30 ثانية
                        updateWatchProgress();
                        lastProgressUpdate = currentTime;
                    }
                }
            }, 10000); // فحص كل 10 ثوان
        }
        
        function updateWatchProgress() {
            if (!<?php echo IS_LOGGED_IN ? 'true' : 'false'; ?> || !player) return;
            
            const currentTime = player.currentTime();
            const duration = player.duration();
            
            if (currentTime > 0 && duration > 0) {
                fetch('/api/content/progress', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        content_id: contentData.id,
                        episode_id: contentData.currentEpisodeId,
                        watch_time: Math.floor(currentTime),
                        total_duration: Math.floor(duration)
                    })
                })
                .catch(error => console.error('Error updating progress:', error));
            }
        }
        
        function handleVideoEnd() {
            updateWatchProgress();
            
            // تتبع إكمال المشاهدة
            trackEvent('video_complete', {
                content_id: contentData.id,
                episode_id: contentData.currentEpisodeId,
                watch_duration: Date.now() - watchStartTime
            });
            
            // للمسلسلات: اقتراح الحلقة التالية
            if (contentData.type === 'series') {
                suggestNextEpisode();
            }
        }
        
        function suggestNextEpisode() {
            // TODO: تنفيذ اقتراح الحلقة التالية
            console.log('Suggesting next episode...');
        }
        
        function loadSeasonEpisodes(seasonId) {
            fetch(`/api/content/episodes?season_id=${seasonId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayEpisodes(data.episodes);
                    }
                })
                .catch(error => console.error('Error loading episodes:', error));
        }
        
        function displayEpisodes(episodes) {
            const episodesList = document.getElementById('episodes-list');
            episodesList.innerHTML = '';
            
            episodes.forEach(episode => {
                const episodeItem = document.createElement('div');
                episodeItem.className = 'episode-item';
                if (episode.id === contentData.currentEpisodeId) {
                    episodeItem.classList.add('current');
                }
                
                episodeItem.innerHTML = `
                    <div class="episode-thumbnail">
                        <img src="${episode.thumbnail || '/assets/images/default-episode.jpg'}" 
                             alt="الحلقة ${episode.episode_number}">
                    </div>
                    <div class="episode-info">
                        <div class="episode-title">
                            الحلقة ${episode.episode_number}: ${episode.title}
                        </div>
                        <div class="episode-meta">
                            ${episode.duration ? episode.duration + ' دقيقة' : ''} • 
                            ${episode.view_count} مشاهدة
                        </div>
                    </div>
                `;
                
                episodeItem.addEventListener('click', () => {
                    playEpisode(episode.id);
                });
                
                episodesList.appendChild(episodeItem);
            });
        }
        
        function playEpisode(episodeId) {
            window.location.href = `/player/${contentData.id}?episode=${episodeId}`;
        }
        
        function toggleEpisodes() {
            const sidebar = document.getElementById('episodes-sidebar');
            isEpisodesSidebarOpen = !isEpisodesSidebarOpen;
            
            if (isEpisodesSidebarOpen) {
                sidebar.classList.add('open');
            } else {
                sidebar.classList.remove('open');
            }
        }
        
        function showErrorMessage() {
            document.getElementById('error-message').style.display = 'block';
        }
        
        function retryVideo() {
            document.getElementById('error-message').style.display = 'none';
            player.load();
        }
        
        function goBack() {
            if (document.referrer) {
                window.history.back();
            } else {
                window.location.href = `/content/${contentData.id}`;
            }
        }
        
        function formatTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);
            
            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else {
                return `${minutes}:${secs.toString().padStart(2, '0')}`;
            }
        }
        
        function trackEvent(eventName, data) {
            fetch('/api/analytics/track', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    event: eventName,
                    data: data
                })
            }).catch(error => console.error('Error tracking event:', error));
        }
        
        // تنظيف الموارد عند مغادرة الصفحة
        window.addEventListener('beforeunload', function() {
            if (progressUpdateInterval) {
                clearInterval(progressUpdateInterval);
            }
            updateWatchProgress();
        });
        
        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            if (!player) return;
            
            switch(e.code) {
                case 'Space':
                    e.preventDefault();
                    if (player.paused()) {
                        player.play();
                    } else {
                        player.pause();
                    }
                    break;
                    
                case 'ArrowLeft':
                    e.preventDefault();
                    player.currentTime(player.currentTime() - 10);
                    break;
                    
                case 'ArrowRight':
                    e.preventDefault();
                    player.currentTime(player.currentTime() + 10);
                    break;
                    
                case 'ArrowUp':
                    e.preventDefault();
                    player.volume(Math.min(1, player.volume() + 0.1));
                    break;
                    
                case 'ArrowDown':
                    e.preventDefault();
                    player.volume(Math.max(0, player.volume() - 0.1));
                    break;
                    
                case 'KeyF':
                    e.preventDefault();
                    if (player.isFullscreen()) {
                        player.exitFullscreen();
                    } else {
                        player.requestFullscreen();
                    }
                    break;
                    
                case 'KeyM':
                    e.preventDefault();
                    player.muted(!player.muted());
                    break;
                    
                case 'Escape':
                    if (isEpisodesSidebarOpen) {
                        toggleEpisodes();
                    }
                    break;
            }
        });
    </script>
</body>
</html>

<?php
/**
 * دوال مساعدة لمشغل الفيديو
 */

function getEpisodeById($episodeId) {
    global $db;
    return $db->selectOne(
        "SELECT e.*, s.season_number 
         FROM " . DB_PREFIX . "episodes e
         JOIN " . DB_PREFIX . "seasons s ON e.season_id = s.id
         WHERE e.id = ?",
        [$episodeId]
    );
}
?>
