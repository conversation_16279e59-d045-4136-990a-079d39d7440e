# 📺 دليل البث المباشر - منصة البث العربية

## 🎯 **نظرة عامة**

هذا الدليل يوضح كيفية تطبيق نظام البث المباشر في منصة البث العربية، بما في ذلك البث التفاعلي، الدردشة المباشرة، وإدارة القنوات.

### **ميزات البث المباشر**
- **البث عالي الجودة**: دعم 4K و HDR
- **زمن استجابة منخفض**: أقل من 3 ثوانٍ
- **التفاعل المباشر**: دردشة وتفاعل فوري
- **البث متعدد المنصات**: توزيع على عدة منصات
- **التسجيل التلقائي**: حفظ البث للمشاهدة لاحقاً

---

## 🏗️ **بنية نظام البث المباشر**

### **Architecture Overview**

```
┌─────────────────────────────────────────────────────────────┐
│                    Live Streaming Architecture              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  Content Creators                       │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│  │  │   OBS/RTMP  │ │   Mobile    │ │   Browser   │       │ │
│  │  │   Encoder   │ │    App      │ │   WebRTC    │       │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘       │ │
│  └─────────────────────┬───────────────────────────────────┘ │
│                        │                                     │
│  ┌─────────────────────┴───────────────────────────────────┐ │
│  │                 Ingestion Layer                         │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│  │  │    RTMP     │ │    WebRTC   │ │     SRT     │       │ │
│  │  │   Server    │ │   Gateway   │ │   Server    │       │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘       │ │
│  └─────────────────────┬───────────────────────────────────┘ │
│                        │                                     │
│  ┌─────────────────────┴───────────────────────────────────┐ │
│  │                Processing Layer                         │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│  │  │  Transcoder │ │  Packager   │ │   Origin    │       │ │
│  │  │   (FFmpeg)  │ │   (HLS/DASH)│ │   Server    │       │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘       │ │
│  └─────────────────────┬───────────────────────────────────┘ │
│                        │                                     │
│  ┌─────────────────────┴───────────────────────────────────┐ │
│  │                Distribution Layer                       │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│  │  │     CDN     │ │   Edge      │ │   Cache     │       │ │
│  │  │  (CloudFlare)│ │  Servers    │ │  Servers    │       │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘       │ │
│  └─────────────────────┬───────────────────────────────────┘ │
│                        │                                     │
│  ┌─────────────────────┴───────────────────────────────────┐ │
│  │                   Viewers                               │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│  │  │   Web       │ │   Mobile    │ │   Smart TV  │       │ │
│  │  │  Players    │ │    Apps     │ │    Apps     │       │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 📡 **RTMP Ingestion Server**

### **Node.js RTMP Server**

```javascript
// live-streaming/rtmp-server/server.js
const NodeMediaServer = require('node-media-server');
const express = require('express');
const mysql = require('mysql2/promise');
const redis = require('redis');
const WebSocket = require('ws');

class LiveStreamingServer {
    constructor() {
        this.app = express();
        this.redisClient = redis.createClient();
        this.wsServer = new WebSocket.Server({ port: 8080 });
        this.activeStreams = new Map();
        
        this.nms = new NodeMediaServer({
            rtmp: {
                port: 1935,
                chunk_size: 60000,
                gop_cache: true,
                ping: 30,
                ping_timeout: 60
            },
            http: {
                port: 8000,
                mediaroot: './media',
                allow_origin: '*'
            },
            auth: {
                play: false,
                publish: true,
                secret: process.env.RTMP_SECRET
            }
        });
        
        this.setupEventHandlers();
    }
    
    setupEventHandlers() {
        // معالج بداية البث
        this.nms.on('prePublish', async (id, StreamPath, args) => {
            console.log('[NodeEvent on prePublish]', `id=${id} StreamPath=${StreamPath} args=${JSON.stringify(args)}`);
            
            // التحقق من صحة المفتاح
            const streamKey = this.getStreamKeyFromPath(StreamPath);
            const isValid = await this.validateStreamKey(streamKey);
            
            if (!isValid) {
                console.log('Invalid stream key:', streamKey);
                return false;
            }
            
            // تسجيل بداية البث
            await this.startStream(streamKey, id);
            
            return true;
        });
        
        // معالج انتهاء البث
        this.nms.on('donePublish', async (id, StreamPath, args) => {
            console.log('[NodeEvent on donePublish]', `id=${id} StreamPath=${StreamPath}`);
            
            const streamKey = this.getStreamKeyFromPath(StreamPath);
            await this.endStream(streamKey, id);
        });
        
        // معالج بداية المشاهدة
        this.nms.on('prePlay', async (id, StreamPath, args) => {
            console.log('[NodeEvent on prePlay]', `id=${id} StreamPath=${StreamPath}`);
            
            const streamKey = this.getStreamKeyFromPath(StreamPath);
            await this.addViewer(streamKey, id);
        });
        
        // معالج انتهاء المشاهدة
        this.nms.on('donePlay', async (id, StreamPath, args) => {
            console.log('[NodeEvent on donePlay]', `id=${id} StreamPath=${StreamPath}`);
            
            const streamKey = this.getStreamKeyFromPath(StreamPath);
            await this.removeViewer(streamKey, id);
        });
    }
    
    getStreamKeyFromPath(streamPath) {
        // استخراج مفتاح البث من المسار
        // مثال: /live/abc123 -> abc123
        return streamPath.split('/').pop();
    }
    
    async validateStreamKey(streamKey) {
        try {
            // التحقق من قاعدة البيانات
            const connection = await mysql.createConnection({
                host: process.env.DB_HOST,
                user: process.env.DB_USER,
                password: process.env.DB_PASSWORD,
                database: process.env.DB_NAME
            });
            
            const [rows] = await connection.execute(
                'SELECT id, user_id, status FROM live_streams WHERE stream_key = ? AND status = "active"',
                [streamKey]
            );
            
            await connection.end();
            
            return rows.length > 0;
        } catch (error) {
            console.error('Error validating stream key:', error);
            return false;
        }
    }
    
    async startStream(streamKey, sessionId) {
        try {
            // تحديث حالة البث في قاعدة البيانات
            const connection = await mysql.createConnection({
                host: process.env.DB_HOST,
                user: process.env.DB_USER,
                password: process.env.DB_PASSWORD,
                database: process.env.DB_NAME
            });
            
            await connection.execute(
                'UPDATE live_streams SET status = "live", started_at = NOW(), session_id = ? WHERE stream_key = ?',
                [sessionId, streamKey]
            );
            
            // حفظ معلومات البث في Redis
            await this.redisClient.hSet(`stream:${streamKey}`, {
                status: 'live',
                session_id: sessionId,
                started_at: new Date().toISOString(),
                viewers: 0
            });
            
            // إشعار المشتركين
            this.notifyStreamStart(streamKey);
            
            // بدء عملية التحويل والتوزيع
            this.startTranscoding(streamKey);
            
            await connection.end();
            
        } catch (error) {
            console.error('Error starting stream:', error);
        }
    }
    
    async endStream(streamKey, sessionId) {
        try {
            const connection = await mysql.createConnection({
                host: process.env.DB_HOST,
                user: process.env.DB_USER,
                password: process.env.DB_PASSWORD,
                database: process.env.DB_NAME
            });
            
            // تحديث حالة البث
            await connection.execute(
                'UPDATE live_streams SET status = "ended", ended_at = NOW() WHERE stream_key = ?',
                [streamKey]
            );
            
            // حذف من Redis
            await this.redisClient.del(`stream:${streamKey}`);
            
            // إشعار انتهاء البث
            this.notifyStreamEnd(streamKey);
            
            // إيقاف التحويل
            this.stopTranscoding(streamKey);
            
            await connection.end();
            
        } catch (error) {
            console.error('Error ending stream:', error);
        }
    }
    
    startTranscoding(streamKey) {
        const { spawn } = require('child_process');
        
        // إعدادات FFmpeg للتحويل متعدد الجودات
        const ffmpegArgs = [
            '-i', `rtmp://localhost:1935/live/${streamKey}`,
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-preset', 'veryfast',
            '-tune', 'zerolatency',
            '-f', 'flv',
            
            // جودة 1080p
            '-s:v:0', '1920x1080',
            '-b:v:0', '5000k',
            '-maxrate:v:0', '5350k',
            '-bufsize:v:0', '7500k',
            
            // جودة 720p
            '-s:v:1', '1280x720',
            '-b:v:1', '3000k',
            '-maxrate:v:1', '3200k',
            '-bufsize:v:1', '4500k',
            
            // جودة 480p
            '-s:v:2', '854x480',
            '-b:v:2', '1500k',
            '-maxrate:v:2', '1600k',
            '-bufsize:v:2', '2250k',
            
            // إخراج HLS
            '-f', 'hls',
            '-hls_time', '2',
            '-hls_list_size', '3',
            '-hls_flags', 'delete_segments',
            `./media/live/${streamKey}/playlist.m3u8`
        ];
        
        const ffmpeg = spawn('ffmpeg', ffmpegArgs);
        
        ffmpeg.stdout.on('data', (data) => {
            console.log(`FFmpeg stdout: ${data}`);
        });
        
        ffmpeg.stderr.on('data', (data) => {
            console.error(`FFmpeg stderr: ${data}`);
        });
        
        ffmpeg.on('close', (code) => {
            console.log(`FFmpeg process exited with code ${code}`);
        });
        
        // حفظ معرف العملية
        this.activeStreams.set(streamKey, ffmpeg);
    }
    
    stopTranscoding(streamKey) {
        const ffmpeg = this.activeStreams.get(streamKey);
        if (ffmpeg) {
            ffmpeg.kill('SIGTERM');
            this.activeStreams.delete(streamKey);
        }
    }
    
    notifyStreamStart(streamKey) {
        // إرسال إشعار عبر WebSocket
        const message = JSON.stringify({
            type: 'stream_started',
            stream_key: streamKey,
            timestamp: new Date().toISOString()
        });
        
        this.wsServer.clients.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
                client.send(message);
            }
        });
    }
    
    notifyStreamEnd(streamKey) {
        const message = JSON.stringify({
            type: 'stream_ended',
            stream_key: streamKey,
            timestamp: new Date().toISOString()
        });
        
        this.wsServer.clients.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
                client.send(message);
            }
        });
    }
    
    async addViewer(streamKey, viewerId) {
        await this.redisClient.hIncrBy(`stream:${streamKey}`, 'viewers', 1);
        
        // إشعار تحديث عدد المشاهدين
        const viewers = await this.redisClient.hGet(`stream:${streamKey}`, 'viewers');
        this.broadcastViewerCount(streamKey, parseInt(viewers));
    }
    
    async removeViewer(streamKey, viewerId) {
        await this.redisClient.hIncrBy(`stream:${streamKey}`, 'viewers', -1);
        
        const viewers = await this.redisClient.hGet(`stream:${streamKey}`, 'viewers');
        this.broadcastViewerCount(streamKey, Math.max(0, parseInt(viewers)));
    }
    
    broadcastViewerCount(streamKey, count) {
        const message = JSON.stringify({
            type: 'viewer_count_update',
            stream_key: streamKey,
            viewers: count
        });
        
        this.wsServer.clients.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
                client.send(message);
            }
        });
    }
    
    start() {
        this.nms.run();
        this.app.listen(3000, () => {
            console.log('Live Streaming Server started on port 3000');
        });
        
        console.log('RTMP Server started on port 1935');
        console.log('WebSocket Server started on port 8080');
    }
}

// تشغيل الخادم
const server = new LiveStreamingServer();
server.start();
```

---

## 💬 **نظام الدردشة المباشرة**

### **Real-time Chat System**

```javascript
// live-streaming/chat/chat-server.js
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const redis = require('redis');
const jwt = require('jsonwebtoken');

class LiveChatServer {
    constructor() {
        this.app = express();
        this.server = http.createServer(this.app);
        this.io = socketIo(this.server, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"]
            }
        });
        
        this.redisClient = redis.createClient();
        this.chatRooms = new Map();
        this.userSockets = new Map();
        
        this.setupMiddleware();
        this.setupSocketHandlers();
    }
    
    setupMiddleware() {
        // التحقق من JWT token
        this.io.use(async (socket, next) => {
            try {
                const token = socket.handshake.auth.token;
                const decoded = jwt.verify(token, process.env.JWT_SECRET);
                
                socket.userId = decoded.sub;
                socket.username = decoded.username;
                socket.userRole = decoded.role || 'viewer';
                
                next();
            } catch (error) {
                next(new Error('Authentication error'));
            }
        });
    }
    
    setupSocketHandlers() {
        this.io.on('connection', (socket) => {
            console.log(`User ${socket.username} connected`);
            
            // حفظ معلومات المستخدم
            this.userSockets.set(socket.userId, socket);
            
            // الانضمام لغرفة البث
            socket.on('join_stream', async (streamKey) => {
                await this.joinStreamChat(socket, streamKey);
            });
            
            // إرسال رسالة
            socket.on('send_message', async (data) => {
                await this.handleMessage(socket, data);
            });
            
            // تفاعلات (إعجاب، قلب، إلخ)
            socket.on('send_reaction', async (data) => {
                await this.handleReaction(socket, data);
            });
            
            // إدارة المشرفين
            socket.on('moderate_message', async (data) => {
                await this.handleModeration(socket, data);
            });
            
            // قطع الاتصال
            socket.on('disconnect', () => {
                this.handleDisconnect(socket);
            });
        });
    }
    
    async joinStreamChat(socket, streamKey) {
        try {
            // التحقق من وجود البث
            const streamExists = await this.redisClient.exists(`stream:${streamKey}`);
            if (!streamExists) {
                socket.emit('error', { message: 'البث غير موجود' });
                return;
            }
            
            // الانضمام للغرفة
            socket.join(`stream:${streamKey}`);
            socket.currentStream = streamKey;
            
            // تحديث عدد المشاهدين في الدردشة
            const chatRoom = this.getChatRoom(streamKey);
            chatRoom.viewers.add(socket.userId);
            
            // إرسال رسائل الترحيب
            socket.emit('joined_stream', {
                streamKey: streamKey,
                message: 'تم الانضمام للدردشة بنجاح'
            });
            
            // إرسال آخر الرسائل
            const recentMessages = await this.getRecentMessages(streamKey);
            socket.emit('recent_messages', recentMessages);
            
            // إشعار الآخرين بالانضمام
            socket.to(`stream:${streamKey}`).emit('user_joined', {
                username: socket.username,
                userId: socket.userId
            });
            
        } catch (error) {
            console.error('Error joining stream chat:', error);
            socket.emit('error', { message: 'خطأ في الانضمام للدردشة' });
        }
    }
    
    async handleMessage(socket, data) {
        try {
            const { streamKey, message, type = 'text' } = data;
            
            // التحقق من صحة البيانات
            if (!streamKey || !message) {
                socket.emit('error', { message: 'بيانات غير صحيحة' });
                return;
            }
            
            // فلترة المحتوى
            const filteredMessage = await this.filterMessage(message);
            if (!filteredMessage) {
                socket.emit('message_blocked', { reason: 'محتوى غير مناسب' });
                return;
            }
            
            // إنشاء كائن الرسالة
            const messageObj = {
                id: this.generateMessageId(),
                userId: socket.userId,
                username: socket.username,
                message: filteredMessage,
                type: type,
                timestamp: new Date().toISOString(),
                streamKey: streamKey
            };
            
            // حفظ الرسالة
            await this.saveMessage(messageObj);
            
            // إرسال الرسالة لجميع المشاهدين
            this.io.to(`stream:${streamKey}`).emit('new_message', messageObj);
            
            // تحديث إحصائيات الدردشة
            await this.updateChatStats(streamKey);
            
        } catch (error) {
            console.error('Error handling message:', error);
            socket.emit('error', { message: 'خطأ في إرسال الرسالة' });
        }
    }
    
    async handleReaction(socket, data) {
        const { streamKey, type, targetMessageId } = data;
        
        const reaction = {
            id: this.generateReactionId(),
            userId: socket.userId,
            username: socket.username,
            type: type, // like, love, laugh, wow, sad, angry
            targetMessageId: targetMessageId,
            timestamp: new Date().toISOString(),
            streamKey: streamKey
        };
        
        // حفظ التفاعل
        await this.saveReaction(reaction);
        
        // إرسال التفاعل للجميع
        this.io.to(`stream:${streamKey}`).emit('new_reaction', reaction);
    }
    
    async handleModeration(socket, data) {
        // التحقق من صلاحيات الإدارة
        if (socket.userRole !== 'moderator' && socket.userRole !== 'admin') {
            socket.emit('error', { message: 'ليس لديك صلاحيات إدارة' });
            return;
        }
        
        const { action, targetUserId, targetMessageId, reason } = data;
        
        switch (action) {
            case 'delete_message':
                await this.deleteMessage(targetMessageId);
                this.io.to(`stream:${data.streamKey}`).emit('message_deleted', {
                    messageId: targetMessageId,
                    moderator: socket.username
                });
                break;
                
            case 'timeout_user':
                await this.timeoutUser(targetUserId, data.duration || 300); // 5 دقائق افتراضي
                this.io.to(`stream:${data.streamKey}`).emit('user_timeout', {
                    userId: targetUserId,
                    duration: data.duration,
                    moderator: socket.username,
                    reason: reason
                });
                break;
                
            case 'ban_user':
                await this.banUser(targetUserId, data.streamKey);
                this.io.to(`stream:${data.streamKey}`).emit('user_banned', {
                    userId: targetUserId,
                    moderator: socket.username,
                    reason: reason
                });
                break;
        }
    }
    
    async filterMessage(message) {
        // قائمة الكلمات المحظورة
        const bannedWords = await this.getBannedWords();
        
        let filteredMessage = message;
        
        for (const word of bannedWords) {
            const regex = new RegExp(word, 'gi');
            filteredMessage = filteredMessage.replace(regex, '*'.repeat(word.length));
        }
        
        // فحص الرسائل المكررة
        if (await this.isSpam(message)) {
            return null;
        }
        
        return filteredMessage;
    }
    
    async saveMessage(messageObj) {
        // حفظ في Redis للوصول السريع
        const messageKey = `chat:${messageObj.streamKey}:messages`;
        await this.redisClient.lPush(messageKey, JSON.stringify(messageObj));
        await this.redisClient.lTrim(messageKey, 0, 99); // الاحتفاظ بآخر 100 رسالة
        
        // حفظ في قاعدة البيانات للأرشفة
        // يمكن تنفيذ هذا في الخلفية
    }
    
    async getRecentMessages(streamKey, limit = 50) {
        const messageKey = `chat:${streamKey}:messages`;
        const messages = await this.redisClient.lRange(messageKey, 0, limit - 1);
        
        return messages.map(msg => JSON.parse(msg)).reverse();
    }
    
    getChatRoom(streamKey) {
        if (!this.chatRooms.has(streamKey)) {
            this.chatRooms.set(streamKey, {
                viewers: new Set(),
                moderators: new Set(),
                bannedUsers: new Set(),
                settings: {
                    slowMode: false,
                    subscribersOnly: false,
                    emotesOnly: false
                }
            });
        }
        
        return this.chatRooms.get(streamKey);
    }
    
    generateMessageId() {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    generateReactionId() {
        return `react_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    handleDisconnect(socket) {
        console.log(`User ${socket.username} disconnected`);
        
        // إزالة من قائمة المستخدمين المتصلين
        this.userSockets.delete(socket.userId);
        
        // إزالة من غرفة الدردشة
        if (socket.currentStream) {
            const chatRoom = this.getChatRoom(socket.currentStream);
            chatRoom.viewers.delete(socket.userId);
            
            // إشعار الآخرين بالمغادرة
            socket.to(`stream:${socket.currentStream}`).emit('user_left', {
                username: socket.username,
                userId: socket.userId
            });
        }
    }
    
    start(port = 3001) {
        this.server.listen(port, () => {
            console.log(`Live Chat Server started on port ${port}`);
        });
    }
}

// تشغيل خادم الدردشة
const chatServer = new LiveChatServer();
chatServer.start();
```

---

## 🎮 **واجهة البث للمنتجين**

### **Streaming Dashboard**

```vue
<!-- live-streaming/dashboard/StreamingDashboard.vue -->
<template>
  <div class="streaming-dashboard">
    <div class="dashboard-header">
      <h1>لوحة تحكم البث المباشر</h1>
      <div class="stream-status" :class="streamStatus">
        <span class="status-indicator"></span>
        {{ streamStatusText }}
      </div>
    </div>

    <div class="dashboard-content">
      <!-- معلومات البث -->
      <div class="stream-info-card">
        <h3>معلومات البث</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>عنوان البث:</label>
            <input v-model="streamTitle" :disabled="isLive" />
          </div>
          <div class="info-item">
            <label>الوصف:</label>
            <textarea v-model="streamDescription" :disabled="isLive"></textarea>
          </div>
          <div class="info-item">
            <label>التصنيف:</label>
            <select v-model="streamCategory" :disabled="isLive">
              <option value="gaming">ألعاب</option>
              <option value="music">موسيقى</option>
              <option value="talk">حديث</option>
              <option value="education">تعليم</option>
            </select>
          </div>
        </div>
      </div>

      <!-- إعدادات البث -->
      <div class="stream-settings-card">
        <h3>إعدادات البث</h3>
        <div class="settings-grid">
          <div class="setting-item">
            <label>الجودة:</label>
            <select v-model="streamQuality">
              <option value="1080p">1080p</option>
              <option value="720p">720p</option>
              <option value="480p">480p</option>
            </select>
          </div>
          <div class="setting-item">
            <label>معدل الإطارات:</label>
            <select v-model="frameRate">
              <option value="60">60 FPS</option>
              <option value="30">30 FPS</option>
            </select>
          </div>
          <div class="setting-item">
            <label>معدل البت:</label>
            <input v-model="bitrate" type="number" min="1000" max="8000" />
            <span>kbps</span>
          </div>
        </div>
      </div>

      <!-- إحصائيات مباشرة -->
      <div class="live-stats-card">
        <h3>الإحصائيات المباشرة</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ viewerCount }}</div>
            <div class="stat-label">مشاهد</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ chatMessageCount }}</div>
            <div class="stat-label">رسالة دردشة</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ streamDuration }}</div>
            <div class="stat-label">مدة البث</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ averageBitrate }}</div>
            <div class="stat-label">متوسط البت</div>
          </div>
        </div>
      </div>

      <!-- أدوات التحكم -->
      <div class="stream-controls-card">
        <h3>أدوات التحكم</h3>
        <div class="controls-section">
          <button 
            @click="toggleStream" 
            :class="['control-btn', isLive ? 'stop-btn' : 'start-btn']"
            :disabled="isLoading"
          >
            {{ isLive ? 'إيقاف البث' : 'بدء البث' }}
          </button>
          
          <button @click="toggleRecording" :class="['control-btn', isRecording ? 'recording' : '']">
            {{ isRecording ? 'إيقاف التسجيل' : 'بدء التسجيل' }}
          </button>
          
          <button @click="takeScreenshot" class="control-btn">
            لقطة شاشة
          </button>
          
          <button @click="shareStream" class="control-btn">
            مشاركة البث
          </button>
        </div>
      </div>

      <!-- دردشة مباشرة -->
      <div class="live-chat-card">
        <h3>الدردشة المباشرة</h3>
        <div class="chat-container">
          <div class="chat-messages" ref="chatMessages">
            <div 
              v-for="message in chatMessages" 
              :key="message.id"
              class="chat-message"
              :class="{ 'highlighted': message.isHighlighted }"
            >
              <span class="username" :style="{ color: message.userColor }">
                {{ message.username }}:
              </span>
              <span class="message-text">{{ message.text }}</span>
              <div class="message-actions" v-if="canModerate">
                <button @click="deleteMessage(message.id)" class="delete-btn">حذف</button>
                <button @click="timeoutUser(message.userId)" class="timeout-btn">إسكات</button>
              </div>
            </div>
          </div>
          
          <div class="chat-input-section">
            <input 
              v-model="chatInput" 
              @keyup.enter="sendChatMessage"
              placeholder="اكتب رسالة..."
              class="chat-input"
            />
            <button @click="sendChatMessage" class="send-btn">إرسال</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue';
import { io } from 'socket.io-client';

export default {
  name: 'StreamingDashboard',
  setup() {
    // البيانات التفاعلية
    const streamTitle = ref('');
    const streamDescription = ref('');
    const streamCategory = ref('gaming');
    const streamQuality = ref('1080p');
    const frameRate = ref(60);
    const bitrate = ref(5000);
    
    const isLive = ref(false);
    const isRecording = ref(false);
    const isLoading = ref(false);
    const viewerCount = ref(0);
    const chatMessageCount = ref(0);
    const streamDuration = ref('00:00:00');
    const averageBitrate = ref(0);
    
    const chatMessages = ref([]);
    const chatInput = ref('');
    const canModerate = ref(true);
    
    let socket = null;
    let streamStartTime = null;
    let durationInterval = null;
    
    // الحالات المحسوبة
    const streamStatus = computed(() => {
      if (isLive.value) return 'live';
      if (isLoading.value) return 'loading';
      return 'offline';
    });
    
    const streamStatusText = computed(() => {
      if (isLive.value) return 'مباشر';
      if (isLoading.value) return 'جاري التحميل...';
      return 'غير متصل';
    });
    
    // الوظائف
    const initializeSocket = () => {
      socket = io('ws://localhost:3001', {
        auth: {
          token: localStorage.getItem('auth_token')
        }
      });
      
      socket.on('connect', () => {
        console.log('Connected to chat server');
      });
      
      socket.on('new_message', (message) => {
        chatMessages.value.push(message);
        chatMessageCount.value++;
        scrollChatToBottom();
      });
      
      socket.on('viewer_count_update', (data) => {
        viewerCount.value = data.viewers;
      });
      
      socket.on('stream_stats', (stats) => {
        averageBitrate.value = stats.bitrate;
      });
    };
    
    const toggleStream = async () => {
      isLoading.value = true;
      
      try {
        if (isLive.value) {
          await stopStream();
        } else {
          await startStream();
        }
      } catch (error) {
        console.error('Error toggling stream:', error);
        alert('حدث خطأ في تشغيل/إيقاف البث');
      } finally {
        isLoading.value = false;
      }
    };
    
    const startStream = async () => {
      // إرسال طلب بدء البث للخادم
      const response = await fetch('/api/streams/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({
          title: streamTitle.value,
          description: streamDescription.value,
          category: streamCategory.value,
          quality: streamQuality.value,
          frameRate: frameRate.value,
          bitrate: bitrate.value
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        isLive.value = true;
        streamStartTime = new Date();
        startDurationTimer();
        
        // الانضمام لغرفة الدردشة
        socket.emit('join_stream', data.streamKey);
      } else {
        throw new Error('Failed to start stream');
      }
    };
    
    const stopStream = async () => {
      const response = await fetch('/api/streams/stop', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });
      
      if (response.ok) {
        isLive.value = false;
        stopDurationTimer();
        streamDuration.value = '00:00:00';
      } else {
        throw new Error('Failed to stop stream');
      }
    };
    
    const toggleRecording = () => {
      isRecording.value = !isRecording.value;
      // تنفيذ منطق التسجيل
    };
    
    const takeScreenshot = () => {
      // تنفيذ منطق لقطة الشاشة
      console.log('Taking screenshot...');
    };
    
    const shareStream = () => {
      // تنفيذ منطق مشاركة البث
      const streamUrl = `${window.location.origin}/live/${streamKey}`;
      navigator.clipboard.writeText(streamUrl);
      alert('تم نسخ رابط البث');
    };
    
    const sendChatMessage = () => {
      if (chatInput.value.trim() && socket) {
        socket.emit('send_message', {
          streamKey: 'current_stream_key', // يجب الحصول عليه من البيانات
          message: chatInput.value,
          type: 'text'
        });
        chatInput.value = '';
      }
    };
    
    const deleteMessage = (messageId) => {
      if (socket) {
        socket.emit('moderate_message', {
          action: 'delete_message',
          targetMessageId: messageId,
          streamKey: 'current_stream_key'
        });
      }
    };
    
    const timeoutUser = (userId) => {
      if (socket) {
        socket.emit('moderate_message', {
          action: 'timeout_user',
          targetUserId: userId,
          duration: 300, // 5 دقائق
          streamKey: 'current_stream_key'
        });
      }
    };
    
    const startDurationTimer = () => {
      durationInterval = setInterval(() => {
        if (streamStartTime) {
          const now = new Date();
          const diff = now - streamStartTime;
          const hours = Math.floor(diff / 3600000);
          const minutes = Math.floor((diff % 3600000) / 60000);
          const seconds = Math.floor((diff % 60000) / 1000);
          
          streamDuration.value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
      }, 1000);
    };
    
    const stopDurationTimer = () => {
      if (durationInterval) {
        clearInterval(durationInterval);
        durationInterval = null;
      }
    };
    
    const scrollChatToBottom = () => {
      setTimeout(() => {
        const chatContainer = document.querySelector('.chat-messages');
        if (chatContainer) {
          chatContainer.scrollTop = chatContainer.scrollHeight;
        }
      }, 100);
    };
    
    // دورة الحياة
    onMounted(() => {
      initializeSocket();
    });
    
    onUnmounted(() => {
      if (socket) {
        socket.disconnect();
      }
      stopDurationTimer();
    });
    
    return {
      // البيانات
      streamTitle,
      streamDescription,
      streamCategory,
      streamQuality,
      frameRate,
      bitrate,
      isLive,
      isRecording,
      isLoading,
      viewerCount,
      chatMessageCount,
      streamDuration,
      averageBitrate,
      chatMessages,
      chatInput,
      canModerate,
      
      // الحالات المحسوبة
      streamStatus,
      streamStatusText,
      
      // الوظائف
      toggleStream,
      toggleRecording,
      takeScreenshot,
      shareStream,
      sendChatMessage,
      deleteMessage,
      timeoutUser
    };
  }
};
</script>

<style scoped>
.streaming-dashboard {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  direction: rtl;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e0e0e0;
}

.stream-status {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 18px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-left: 8px;
}

.stream-status.live .status-indicator {
  background-color: #ff4444;
  animation: pulse 2s infinite;
}

.stream-status.loading .status-indicator {
  background-color: #ffaa00;
}

.stream-status.offline .status-indicator {
  background-color: #888888;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.dashboard-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.stream-info-card,
.stream-settings-card,
.live-stats-card,
.stream-controls-card,
.live-chat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.live-chat-card {
  grid-column: 1 / -1;
}

.info-grid,
.settings-grid,
.stats-grid {
  display: grid;
  gap: 15px;
}

.info-grid {
  grid-template-columns: 1fr;
}

.settings-grid {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.stats-grid {
  grid-template-columns: repeat(4, 1fr);
}

.info-item,
.setting-item {
  display: flex;
  flex-direction: column;
}

.info-item label,
.setting-item label {
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
}

.info-item input,
.info-item textarea,
.info-item select,
.setting-item input,
.setting-item select {
  padding: 10px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.controls-section {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.control-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.start-btn {
  background-color: #28a745;
  color: white;
}

.stop-btn {
  background-color: #dc3545;
  color: white;
}

.control-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.control-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.recording {
  background-color: #ff4444 !important;
  animation: pulse 2s infinite;
}

.chat-container {
  height: 400px;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 10px;
}

.chat-message {
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  background: #f8f9fa;
}

.chat-message.highlighted {
  background: #fff3cd;
  border-left: 4px solid #ffc107;
}

.username {
  font-weight: bold;
  margin-left: 5px;
}

.message-actions {
  margin-top: 5px;
}

.delete-btn,
.timeout-btn {
  font-size: 10px;
  padding: 2px 6px;
  margin-left: 5px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.delete-btn {
  background-color: #dc3545;
  color: white;
}

.timeout-btn {
  background-color: #ffc107;
  color: black;
}

.chat-input-section {
  display: flex;
  gap: 10px;
}

.chat-input {
  flex: 1;
  padding: 10px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
}

.send-btn {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.send-btn:hover {
  background-color: #0056b3;
}
</style>
```

---

**📺 نظام بث مباشر شامل ومتطور لمنصة البث العربية**

آخر تحديث: 15 يناير 2024
