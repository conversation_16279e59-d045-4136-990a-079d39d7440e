/**
 * 🎛️ لوحة التحكم الإدارية - JavaScript
 * إدارة التفاعلات والوظائف المتقدمة
 */

class AdminDashboard {
    constructor() {
        this.init();
        this.bindEvents();
        this.loadStats();
    }

    init() {
        console.log('🎛️ تم تحميل لوحة التحكم الإدارية');
        this.setupSidebar();
        this.setupSearch();
        this.setupNotifications();
    }

    bindEvents() {
        // إدارة الشريط الجانبي
        document.addEventListener('click', (e) => {
            if (e.target.matches('.sidebar-toggle')) {
                this.toggleSidebar();
            }
        });

        // إدارة النماذج
        document.querySelectorAll('form[data-ajax]').forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleAjaxForm(form);
            });
        });

        // إدارة الحذف
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn-delete')) {
                e.preventDefault();
                this.confirmDelete(e.target);
            }
        });

        // إدارة البحث المباشر
        const searchInput = document.querySelector('.header-search input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.handleSearch(e.target.value);
            });
        }
    }

    setupSidebar() {
        // تفعيل الرابط النشط
        const currentPath = window.location.pathname;
        document.querySelectorAll('.nav-item').forEach(item => {
            if (item.getAttribute('href') === currentPath) {
                item.classList.add('active');
            }
        });

        // طي/فتح الأقسام
        document.querySelectorAll('.nav-section-title').forEach(title => {
            title.addEventListener('click', () => {
                const section = title.parentElement;
                section.classList.toggle('collapsed');
            });
        });
    }

    toggleSidebar() {
        const sidebar = document.querySelector('.admin-sidebar');
        sidebar.classList.toggle('open');
    }

    setupSearch() {
        // إعداد البحث المتقدم
        this.searchCache = new Map();
        this.searchDebounce = null;
    }

    handleSearch(query) {
        clearTimeout(this.searchDebounce);
        
        if (query.length < 2) {
            this.clearSearchResults();
            return;
        }

        this.searchDebounce = setTimeout(() => {
            this.performSearch(query);
        }, 300);
    }

    async performSearch(query) {
        if (this.searchCache.has(query)) {
            this.displaySearchResults(this.searchCache.get(query));
            return;
        }

        try {
            const response = await fetch('/api/admin/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': this.getCSRFToken()
                },
                body: JSON.stringify({ query })
            });

            const results = await response.json();
            this.searchCache.set(query, results);
            this.displaySearchResults(results);
        } catch (error) {
            console.error('خطأ في البحث:', error);
        }
    }

    displaySearchResults(results) {
        // عرض نتائج البحث
        const dropdown = document.querySelector('.search-dropdown');
        if (!dropdown) return;

        dropdown.innerHTML = '';
        
        if (results.length === 0) {
            dropdown.innerHTML = '<div class="search-no-results">لا توجد نتائج</div>';
            return;
        }

        results.forEach(result => {
            const item = document.createElement('div');
            item.className = 'search-result-item';
            item.innerHTML = `
                <div class="result-icon">${this.getResultIcon(result.type)}</div>
                <div class="result-content">
                    <div class="result-title">${result.title}</div>
                    <div class="result-type">${result.type}</div>
                </div>
            `;
            item.addEventListener('click', () => {
                window.location.href = result.url;
            });
            dropdown.appendChild(item);
        });
    }

    clearSearchResults() {
        const dropdown = document.querySelector('.search-dropdown');
        if (dropdown) {
            dropdown.innerHTML = '';
        }
    }

    getResultIcon(type) {
        const icons = {
            'user': '👤',
            'content': '🎬',
            'setting': '⚙️',
            'report': '📊'
        };
        return icons[type] || '📄';
    }

    setupNotifications() {
        this.notifications = [];
        this.checkNotifications();
        
        // فحص الإشعارات كل دقيقة
        setInterval(() => {
            this.checkNotifications();
        }, 60000);
    }

    async checkNotifications() {
        try {
            const response = await fetch('/api/admin/notifications');
            const notifications = await response.json();
            
            this.updateNotificationBadge(notifications.length);
            this.notifications = notifications;
        } catch (error) {
            console.error('خطأ في تحميل الإشعارات:', error);
        }
    }

    updateNotificationBadge(count) {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            badge.textContent = count;
            badge.style.display = count > 0 ? 'block' : 'none';
        }
    }

    async loadStats() {
        try {
            const response = await fetch('/api/admin/stats');
            const stats = await response.json();
            this.updateStatsCards(stats);
            this.updateCharts(stats);
        } catch (error) {
            console.error('خطأ في تحميل الإحصائيات:', error);
        }
    }

    updateStatsCards(stats) {
        Object.keys(stats).forEach(key => {
            const card = document.querySelector(`[data-stat="${key}"]`);
            if (card) {
                const valueElement = card.querySelector('.stat-value');
                const changeElement = card.querySelector('.stat-change');
                
                if (valueElement) {
                    this.animateNumber(valueElement, stats[key].value);
                }
                
                if (changeElement && stats[key].change) {
                    changeElement.textContent = `${stats[key].change > 0 ? '+' : ''}${stats[key].change}%`;
                    changeElement.className = `stat-change ${stats[key].change > 0 ? 'positive' : 'negative'}`;
                }
            }
        });
    }

    animateNumber(element, targetValue) {
        const startValue = parseInt(element.textContent) || 0;
        const duration = 1000;
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
            element.textContent = this.formatNumber(currentValue);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        animate();
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'م';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'ك';
        }
        return num.toString();
    }

    updateCharts(stats) {
        // تحديث الرسوم البيانية
        if (typeof Chart !== 'undefined') {
            this.updateViewsChart(stats.views);
            this.updateUsersChart(stats.users);
            this.updateRevenueChart(stats.revenue);
        }
    }

    async handleAjaxForm(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        // إظهار حالة التحميل
        this.setButtonLoading(submitBtn, true);
        
        try {
            const response = await fetch(form.action, {
                method: form.method,
                body: formData,
                headers: {
                    'X-CSRF-Token': this.getCSRFToken()
                }
            });

            const result = await response.json();
            
            if (result.success) {
                this.showNotification('تم الحفظ بنجاح', 'success');
                if (result.redirect) {
                    window.location.href = result.redirect;
                }
            } else {
                this.showNotification(result.message || 'حدث خطأ', 'error');
                this.displayFormErrors(form, result.errors);
            }
        } catch (error) {
            this.showNotification('حدث خطأ في الاتصال', 'error');
            console.error('خطأ في النموذج:', error);
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }

    setButtonLoading(button, loading) {
        if (!button) return;
        
        if (loading) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        } else {
            button.disabled = false;
            button.innerHTML = button.dataset.originalText || 'حفظ';
        }
    }

    displayFormErrors(form, errors) {
        // مسح الأخطاء السابقة
        form.querySelectorAll('.error-message').forEach(el => el.remove());
        form.querySelectorAll('.form-control.error').forEach(el => {
            el.classList.remove('error');
        });

        if (!errors) return;

        Object.keys(errors).forEach(field => {
            const input = form.querySelector(`[name="${field}"]`);
            if (input) {
                input.classList.add('error');
                
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = errors[field];
                input.parentNode.appendChild(errorDiv);
            }
        });
    }

    confirmDelete(button) {
        const itemName = button.dataset.itemName || 'هذا العنصر';
        const deleteUrl = button.dataset.deleteUrl || button.href;
        
        if (confirm(`هل أنت متأكد من حذف ${itemName}؟`)) {
            this.performDelete(deleteUrl, button);
        }
    }

    async performDelete(url, button) {
        try {
            const response = await fetch(url, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-Token': this.getCSRFToken()
                }
            });

            const result = await response.json();
            
            if (result.success) {
                this.showNotification('تم الحذف بنجاح', 'success');
                
                // إزالة الصف من الجدول
                const row = button.closest('tr');
                if (row) {
                    row.style.opacity = '0.5';
                    setTimeout(() => row.remove(), 300);
                }
            } else {
                this.showNotification(result.message || 'فشل في الحذف', 'error');
            }
        } catch (error) {
            this.showNotification('حدث خطأ في الحذف', 'error');
            console.error('خطأ في الحذف:', error);
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-icon">${this.getNotificationIcon(type)}</span>
                <span class="notification-message">${message}</span>
            </div>
            <button class="notification-close">&times;</button>
        `;

        // إضافة الإشعار إلى الصفحة
        const container = document.querySelector('.notifications-container') || 
                         this.createNotificationsContainer();
        container.appendChild(notification);

        // إزالة الإشعار تلقائياً
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => notification.remove(), 300);
        }, 5000);

        // إزالة عند النقر على زر الإغلاق
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });
    }

    createNotificationsContainer() {
        const container = document.createElement('div');
        container.className = 'notifications-container';
        document.body.appendChild(container);
        return container;
    }

    getNotificationIcon(type) {
        const icons = {
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️'
        };
        return icons[type] || 'ℹ️';
    }

    getCSRFToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.getAttribute('content') : '';
    }

    // دوال مساعدة للرسوم البيانية
    updateViewsChart(data) {
        const ctx = document.getElementById('viewsChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'المشاهدات',
                    data: data.values,
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    updateUsersChart(data) {
        const ctx = document.getElementById('usersChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: [
                        '#2563eb',
                        '#10b981',
                        '#f59e0b',
                        '#ef4444'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    updateRevenueChart(data) {
        const ctx = document.getElementById('revenueChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'الإيرادات',
                    data: data.values,
                    backgroundColor: '#10b981'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// تهيئة لوحة التحكم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new AdminDashboard();
});

// دوال مساعدة عامة
window.AdminUtils = {
    formatDate: (date) => {
        return new Date(date).toLocaleDateString('ar-SA');
    },
    
    formatCurrency: (amount) => {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
        }).format(amount);
    },
    
    copyToClipboard: (text) => {
        navigator.clipboard.writeText(text).then(() => {
            console.log('تم النسخ إلى الحافظة');
        });
    },
    
    downloadFile: (url, filename) => {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
    }
};
