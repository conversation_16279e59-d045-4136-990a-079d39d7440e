{
    "name": "streaming-platform/arabic-streaming-platform",
    "description": "منصة البث العربية الشاملة - مشاهدة الأفلام والمسلسلات بجودة عالية",
    "type": "project",
    "license": "MIT",
    "keywords": [
        "streaming",
        "video",
        "movies",
        "series",
        "arabic",
        "platform",
        "entertainment",
        "media",
        "php",
        "mysql",
        "redis"
    ],
    "authors": [
        {
            "name": "فريق منصة البث العربية",
            "email": "<EMAIL>",
            "homepage": "https://streaming-platform.com",
            "role": "Developer"
        }
    ],
    "homepage": "https://streaming-platform.com",
    "support": {
        "issues": "https://github.com/streaming-platform/streaming-platform/issues",
        "source": "https://github.com/streaming-platform/streaming-platform",
        "docs": "https://docs.streaming-platform.com",
        "wiki": "https://github.com/streaming-platform/streaming-platform/wiki"
    },
    "require": {
        "php": "^8.1",
        "ext-json": "*",
        "ext-mbstring": "*",
        "ext-openssl": "*",
        "ext-pdo": "*",
        "ext-pdo_mysql": "*",
        "ext-redis": "*",
        "ext-curl": "*",
        "ext-gd": "*",
        "ext-fileinfo": "*",
        "ext-zip": "*",
        "monolog/monolog": "^3.5",
        "vlucas/phpdotenv": "^5.6",
        "firebase/php-jwt": "^6.10",
        "ramsey/uuid": "^4.7",
        "symfony/console": "^6.4",
        "symfony/http-foundation": "^6.4",
        "symfony/routing": "^6.4",
        "symfony/validator": "^6.4",
        "symfony/serializer": "^6.4",
        "doctrine/dbal": "^3.7",
        "doctrine/orm": "^2.17",
        "doctrine/migrations": "^3.7",
        "twig/twig": "^3.8",
        "swiftmailer/swiftmailer": "^6.3",
        "intervention/image": "^2.7",
        "league/flysystem": "^3.23",
        "predis/predis": "^2.2",
        "guzzlehttp/guzzle": "^7.8",
        "nesbot/carbon": "^2.72",
        "league/csv": "^9.15",
        "phpoffice/phpspreadsheet": "^1.29",
        "dompdf/dompdf": "^2.0",
        "mpdf/mpdf": "^8.2",
        "league/oauth2-server": "^8.5",
        "spatie/image-optimizer": "^1.7",
        "cocur/slugify": "^4.5",
        "league/commonmark": "^2.4",
        "symfony/cache": "^6.4",
        "symfony/rate-limiter": "^6.4"
    },
    "require-dev": {
        "phpunit/phpunit": "^10.5",
        "phpstan/phpstan": "^1.10",
        "squizlabs/php_codesniffer": "^3.8",
        "friendsofphp/php-cs-fixer": "^3.45",
        "mockery/mockery": "^1.6",
        "fakerphp/faker": "^1.23",
        "symfony/var-dumper": "^6.4",
        "symfony/debug-bundle": "^6.4",
        "symfony/web-profiler-bundle": "^6.4",
        "doctrine/doctrine-fixtures-bundle": "^3.5",
        "dama/doctrine-test-bundle": "^8.0",
        "phpunit/php-code-coverage": "^10.1",
        "sebastian/phpcpd": "^6.0",
        "pdepend/pdepend": "^2.16",
        "phpmd/phpmd": "^2.15",
        "vimeo/psalm": "^5.18",
        "roave/security-advisories": "dev-master"
    },
    "autoload": {
        "psr-4": {
            "StreamingPlatform\\": "src/",
            "App\\": "includes/"
        },
        "files": [
            "includes/functions.php",
            "includes/helpers.php"
        ]
    },
    "autoload-dev": {
        "psr-4": {
            "Tests\\": "tests/"
        ]
    },
    "scripts": {
        "test": "phpunit",
        "test-coverage": "phpunit --coverage-html coverage",
        "test-unit": "phpunit --testsuite=Unit",
        "test-feature": "phpunit --testsuite=Feature",
        "test-api": "phpunit --testsuite=API",
        "analyse": "phpstan analyse src includes --level=8",
        "cs-check": "phpcs --standard=PSR12 src includes",
        "cs-fix": "php-cs-fixer fix src includes",
        "psalm": "psalm",
        "phpmd": "phpmd src,includes text cleancode,codesize,controversial,design,naming,unusedcode",
        "security-check": "composer audit",
        "quality": [
            "@cs-check",
            "@analyse",
            "@psalm",
            "@phpmd"
        ],
        "fix": [
            "@cs-fix"
        ],
        "build": [
            "@quality",
            "@test"
        ],
        "post-install-cmd": [
            "@php -r \"file_exists('includes/config.php') || copy('includes/config.example.php', 'includes/config.php');\""
        ],
        "post-update-cmd": [
            "@security-check"
        ],
        "serve": "php -S localhost:8000 -t public",
        "migrate": "php bin/console doctrine:migrations:migrate",
        "fixtures": "php bin/console doctrine:fixtures:load",
        "cache-clear": "php bin/console cache:clear",
        "setup": [
            "composer install",
            "@migrate",
            "@fixtures"
        ]
    },
    "scripts-descriptions": {
        "test": "تشغيل جميع الاختبارات",
        "test-coverage": "تشغيل الاختبارات مع تقرير التغطية",
        "analyse": "تحليل الكود باستخدام PHPStan",
        "cs-check": "فحص معايير الكود",
        "cs-fix": "إصلاح معايير الكود تلقائياً",
        "quality": "فحص جودة الكود الشامل",
        "build": "بناء المشروع مع الفحوصات",
        "serve": "تشغيل خادم التطوير",
        "setup": "إعداد المشروع للمرة الأولى"
    },
    "config": {
        "optimize-autoloader": true,
        "preferred-install": "dist",
        "sort-packages": true,
        "allow-plugins": {
            "composer/package-versions-deprecated": true,
            "symfony/flex": true,
            "symfony/runtime": true
        },
        "platform": {
            "php": "8.1"
        }
    },
    "extra": {
        "symfony": {
            "allow-contrib": false,
            "require": "6.4.*"
        }
    },
    "minimum-stability": "stable",
    "prefer-stable": true,
    "archive": {
        "exclude": [
            "/tests",
            "/docs",
            "/.github",
            "/coverage",
            "/.phpunit.cache",
            "/phpunit.xml",
            "/phpstan.neon",
            "/.php-cs-fixer.cache",
            "/psalm.xml"
        ]
    }
}
