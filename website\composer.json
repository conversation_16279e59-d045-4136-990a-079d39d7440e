{"name": "streaming-platform/website", "description": "منصة بث شاملة للأفلام والمسلسلات", "type": "project", "keywords": ["streaming", "movies", "series", "php", "platform"], "license": "MIT", "authors": [{"name": "Streaming Platform Team", "email": "<EMAIL>"}], "require": {"php": ">=7.4", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-curl": "*", "ext-gd": "*"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "autoload": {"psr-4": {"StreamingPlatform\\": "src/"}, "files": ["includes/functions.php"]}, "autoload-dev": {"psr-4": {"StreamingPlatform\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "setup": ["php -r \"if (!file_exists('includes/config.php')) { copy('includes/config.example.php', 'includes/config.php'); }\"", "php -r \"echo 'Setup completed successfully!\\n';\""], "serve": "php -S localhost:8000 -t public", "clear-cache": "php -r \"array_map('unlink', glob('cache/*.cache'));\""}, "config": {"optimize-autoloader": true, "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}