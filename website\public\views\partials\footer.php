<?php
/**
 * 🦶 تذييل الصفحة (Footer)
 * يحتوي على الروابط والمعلومات الأساسية
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}
?>

<footer class="footer">
    <div class="container">
        <!-- الجزء العلوي من التذييل -->
        <div class="footer-top">
            <div class="footer-grid">
                <!-- معلومات الموقع -->
                <div class="footer-section">
                    <div class="footer-brand">
                        <img src="assets/images/logo.png" alt="<?php echo SITE_NAME; ?>" class="footer-logo">
                        <h3 class="footer-brand-name"><?php echo SITE_NAME; ?></h3>
                    </div>
                    <p class="footer-description">
                        <?php echo getSetting('site_description'); ?>
                    </p>
                    <div class="footer-social">
                        <a href="<?php echo SOCIAL_FACEBOOK; ?>" class="social-link" target="_blank" rel="noopener">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="<?php echo SOCIAL_TWITTER; ?>" class="social-link" target="_blank" rel="noopener">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="<?php echo SOCIAL_INSTAGRAM; ?>" class="social-link" target="_blank" rel="noopener">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="<?php echo SOCIAL_YOUTUBE; ?>" class="social-link" target="_blank" rel="noopener">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i class="fab fa-telegram"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                    </div>
                </div>

                <!-- روابط سريعة -->
                <div class="footer-section">
                    <h4 class="footer-title">روابط سريعة</h4>
                    <ul class="footer-links">
                        <li><a href="/" class="footer-link">الرئيسية</a></li>
                        <li><a href="/movies" class="footer-link">الأفلام</a></li>
                        <li><a href="/series" class="footer-link">المسلسلات</a></li>
                        <li><a href="/trending" class="footer-link">الرائج</a></li>
                        <li><a href="/top-rated" class="footer-link">الأعلى تقييماً</a></li>
                        <li><a href="/new-releases" class="footer-link">الإصدارات الجديدة</a></li>
                    </ul>
                </div>

                <!-- التصنيفات -->
                <div class="footer-section">
                    <h4 class="footer-title">التصنيفات</h4>
                    <ul class="footer-links">
                        <li><a href="/genre/action" class="footer-link">أكشن</a></li>
                        <li><a href="/genre/comedy" class="footer-link">كوميديا</a></li>
                        <li><a href="/genre/drama" class="footer-link">دراما</a></li>
                        <li><a href="/genre/horror" class="footer-link">رعب</a></li>
                        <li><a href="/genre/romance" class="footer-link">رومانسي</a></li>
                        <li><a href="/genre/thriller" class="footer-link">إثارة</a></li>
                    </ul>
                </div>

                <!-- الحساب -->
                <div class="footer-section">
                    <h4 class="footer-title">الحساب</h4>
                    <ul class="footer-links">
                        <?php if (IS_LOGGED_IN): ?>
                            <li><a href="/profile" class="footer-link">الملف الشخصي</a></li>
                            <li><a href="/favorites" class="footer-link">المفضلة</a></li>
                            <li><a href="/watchlist" class="footer-link">قائمة المشاهدة</a></li>
                            <li><a href="/history" class="footer-link">سجل المشاهدة</a></li>
                            <li><a href="/downloads" class="footer-link">التحميلات</a></li>
                            <li><a href="/settings" class="footer-link">الإعدادات</a></li>
                        <?php else: ?>
                            <li><a href="/auth/login" class="footer-link">تسجيل الدخول</a></li>
                            <li><a href="/auth/register" class="footer-link">إنشاء حساب</a></li>
                            <li><a href="/auth/forgot-password" class="footer-link">نسيت كلمة المرور؟</a></li>
                        <?php endif; ?>
                    </ul>
                </div>

                <!-- الدعم -->
                <div class="footer-section">
                    <h4 class="footer-title">الدعم والمساعدة</h4>
                    <ul class="footer-links">
                        <li><a href="/help" class="footer-link">مركز المساعدة</a></li>
                        <li><a href="/contact" class="footer-link">اتصل بنا</a></li>
                        <li><a href="/faq" class="footer-link">الأسئلة الشائعة</a></li>
                        <li><a href="/support" class="footer-link">الدعم الفني</a></li>
                        <li><a href="/feedback" class="footer-link">ملاحظات</a></li>
                        <li><a href="/report" class="footer-link">إبلاغ عن مشكلة</a></li>
                    </ul>
                </div>

                <!-- الاشتراك في النشرة الإخبارية -->
                <div class="footer-section">
                    <h4 class="footer-title">النشرة الإخبارية</h4>
                    <p class="newsletter-description">
                        اشترك في نشرتنا الإخبارية لتصلك أحدث الأفلام والمسلسلات
                    </p>
                    <form class="newsletter-form" id="newsletter-form">
                        <div class="newsletter-input-group">
                            <input type="email" 
                                   class="newsletter-input" 
                                   placeholder="البريد الإلكتروني"
                                   required>
                            <button type="submit" class="newsletter-btn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                    <div class="newsletter-apps">
                        <h5 class="apps-title">حمل التطبيق</h5>
                        <div class="app-buttons">
                            <a href="#" class="app-button">
                                <img src="assets/images/google-play.png" alt="Google Play">
                            </a>
                            <a href="#" class="app-button">
                                <img src="assets/images/app-store.png" alt="App Store">
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الجزء الأوسط من التذييل -->
        <div class="footer-middle">
            <div class="footer-stats">
                <div class="stat-item">
                    <div class="stat-number"><?php echo number_format(getTotalContent()); ?></div>
                    <div class="stat-label">محتوى</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo number_format(getTotalUsers()); ?></div>
                    <div class="stat-label">مستخدم</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo number_format(getTotalViews()); ?></div>
                    <div class="stat-label">مشاهدة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo count(SUPPORTED_LANGUAGES); ?></div>
                    <div class="stat-label">لغة</div>
                </div>
            </div>
        </div>

        <!-- الجزء السفلي من التذييل -->
        <div class="footer-bottom">
            <div class="footer-bottom-content">
                <div class="footer-legal">
                    <p class="copyright">
                        &copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.
                    </p>
                    <div class="legal-links">
                        <a href="/privacy" class="legal-link">سياسة الخصوصية</a>
                        <a href="/terms" class="legal-link">الشروط والأحكام</a>
                        <a href="/cookies" class="legal-link">سياسة الكوكيز</a>
                        <a href="/dmca" class="legal-link">DMCA</a>
                    </div>
                </div>
                
                <div class="footer-info">
                    <div class="footer-version">
                        الإصدار <?php echo SITE_VERSION; ?>
                    </div>
                    <div class="footer-language">
                        <i class="fas fa-globe"></i>
                        <?php echo SUPPORTED_LANGUAGES[CURRENT_LANGUAGE]; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="footer-extra">
        <div class="container">
            <div class="footer-disclaimer">
                <p>
                    <strong>إخلاء مسؤولية:</strong>
                    هذا الموقع لا يستضيف أي ملفات على خوادمه. جميع المحتويات مقدمة من طرف ثالث.
                    إذا كان لديك أي مشاكل قانونية، يرجى الاتصال بمالكي الملفات/المضيفين المناسبين.
                </p>
            </div>
            
            <div class="footer-security">
                <div class="security-badges">
                    <div class="security-badge">
                        <i class="fas fa-shield-alt"></i>
                        <span>آمن ومحمي</span>
                    </div>
                    <div class="security-badge">
                        <i class="fas fa-lock"></i>
                        <span>SSL مشفر</span>
                    </div>
                    <div class="security-badge">
                        <i class="fas fa-user-shield"></i>
                        <span>خصوصية محمية</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initFooter();
});

function initFooter() {
    // تهيئة النشرة الإخبارية
    initNewsletter();
    
    // تهيئة الإحصائيات المتحركة
    initAnimatedStats();
    
    // تهيئة الروابط الخارجية
    initExternalLinks();
}

function initNewsletter() {
    const newsletterForm = document.getElementById('newsletter-form');
    
    if (!newsletterForm) return;
    
    newsletterForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const emailInput = newsletterForm.querySelector('.newsletter-input');
        const email = emailInput.value.trim();
        
        if (!email) {
            APP.ui.showNotification('يرجى إدخال البريد الإلكتروني', 'warning');
            return;
        }
        
        if (!isValidEmail(email)) {
            APP.ui.showNotification('البريد الإلكتروني غير صحيح', 'error');
            return;
        }
        
        try {
            const response = await APP.api.post('/api/newsletter/subscribe', { email });
            
            if (response.success) {
                APP.ui.showNotification('تم الاشتراك في النشرة الإخبارية بنجاح', 'success');
                emailInput.value = '';
            } else {
                APP.ui.showNotification(response.error || 'حدث خطأ في الاشتراك', 'error');
            }
        } catch (error) {
            APP.ui.showNotification('خطأ في الاتصال', 'error');
        }
    });
}

function initAnimatedStats() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateNumber(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });
    
    statNumbers.forEach(stat => {
        observer.observe(stat);
    });
}

function animateNumber(element) {
    const finalNumber = parseInt(element.textContent.replace(/,/g, ''));
    const duration = 2000; // 2 ثانية
    const steps = 60;
    const increment = finalNumber / steps;
    let current = 0;
    
    const timer = setInterval(() => {
        current += increment;
        
        if (current >= finalNumber) {
            current = finalNumber;
            clearInterval(timer);
        }
        
        element.textContent = Math.floor(current).toLocaleString();
    }, duration / steps);
}

function initExternalLinks() {
    // إضافة target="_blank" للروابط الخارجية
    document.querySelectorAll('a[href^="http"]').forEach(link => {
        if (!link.hostname.includes(window.location.hostname)) {
            link.setAttribute('target', '_blank');
            link.setAttribute('rel', 'noopener noreferrer');
        }
    });
    
    // تتبع النقرات على الروابط الاجتماعية
    document.querySelectorAll('.social-link').forEach(link => {
        link.addEventListener('click', (e) => {
            const platform = getSocialPlatform(link.href);
            trackSocialClick(platform);
        });
    });
}

function getSocialPlatform(url) {
    if (url.includes('facebook')) return 'facebook';
    if (url.includes('twitter')) return 'twitter';
    if (url.includes('instagram')) return 'instagram';
    if (url.includes('youtube')) return 'youtube';
    if (url.includes('telegram')) return 'telegram';
    if (url.includes('whatsapp')) return 'whatsapp';
    return 'other';
}

function trackSocialClick(platform) {
    // تتبع النقرات للتحليلات
    if (typeof gtag !== 'undefined') {
        gtag('event', 'social_click', {
            'social_platform': platform,
            'page_location': window.location.href
        });
    }
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// دوال مساعدة للإحصائيات
function getTotalContent() {
    // سيتم استبدالها بقيم حقيقية من قاعدة البيانات
    return <?php echo getTotalContent() ?: 0; ?>;
}

function getTotalUsers() {
    return <?php echo getTotalUsers() ?: 0; ?>;
}

function getTotalViews() {
    return <?php echo getTotalViews() ?: 0; ?>;
}

// تحديث الإحصائيات كل 5 دقائق
setInterval(async () => {
    try {
        const stats = await APP.api.get('/api/stats');
        updateFooterStats(stats);
    } catch (error) {
        console.error('Failed to update stats:', error);
    }
}, 300000); // 5 دقائق

function updateFooterStats(stats) {
    const statItems = document.querySelectorAll('.footer-stats .stat-item');
    
    if (statItems.length >= 4) {
        statItems[0].querySelector('.stat-number').textContent = stats.total_content?.toLocaleString() || '0';
        statItems[1].querySelector('.stat-number').textContent = stats.total_users?.toLocaleString() || '0';
        statItems[2].querySelector('.stat-number').textContent = stats.total_views?.toLocaleString() || '0';
        statItems[3].querySelector('.stat-number').textContent = stats.supported_languages || '4';
    }
}

// تهيئة تأثيرات التمرير
window.addEventListener('scroll', APP.utils.throttle(() => {
    const footer = document.querySelector('.footer');
    const scrolled = window.pageYOffset;
    const rate = scrolled * -0.5;
    
    // تأثير المنظور للخلفية
    if (footer) {
        footer.style.transform = `translateY(${rate}px)`;
    }
}, 16)); // 60fps
</script>

<style>
/* تنسيقات إضافية للتذييل */
.footer {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    color: var(--text-secondary);
    margin-top: auto;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
}

.footer-top {
    padding: var(--spacing-2xl) 0;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
}

.footer-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.footer-logo {
    width: 40px;
    height: 40px;
}

.footer-brand-name {
    color: var(--text-primary);
    font-size: var(--font-xl);
    margin: 0;
}

.footer-description {
    color: var(--text-muted);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.footer-social {
    display: flex;
    gap: var(--spacing-sm);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.social-link:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.footer-title {
    color: var(--text-primary);
    font-size: var(--font-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.footer-links {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.footer-link {
    color: var(--text-muted);
    transition: color var(--transition-fast);
    padding: var(--spacing-xs) 0;
}

.footer-link:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.newsletter-form {
    margin-bottom: var(--spacing-lg);
}

.newsletter-input-group {
    display: flex;
    border-radius: var(--radius-md);
    overflow: hidden;
    background-color: var(--bg-tertiary);
}

.newsletter-input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: transparent;
    color: var(--text-primary);
}

.newsletter-input::placeholder {
    color: var(--text-muted);
}

.newsletter-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--primary-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.newsletter-btn:hover {
    background-color: var(--secondary-color);
}

.app-buttons {
    display: flex;
    gap: var(--spacing-sm);
}

.app-button img {
    height: 40px;
    border-radius: var(--radius-sm);
}

.footer-middle {
    padding: var(--spacing-xl) 0;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
}

.footer-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
    text-align: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
}

.stat-number {
    font-size: var(--font-2xl);
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    color: var(--text-muted);
    font-size: var(--font-sm);
}

.footer-bottom {
    padding: var(--spacing-lg) 0;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-legal {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.copyright {
    color: var(--text-muted);
    font-size: var(--font-sm);
    margin: 0;
}

.legal-links {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.legal-link {
    color: var(--text-muted);
    font-size: var(--font-sm);
    transition: color var(--transition-fast);
}

.legal-link:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.footer-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--text-muted);
    font-size: var(--font-sm);
}

.footer-extra {
    background-color: var(--bg-tertiary);
    padding: var(--spacing-lg) 0;
}

.footer-disclaimer {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.footer-disclaimer p {
    color: var(--text-muted);
    font-size: var(--font-sm);
    line-height: 1.6;
    margin: 0;
}

.footer-security {
    display: flex;
    justify-content: center;
}

.security-badges {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.security-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-muted);
    font-size: var(--font-sm);
}

.security-badge i {
    color: var(--success-color);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .footer-grid {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }
    
    .legal-links {
        justify-content: center;
    }
    
    .security-badges {
        justify-content: center;
    }
    
    .footer-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .footer-stats {
        grid-template-columns: 1fr;
    }
    
    .security-badges {
        flex-direction: column;
        align-items: center;
    }
}
</style>
