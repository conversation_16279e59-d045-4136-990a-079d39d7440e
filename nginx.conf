# 🌐 إعدادات Nginx لمنصة البث العربية
# ملف إعدادات محسن للأداء والأمان

server {
    listen 80;
    listen [::]:80;
    server_name your-domain.com www.your-domain.com;
    
    # إعادة توجيه HTTP إلى HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # مسار الملفات
    root /var/www/streaming-platform/website/public;
    index simple-index.php index.php index.html;
    
    # إعدادات SSL
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # إعدادات الأمان
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # إعدادات التخزين المؤقت
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    location ~* \.(mp4|webm|ogg|avi|mov)$ {
        expires 30d;
        add_header Cache-Control "public";
        access_log off;
        
        # دعم Range Requests للفيديو
        add_header Accept-Ranges bytes;
    }
    
    # معالجة ملفات PHP
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # إعدادات PHP محسنة
        fastcgi_read_timeout 300;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
    }
    
    # إعادة كتابة الروابط
    location / {
        try_files $uri $uri/ /simple-index.php?$query_string;
    }
    
    # API routes
    location /api/ {
        try_files $uri $uri/ /api/index.php?$query_string;
    }
    
    # Admin routes
    location /admin/ {
        try_files $uri $uri/ /admin/index.php?$query_string;
        
        # حماية إضافية للوحة الإدارة
        # allow ***********/24;
        # deny all;
    }
    
    # منع الوصول للملفات الحساسة
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~* \.(htaccess|htpasswd|ini|log|sh|inc|bak|sql|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # منع الوصول للمجلدات الحساسة
    location ~ ^/(includes|vendor|logs|cache|backups)/ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # ضغط الملفات
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # إعدادات رفع الملفات
    client_max_body_size 100M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # إعدادات الأداء
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # تسجيل الأخطاء
    error_log /var/log/nginx/streaming-platform-error.log;
    access_log /var/log/nginx/streaming-platform-access.log;
}

# إعدادات عامة
client_body_buffer_size 128k;
client_header_buffer_size 1k;
large_client_header_buffers 4 4k;
output_buffers 1 32k;
postpone_output 1460;
