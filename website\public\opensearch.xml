<?xml version="1.0" encoding="UTF-8"?>
<!-- 🔍 OpenSearch Description لمنصة البث العربية -->
<OpenSearchDescription xmlns="http://a9.com/-/spec/opensearch/1.1/"
                       xmlns:moz="http://www.mozilla.org/2006/browser/search/">
    
    <!-- معلومات أساسية -->
    <ShortName>منصة البث العربية</ShortName>
    <Description>ابحث في منصة البث العربية عن الأفلام والمسلسلات والوثائقيات</Description>
    <LongName>البحث في منصة البث العربية - أفلام ومسلسلات عربية وعالمية</LongName>
    
    <!-- الصور والأيقونات -->
    <Image height="16" width="16" type="image/x-icon">https://streaming-platform.com/favicon.ico</Image>
    <Image height="32" width="32" type="image/png">https://streaming-platform.com/assets/images/icons/icon-32x32.png</Image>
    <Image height="64" width="64" type="image/png">https://streaming-platform.com/assets/images/icons/icon-64x64.png</Image>
    <Image height="128" width="128" type="image/png">https://streaming-platform.com/assets/images/icons/icon-128x128.png</Image>
    
    <!-- URLs للبحث -->
    <!-- البحث الرئيسي -->
    <Url type="text/html" 
         method="get" 
         template="https://streaming-platform.com/search?q={searchTerms}&amp;type=all&amp;page={startPage?}&amp;count={count?}">
        <Param name="q" value="{searchTerms}"/>
        <Param name="type" value="all"/>
        <Param name="source" value="opensearch"/>
    </Url>
    
    <!-- البحث في الأفلام فقط -->
    <Url type="text/html" 
         method="get" 
         template="https://streaming-platform.com/search?q={searchTerms}&amp;type=movies&amp;page={startPage?}&amp;count={count?}">
        <Param name="q" value="{searchTerms}"/>
        <Param name="type" value="movies"/>
        <Param name="source" value="opensearch"/>
    </Url>
    
    <!-- البحث في المسلسلات فقط -->
    <Url type="text/html" 
         method="get" 
         template="https://streaming-platform.com/search?q={searchTerms}&amp;type=series&amp;page={startPage?}&amp;count={count?}">
        <Param name="q" value="{searchTerms}"/>
        <Param name="type" value="series"/>
        <Param name="source" value="opensearch"/>
    </Url>
    
    <!-- البحث JSON API -->
    <Url type="application/json" 
         method="get" 
         template="https://streaming-platform.com/api/search?q={searchTerms}&amp;format=json&amp;page={startPage?}&amp;count={count?}">
        <Param name="q" value="{searchTerms}"/>
        <Param name="format" value="json"/>
        <Param name="source" value="opensearch"/>
    </Url>
    
    <!-- البحث XML API -->
    <Url type="application/xml" 
         method="get" 
         template="https://streaming-platform.com/api/search?q={searchTerms}&amp;format=xml&amp;page={startPage?}&amp;count={count?}">
        <Param name="q" value="{searchTerms}"/>
        <Param name="format" value="xml"/>
        <Param name="source" value="opensearch"/>
    </Url>
    
    <!-- البحث RSS -->
    <Url type="application/rss+xml" 
         method="get" 
         template="https://streaming-platform.com/search/rss?q={searchTerms}&amp;page={startPage?}&amp;count={count?}">
        <Param name="q" value="{searchTerms}"/>
        <Param name="format" value="rss"/>
        <Param name="source" value="opensearch"/>
    </Url>
    
    <!-- البحث Atom -->
    <Url type="application/atom+xml" 
         method="get" 
         template="https://streaming-platform.com/search/atom?q={searchTerms}&amp;page={startPage?}&amp;count={count?}">
        <Param name="q" value="{searchTerms}"/>
        <Param name="format" value="atom"/>
        <Param name="source" value="opensearch"/>
    </Url>
    
    <!-- اقتراحات البحث -->
    <Url type="application/x-suggestions+json" 
         method="get" 
         template="https://streaming-platform.com/api/search/suggestions?q={searchTerms}&amp;count={count?}">
        <Param name="q" value="{searchTerms}"/>
        <Param name="type" value="suggestions"/>
        <Param name="source" value="opensearch"/>
    </Url>
    
    <!-- البحث المتقدم -->
    <Url type="text/html" 
         method="get" 
         template="https://streaming-platform.com/advanced-search?q={searchTerms}&amp;genre={genre?}&amp;year={year?}&amp;rating={rating?}&amp;duration={duration?}&amp;language={language?}">
        <Param name="q" value="{searchTerms}"/>
        <Param name="advanced" value="true"/>
        <Param name="source" value="opensearch"/>
    </Url>
    
    <!-- معلومات إضافية -->
    <Tags>أفلام مسلسلات عربية وثائقيات ترفيه</Tags>
    <Contact><EMAIL></Contact>
    <Developer>فريق منصة البث العربية</Developer>
    <Attribution>البحث مقدم من منصة البث العربية © 2024</Attribution>
    <SyndicationRight>open</SyndicationRight>
    <AdultContent>false</AdultContent>
    <Language>ar</Language>
    <Language>ar-SA</Language>
    <Language>en</Language>
    <InputEncoding>UTF-8</InputEncoding>
    <OutputEncoding>UTF-8</OutputEncoding>
    
    <!-- إعدادات Mozilla Firefox -->
    <moz:SearchForm>https://streaming-platform.com/search</moz:SearchForm>
    <moz:UpdateUrl>https://streaming-platform.com/opensearch.xml</moz:UpdateUrl>
    <moz:UpdateInterval>7</moz:UpdateInterval>
    <moz:IconUpdateUrl>https://streaming-platform.com/favicon.ico</moz:IconUpdateUrl>
    
    <!-- استعلامات مثال -->
    <Query role="example" searchTerms="أكشن"/>
    <Query role="example" searchTerms="كوميديا"/>
    <Query role="example" searchTerms="دراما"/>
    <Query role="example" searchTerms="رومانسي"/>
    <Query role="example" searchTerms="إثارة"/>
    <Query role="example" searchTerms="خيال علمي"/>
    <Query role="example" searchTerms="رعب"/>
    <Query role="example" searchTerms="مغامرة"/>
    <Query role="example" searchTerms="تاريخي"/>
    <Query role="example" searchTerms="وثائقي"/>
    
</OpenSearchDescription>
