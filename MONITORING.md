# 📊 دليل المراقبة والتحليل - منصة البث العربية

## 🎯 **نظرة عامة**

هذا الدليل يوضح كيفية مراقبة وتحليل أداء منصة البث العربية لضمان الاستقرار والأداء الأمثل. نغطي جميع جوانب المراقبة من الخادم إلى تجربة المستخدم.

### **أهداف المراقبة**
- **مراقبة الأداء**: قياس سرعة الاستجابة والإنتاجية
- **مراقبة الاستقرار**: تتبع الأخطاء والتوقفات
- **مراقبة الأمان**: كشف التهديدات والهجمات
- **مراقبة المستخدمين**: تحليل سلوك المستخدمين
- **مراقبة الموارد**: تتبع استخدام الخادم والشبكة

---

## 🖥️ **مراقبة الخادم (Server Monitoring)**

### **مراقبة الموارد الأساسية**

#### **CPU والذاكرة**

```bash
# مراقبة استخدام CPU
top -p $(pgrep -d',' php-fpm)

# مراقبة استخدام الذاكرة
free -h
ps aux --sort=-%mem | head

# مراقبة مستمرة مع htop
htop -p $(pgrep -d',' nginx,php-fpm,mysql)
```

#### **مساحة القرص والشبكة**

```bash
# مراقبة مساحة القرص
df -h
du -sh /var/www/streaming-platform/*

# مراقبة الشبكة
iftop -i eth0
netstat -tuln | grep :80
ss -tuln | grep :443
```

### **مراقبة قاعدة البيانات**

#### **MySQL Performance Schema**

```sql
-- تفعيل Performance Schema
UPDATE performance_schema.setup_instruments 
SET ENABLED = 'YES', TIMED = 'YES' 
WHERE NAME LIKE '%statement/%';

-- مراقبة الاستعلامات البطيئة
SELECT 
    DIGEST_TEXT,
    COUNT_STAR,
    AVG_TIMER_WAIT/1000000000 AS avg_time_seconds,
    SUM_TIMER_WAIT/1000000000 AS total_time_seconds
FROM performance_schema.events_statements_summary_by_digest 
ORDER BY AVG_TIMER_WAIT DESC 
LIMIT 10;

-- مراقبة الاتصالات
SELECT 
    processlist_user,
    processlist_host,
    processlist_db,
    processlist_command,
    processlist_time,
    processlist_info
FROM performance_schema.processlist 
WHERE processlist_command != 'Sleep';
```

#### **إعدادات MySQL للمراقبة**

```ini
# my.cnf
[mysqld]
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 1
log_queries_not_using_indexes = 1

# تفعيل General Log للتطوير فقط
general_log = 1
general_log_file = /var/log/mysql/general.log
```

---

## 📈 **أدوات المراقبة المتقدمة**

### **Prometheus + Grafana**

#### **إعداد Prometheus**

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'streaming-platform'
    static_configs:
      - targets: ['localhost:9090']
  
  - job_name: 'nginx'
    static_configs:
      - targets: ['localhost:9113']
  
  - job_name: 'mysql'
    static_configs:
      - targets: ['localhost:9104']
  
  - job_name: 'php-fpm'
    static_configs:
      - targets: ['localhost:9253']

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### **قواعد التنبيه**

```yaml
# alert_rules.yml
groups:
- name: streaming_platform_alerts
  rules:
  - alert: HighCPUUsage
    expr: cpu_usage_percent > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "استخدام CPU عالي"
      description: "استخدام CPU {{ $value }}% لأكثر من 5 دقائق"

  - alert: HighMemoryUsage
    expr: memory_usage_percent > 85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "استخدام الذاكرة عالي"
      description: "استخدام الذاكرة {{ $value }}% لأكثر من 5 دقائق"

  - alert: DatabaseConnectionsHigh
    expr: mysql_global_status_threads_connected > 100
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "عدد اتصالات قاعدة البيانات عالي"
      description: "{{ $value }} اتصال نشط بقاعدة البيانات"

  - alert: SlowQueries
    expr: rate(mysql_global_status_slow_queries[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "استعلامات بطيئة في قاعدة البيانات"
      description: "{{ $value }} استعلام بطيء في الدقيقة"
```

### **ELK Stack (Elasticsearch, Logstash, Kibana)**

#### **إعداد Logstash**

```ruby
# logstash.conf
input {
  file {
    path => "/var/log/nginx/access.log"
    type => "nginx_access"
    start_position => "beginning"
  }
  
  file {
    path => "/var/log/nginx/error.log"
    type => "nginx_error"
    start_position => "beginning"
  }
  
  file {
    path => "/var/www/streaming-platform/storage/logs/laravel.log"
    type => "laravel"
    start_position => "beginning"
  }
}

filter {
  if [type] == "nginx_access" {
    grok {
      match => { "message" => "%{NGINXACCESS}" }
    }
    
    date {
      match => [ "timestamp", "dd/MMM/yyyy:HH:mm:ss Z" ]
    }
    
    mutate {
      convert => { "response" => "integer" }
      convert => { "bytes" => "integer" }
      convert => { "responsetime" => "float" }
    }
  }
  
  if [type] == "laravel" {
    grok {
      match => { "message" => "\[%{TIMESTAMP_ISO8601:timestamp}\] %{WORD:env}\.%{WORD:level}: %{GREEDYDATA:log_message}" }
    }
  }
}

output {
  elasticsearch {
    hosts => ["localhost:9200"]
    index => "streaming-platform-%{+YYYY.MM.dd}"
  }
}
```

---

## 🔍 **مراقبة التطبيق (Application Monitoring)**

### **Laravel Telescope**

#### **إعداد Telescope**

```php
<?php
// config/telescope.php
return [
    'enabled' => env('TELESCOPE_ENABLED', true),
    
    'watchers' => [
        Watchers\CacheWatcher::class => env('TELESCOPE_CACHE_WATCHER', true),
        Watchers\CommandWatcher::class => env('TELESCOPE_COMMAND_WATCHER', true),
        Watchers\DumpWatcher::class => env('TELESCOPE_DUMP_WATCHER', true),
        Watchers\EventWatcher::class => env('TELESCOPE_EVENT_WATCHER', true),
        Watchers\ExceptionWatcher::class => env('TELESCOPE_EXCEPTION_WATCHER', true),
        Watchers\JobWatcher::class => env('TELESCOPE_JOB_WATCHER', true),
        Watchers\LogWatcher::class => env('TELESCOPE_LOG_WATCHER', true),
        Watchers\MailWatcher::class => env('TELESCOPE_MAIL_WATCHER', true),
        Watchers\ModelWatcher::class => env('TELESCOPE_MODEL_WATCHER', true),
        Watchers\NotificationWatcher::class => env('TELESCOPE_NOTIFICATION_WATCHER', true),
        Watchers\QueryWatcher::class => env('TELESCOPE_QUERY_WATCHER', true),
        Watchers\RedisWatcher::class => env('TELESCOPE_REDIS_WATCHER', true),
        Watchers\RequestWatcher::class => env('TELESCOPE_REQUEST_WATCHER', true),
        Watchers\ScheduleWatcher::class => env('TELESCOPE_SCHEDULE_WATCHER', true),
    ],
];
```

### **مراقبة مخصصة**

#### **Middleware للمراقبة**

```php
<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class MonitoringMiddleware
{
    public function handle($request, Closure $next)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        $response = $next($request);
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        
        $metrics = [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'response_time' => round(($endTime - $startTime) * 1000, 2), // ms
            'memory_usage' => $endMemory - $startMemory,
            'status_code' => $response->getStatusCode(),
            'user_id' => auth()->id(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now()->toISOString(),
        ];
        
        // تسجيل المقاييس
        Log::channel('metrics')->info('Request Metrics', $metrics);
        
        // تحديث إحصائيات الوقت الفعلي
        $this->updateRealTimeStats($metrics);
        
        return $response;
    }
    
    private function updateRealTimeStats($metrics)
    {
        // تحديث متوسط وقت الاستجابة
        $avgKey = 'avg_response_time';
        $currentAvg = Cache::get($avgKey, 0);
        $newAvg = ($currentAvg + $metrics['response_time']) / 2;
        Cache::put($avgKey, $newAvg, 300);
        
        // تحديث عداد الطلبات
        Cache::increment('total_requests');
        Cache::increment('requests_' . date('Y-m-d-H'));
        
        // تتبع الأخطاء
        if ($metrics['status_code'] >= 400) {
            Cache::increment('error_count');
            Cache::increment('errors_' . $metrics['status_code']);
        }
    }
}
```

---

## 📱 **مراقبة تجربة المستخدم**

### **Real User Monitoring (RUM)**

#### **JavaScript للمراقبة**

```javascript
// monitoring.js
class UserExperienceMonitor {
    constructor() {
        this.metrics = {};
        this.init();
    }
    
    init() {
        this.trackPageLoad();
        this.trackUserInteractions();
        this.trackErrors();
        this.trackPerformance();
    }
    
    trackPageLoad() {
        window.addEventListener('load', () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            
            this.metrics.pageLoad = {
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                firstPaint: this.getFirstPaint(),
                firstContentfulPaint: this.getFirstContentfulPaint(),
                largestContentfulPaint: this.getLargestContentfulPaint()
            };
            
            this.sendMetrics('page_load', this.metrics.pageLoad);
        });
    }
    
    trackUserInteractions() {
        // تتبع النقرات
        document.addEventListener('click', (event) => {
            const element = event.target;
            const metrics = {
                element: element.tagName,
                id: element.id,
                class: element.className,
                text: element.textContent?.substring(0, 50),
                timestamp: Date.now()
            };
            
            this.sendMetrics('user_click', metrics);
        });
        
        // تتبع التمرير
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                const scrollPercent = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;
                this.sendMetrics('scroll', { percent: Math.round(scrollPercent) });
            }, 100);
        });
    }
    
    trackErrors() {
        window.addEventListener('error', (event) => {
            const errorMetrics = {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack,
                timestamp: Date.now()
            };
            
            this.sendMetrics('javascript_error', errorMetrics);
        });
        
        // تتبع أخطاء Promise
        window.addEventListener('unhandledrejection', (event) => {
            const errorMetrics = {
                reason: event.reason,
                timestamp: Date.now()
            };
            
            this.sendMetrics('promise_rejection', errorMetrics);
        });
    }
    
    trackPerformance() {
        // تتبع Web Vitals
        import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
            getCLS((metric) => this.sendMetrics('cls', metric));
            getFID((metric) => this.sendMetrics('fid', metric));
            getFCP((metric) => this.sendMetrics('fcp', metric));
            getLCP((metric) => this.sendMetrics('lcp', metric));
            getTTFB((metric) => this.sendMetrics('ttfb', metric));
        });
    }
    
    sendMetrics(type, data) {
        // إرسال البيانات للخادم
        fetch('/api/metrics', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                type: type,
                data: data,
                url: window.location.href,
                userAgent: navigator.userAgent,
                timestamp: Date.now()
            })
        }).catch(error => {
            console.error('Failed to send metrics:', error);
        });
    }
    
    getFirstPaint() {
        const paintEntries = performance.getEntriesByType('paint');
        const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
        return firstPaint ? firstPaint.startTime : null;
    }
    
    getFirstContentfulPaint() {
        const paintEntries = performance.getEntriesByType('paint');
        const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
        return fcp ? fcp.startTime : null;
    }
    
    getLargestContentfulPaint() {
        return new Promise((resolve) => {
            new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                resolve(lastEntry.startTime);
            }).observe({ entryTypes: ['largest-contentful-paint'] });
        });
    }
}

// تهيئة المراقبة
const monitor = new UserExperienceMonitor();
```

---

## 🚨 **نظام التنبيهات**

### **إعداد التنبيهات**

#### **Slack Integration**

```php
<?php
namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AlertService
{
    private $slackWebhook;
    
    public function __construct()
    {
        $this->slackWebhook = config('services.slack.webhook_url');
    }
    
    public function sendAlert($level, $message, $details = [])
    {
        $color = $this->getColorByLevel($level);
        
        $payload = [
            'text' => "تنبيه من منصة البث العربية",
            'attachments' => [
                [
                    'color' => $color,
                    'title' => $message,
                    'fields' => $this->formatDetails($details),
                    'footer' => 'منصة البث العربية',
                    'ts' => time()
                ]
            ]
        ];
        
        try {
            Http::post($this->slackWebhook, $payload);
        } catch (\Exception $e) {
            Log::error('Failed to send Slack alert', [
                'error' => $e->getMessage(),
                'alert' => $message
            ]);
        }
    }
    
    private function getColorByLevel($level)
    {
        return match($level) {
            'critical' => '#ff0000',
            'warning' => '#ffaa00',
            'info' => '#00ff00',
            default => '#cccccc'
        };
    }
    
    private function formatDetails($details)
    {
        $fields = [];
        
        foreach ($details as $key => $value) {
            $fields[] = [
                'title' => $key,
                'value' => $value,
                'short' => true
            ];
        }
        
        return $fields;
    }
}
```

### **تنبيهات تلقائية**

```php
<?php
// في Command أو Job
class SystemHealthCheck extends Command
{
    protected $signature = 'system:health-check';
    
    public function handle(AlertService $alertService)
    {
        // فحص استخدام CPU
        $cpuUsage = $this->getCpuUsage();
        if ($cpuUsage > 80) {
            $alertService->sendAlert('warning', 'استخدام CPU عالي', [
                'CPU Usage' => $cpuUsage . '%',
                'Server' => gethostname(),
                'Time' => now()->format('Y-m-d H:i:s')
            ]);
        }
        
        // فحص مساحة القرص
        $diskUsage = $this->getDiskUsage();
        if ($diskUsage > 85) {
            $alertService->sendAlert('critical', 'مساحة القرص منخفضة', [
                'Disk Usage' => $diskUsage . '%',
                'Available Space' => $this->getAvailableSpace(),
                'Server' => gethostname()
            ]);
        }
        
        // فحص قاعدة البيانات
        try {
            DB::connection()->getPdo();
        } catch (\Exception $e) {
            $alertService->sendAlert('critical', 'فشل الاتصال بقاعدة البيانات', [
                'Error' => $e->getMessage(),
                'Server' => gethostname(),
                'Time' => now()->format('Y-m-d H:i:s')
            ]);
        }
    }
}
```

---

## 📊 **لوحات المراقبة (Dashboards)**

### **Grafana Dashboard**

```json
{
  "dashboard": {
    "title": "منصة البث العربية - لوحة المراقبة",
    "panels": [
      {
        "title": "وقت الاستجابة",
        "type": "graph",
        "targets": [
          {
            "expr": "avg(response_time_seconds)",
            "legendFormat": "متوسط وقت الاستجابة"
          }
        ]
      },
      {
        "title": "عدد المستخدمين النشطين",
        "type": "singlestat",
        "targets": [
          {
            "expr": "sum(active_users)",
            "legendFormat": "المستخدمون النشطون"
          }
        ]
      },
      {
        "title": "معدل الأخطاء",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"4..|5..\"}[5m])",
            "legendFormat": "معدل الأخطاء"
          }
        ]
      }
    ]
  }
}
```

---

## 🔧 **أوامر المراقبة**

```bash
# تشغيل جميع أدوات المراقبة
make monitoring-start

# إيقاف المراقبة
make monitoring-stop

# عرض حالة النظام
make system-status

# تشغيل فحص صحة النظام
make health-check

# عرض السجلات المباشرة
make logs-live

# تحليل الأداء
make performance-analysis

# إنشاء تقرير المراقبة
make monitoring-report
```

---

**📊 نظام مراقبة شامل ومتطور لمنصة البث العربية**

آخر تحديث: 15 يناير 2024
