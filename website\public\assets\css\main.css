/* 🎨 ملف CSS الرئيسي لمنصة البث الشاملة */

/* ==========================================
   🌐 المتغيرات العامة
   ========================================== */
:root {
  /* الألوان الأساسية */
  --primary-color: #6c5ce7;
  --secondary-color: #a29bfe;
  --accent-color: #fd79a8;
  --success-color: #00b894;
  --warning-color: #fdcb6e;
  --danger-color: #e17055;
  --info-color: #74b9ff;
  
  /* ألوان الخلفية */
  --bg-primary: #1a1a2e;
  --bg-secondary: #16213e;
  --bg-tertiary: #0f3460;
  --bg-card: #1e1e2e;
  --bg-overlay: rgba(0, 0, 0, 0.8);
  
  /* ألوان النص */
  --text-primary: #ffffff;
  --text-secondary: #b2b2b2;
  --text-muted: #6c757d;
  --text-dark: #2d3436;
  
  /* ألوان الحدود */
  --border-color: #2d3748;
  --border-light: #4a5568;
  --border-dark: #1a202c;
  
  /* الخطوط */
  --font-primary: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-secondary: 'Tajawal', Arial, sans-serif;
  --font-mono: 'Courier New', monospace;
  
  /* أحجام الخط */
  --font-xs: 0.75rem;
  --font-sm: 0.875rem;
  --font-base: 1rem;
  --font-lg: 1.125rem;
  --font-xl: 1.25rem;
  --font-2xl: 1.5rem;
  --font-3xl: 1.875rem;
  --font-4xl: 2.25rem;
  
  /* المسافات */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* الظلال */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  /* الانتقالات */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  /* نصف القطر */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;
  
  /* العرض الأقصى */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

/* ==========================================
   🔄 إعادة تعيين CSS
   ========================================== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
  direction: rtl;
  text-align: right;
}

/* دعم اللغة الإنجليزية */
[dir="ltr"] {
  direction: ltr;
  text-align: left;
}

/* ==========================================
   📝 تنسيق النصوص
   ========================================== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

h1 { font-size: var(--font-4xl); }
h2 { font-size: var(--font-3xl); }
h3 { font-size: var(--font-2xl); }
h4 { font-size: var(--font-xl); }
h5 { font-size: var(--font-lg); }
h6 { font-size: var(--font-base); }

p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--secondary-color);
  text-decoration: underline;
}

/* ==========================================
   🎛️ عناصر النموذج
   ========================================== */
.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: var(--font-base);
  transition: all var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.1);
}

.form-control::placeholder {
  color: var(--text-muted);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
  color: var(--text-primary);
}

.form-error {
  color: var(--danger-color);
  font-size: var(--font-sm);
  margin-top: var(--spacing-xs);
}

/* ==========================================
   🔘 الأزرار
   ========================================== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-base);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  gap: var(--spacing-xs);
}

.btn:hover {
  background-color: var(--secondary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* أنواع الأزرار */
.btn-primary { background-color: var(--primary-color); }
.btn-secondary { background-color: var(--bg-secondary); }
.btn-success { background-color: var(--success-color); }
.btn-warning { background-color: var(--warning-color); }
.btn-danger { background-color: var(--danger-color); }
.btn-info { background-color: var(--info-color); }

/* أحجام الأزرار */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-sm);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-lg);
}

.btn-outline {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:hover {
  background-color: var(--primary-color);
  color: white;
}

/* ==========================================
   📦 البطاقات
   ========================================== */
.card {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.card-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

/* ==========================================
   🎬 بطاقة المحتوى
   ========================================== */
.content-card {
  position: relative;
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.content-card:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-xl);
}

.content-card-image {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.content-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.content-card:hover .content-card-image img {
  transform: scale(1.1);
}

.content-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(0, 0, 0, 0.7) 100%
  );
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.content-card:hover .content-card-overlay {
  opacity: 1;
}

.content-card-actions {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.content-card:hover .content-card-actions {
  opacity: 1;
}

.content-card-info {
  padding: var(--spacing-md);
}

.content-card-title {
  font-size: var(--font-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.content-card-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-sm);
  color: var(--text-muted);
}

.content-card-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--warning-color);
}

/* ==========================================
   🎥 مشغل الفيديو
   ========================================== */
.video-player {
  position: relative;
  width: 100%;
  background-color: #000;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.video-player video {
  width: 100%;
  height: auto;
  display: block;
}

.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    transparent 100%
  );
  padding: var(--spacing-lg);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.video-player:hover .video-controls {
  opacity: 1;
}

.video-progress {
  width: 100%;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-full);
  margin-bottom: var(--spacing-md);
  cursor: pointer;
}

.video-progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: var(--radius-full);
  transition: width var(--transition-fast);
}

.video-controls-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.video-controls-left,
.video-controls-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.video-control-btn {
  background: none;
  border: none;
  color: white;
  font-size: var(--font-lg);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.video-control-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* ==========================================
   🧭 التنقل
   ========================================== */
.navbar {
  background-color: var(--bg-secondary);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-xl);
  font-weight: 700;
  color: var(--text-primary);
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  list-style: none;
}

.navbar-nav a {
  color: var(--text-secondary);
  font-weight: 500;
  transition: color var(--transition-fast);
}

.navbar-nav a:hover,
.navbar-nav a.active {
  color: var(--primary-color);
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* ==========================================
   🔍 البحث
   ========================================== */
.search-container {
  position: relative;
  max-width: 400px;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  padding-right: 40px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-full);
  color: var(--text-primary);
  font-size: var(--font-base);
}

.search-icon {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.search-suggestion {
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.search-suggestion:hover {
  background-color: var(--bg-secondary);
}

/* ==========================================
   📱 التصميم المتجاوب
   ========================================== */
.container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(var(--spacing-md) * -0.5);
}

.col {
  flex: 1;
  padding: 0 calc(var(--spacing-md) * 0.5);
}

/* الشبكة */
.grid {
  display: grid;
  gap: var(--spacing-lg);
}

.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }
.grid-5 { grid-template-columns: repeat(5, 1fr); }
.grid-6 { grid-template-columns: repeat(6, 1fr); }

/* ==========================================
   📱 الاستعلامات الإعلامية
   ========================================== */
@media (max-width: 1200px) {
  .grid-6 { grid-template-columns: repeat(4, 1fr); }
  .grid-5 { grid-template-columns: repeat(3, 1fr); }
}

@media (max-width: 992px) {
  .grid-4 { grid-template-columns: repeat(3, 1fr); }
  .grid-3 { grid-template-columns: repeat(2, 1fr); }
  
  .navbar-nav {
    display: none;
  }
}

@media (max-width: 768px) {
  .grid-3,
  .grid-4,
  .grid-5,
  .grid-6 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .container {
    padding: 0 var(--spacing-md);
  }
  
  .navbar-container {
    padding: 0 var(--spacing-md);
  }
}

@media (max-width: 576px) {
  .grid-2,
  .grid-3,
  .grid-4,
  .grid-5,
  .grid-6 {
    grid-template-columns: 1fr;
  }
  
  .content-card-image {
    height: 150px;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
}

/* ==========================================
   🎭 الرسوم المتحركة
   ========================================== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* فئات الرسوم المتحركة */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.spin {
  animation: spin 1s linear infinite;
}

/* ==========================================
   🔧 فئات مساعدة
   ========================================== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

.m-0 { margin: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mb-1 { margin-bottom: var(--spacing-xs); }
.ml-1 { margin-left: var(--spacing-xs); }
.mr-1 { margin-right: var(--spacing-xs); }

.p-0 { padding: 0; }
.pt-1 { padding-top: var(--spacing-xs); }
.pb-1 { padding-bottom: var(--spacing-xs); }
.pl-1 { padding-left: var(--spacing-xs); }
.pr-1 { padding-right: var(--spacing-xs); }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-full { border-radius: var(--radius-full); }

.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }

.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

/* ==========================================
   🌙 الوضع الليلي (افتراضي)
   ========================================== */
.dark-mode {
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2a2a2a;
  --bg-card: #1e1e1e;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --border-color: #333333;
}

/* ==========================================
   ☀️ الوضع النهاري
   ========================================== */
.light-mode {
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --bg-card: #ffffff;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --border-color: #dee2e6;
}

/* ==========================================
   🎯 تحسينات الأداء
   ========================================== */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ==========================================
   ♿ إمكانية الوصول
   ========================================== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 9999;
}

.skip-link:focus {
  top: 6px;
}

/* تحسين التباين للنصوص */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --text-secondary: #333333;
    --border-color: #000000;
  }
}

/* تقليل الحركة للمستخدمين الذين يفضلون ذلك */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ==========================================
   🖨️ طباعة
   ========================================== */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}
