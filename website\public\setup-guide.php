<?php
/**
 * 🔧 دليل الإعداد السريع
 * مساعدة المستخدم في إعداد المنصة
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد المنصة - <?php echo SITE_NAME ?? 'منصة البث'; ?></title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px 30px;
        }

        .step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-right: 4px solid #667eea;
        }

        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 15px;
            float: right;
        }

        .step-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #667eea;
        }

        .step-content {
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
            direction: ltr;
            text-align: left;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .alert-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }

        .quick-link {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
        }

        .quick-link:hover {
            border-color: #667eea;
            background: #e8f0fe;
        }

        .quick-link-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .quick-link-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .quick-link-desc {
            font-size: 0.9rem;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إعداد منصة البث</h1>
            <p>دليل سريع لتشغيل المنصة في دقائق</p>
        </div>

        <div class="content">
            <div class="alert alert-error">
                <strong>⚠️ قاعدة البيانات غير متصلة</strong><br>
                يجب إعداد قاعدة البيانات أولاً لتشغيل المنصة.
                <?php if (isset($dbError)): ?>
                <br><strong>الخطأ:</strong> <?php echo htmlspecialchars($dbError); ?>
                <?php endif; ?>
            </div>

            <!-- خطوات الإعداد -->
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-title">تثبيت XAMPP (الطريقة السهلة)</div>
                <div class="step-content">
                    <p>إذا لم يكن لديك خادم PHP و MySQL:</p>
                    <ol>
                        <li>حمل XAMPP من: <a href="https://www.apachefriends.org/download.html" target="_blank">الموقع الرسمي</a></li>
                        <li>ثبت XAMPP واتبع التعليمات</li>
                        <li>افتح XAMPP Control Panel</li>
                        <li>شغل Apache و MySQL</li>
                    </ol>
                </div>
            </div>

            <div class="step">
                <div class="step-number">2</div>
                <div class="step-title">إنشاء قاعدة البيانات</div>
                <div class="step-content">
                    <p><strong>الطريقة الأولى - phpMyAdmin:</strong></p>
                    <ol>
                        <li>افتح: <a href="http://localhost/phpmyadmin" target="_blank">http://localhost/phpmyadmin</a></li>
                        <li>اضغط على "قواعد البيانات" أو "Databases"</li>
                        <li>أنشئ قاعدة بيانات جديدة باسم: <code>streaming_platform</code></li>
                        <li>اختر ترميز: <code>utf8mb4_unicode_ci</code></li>
                        <li>استورد ملف: <code>setup.sql</code></li>
                    </ol>

                    <p><strong>الطريقة الثانية - سطر الأوامر:</strong></p>
                    <div class="code-block">
mysql -u root -p -e "CREATE DATABASE streaming_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -u root -p streaming_platform < setup.sql
                    </div>
                </div>
            </div>

            <div class="step">
                <div class="step-number">3</div>
                <div class="step-title">تحديث إعدادات الاتصال</div>
                <div class="step-content">
                    <p>إذا كانت إعدادات قاعدة البيانات مختلفة، عدل الملف:</p>
                    <div class="code-block">
website/includes/config.php

// أو عدل في أعلى ملف index.php:
define('DB_HOST', 'localhost');
define('DB_NAME', 'streaming_platform');
define('DB_USER', 'root');
define('DB_PASS', ''); // ضع كلمة المرور هنا إذا كانت موجودة
                    </div>
                </div>
            </div>

            <div class="step">
                <div class="step-number">4</div>
                <div class="step-title">اختبار المنصة</div>
                <div class="step-content">
                    <p>بعد إعداد قاعدة البيانات، اختبر المنصة:</p>
                    <ol>
                        <li>أعد تحميل هذه الصفحة</li>
                        <li>أو افتح صفحة الاختبار</li>
                        <li>تأكد من ظهور رسالة "المنصة جاهزة"</li>
                    </ol>
                </div>
            </div>

            <div class="alert alert-info">
                <strong>💡 نصيحة:</strong> إذا كنت تستخدم XAMPP، تأكد من وضع مجلد المشروع في:
                <code>C:\xampp\htdocs\</code> ثم افتح: <code>http://localhost/flutter_application_2/website/public/</code>
            </div>

            <!-- روابط سريعة -->
            <div class="quick-links">
                <a href="test.php" class="quick-link">
                    <div class="quick-link-icon">🧪</div>
                    <div class="quick-link-title">اختبار النظام</div>
                    <div class="quick-link-desc">فحص شامل للمنصة</div>
                </a>

                <a href="../quick-start.html" class="quick-link">
                    <div class="quick-link-icon">🎭</div>
                    <div class="quick-link-title">العرض التوضيحي</div>
                    <div class="quick-link-desc">استكشاف الميزات</div>
                </a>

                <a href="http://localhost/phpmyadmin" target="_blank" class="quick-link">
                    <div class="quick-link-icon">🗄️</div>
                    <div class="quick-link-title">phpMyAdmin</div>
                    <div class="quick-link-desc">إدارة قاعدة البيانات</div>
                </a>

                <a href="javascript:location.reload()" class="quick-link">
                    <div class="quick-link-icon">🔄</div>
                    <div class="quick-link-title">إعادة الاختبار</div>
                    <div class="quick-link-desc">تحديث الصفحة</div>
                </a>
            </div>

            <div class="alert alert-warning">
                <strong>🔐 بيانات الدخول الافتراضية:</strong><br>
                البريد الإلكتروني: <code><EMAIL></code><br>
                كلمة المرور: <code>password</code>
            </div>
        </div>
    </div>

    <script>
        // تحديث تلقائي كل 10 ثوان للتحقق من الاتصال
        setTimeout(function() {
            location.reload();
        }, 10000);

        console.log('🔧 صفحة إعداد منصة البث');
    </script>
</body>
</html>
