{"name": "streaming-platform/website", "description": "منصة بث شاملة للأفلام والمسلسلات - الموقع الإلكتروني المحدث", "type": "project", "version": "1.0.0", "keywords": ["streaming", "video", "movies", "series", "php", "mysql", "arabic", "platform"], "license": "MIT", "authors": [{"name": "فريق منصة البث", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": ">=7.4", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-mbstring": "*", "ext-json": "*", "ext-openssl": "*", "ext-curl": "*", "ext-gd": "*", "ext-fileinfo": "*", "monolog/monolog": "^2.8", "phpmailer/phpmailer": "^6.8", "firebase/php-jwt": "^6.8", "intervention/image": "^2.7", "vlucas/phpdotenv": "^5.5", "ramsey/uuid": "^4.7", "nesbot/carbon": "^2.68", "guzzlehttp/guzzle": "^7.7"}, "require-dev": {"phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.7", "phpstan/phpstan": "^1.10", "friendsofphp/php-cs-fixer": "^3.21"}, "autoload": {"psr-4": {"StreamingPlatform\\": "src/", "StreamingPlatform\\Controllers\\": "src/Controllers/", "StreamingPlatform\\Models\\": "src/Models/", "StreamingPlatform\\Services\\": "src/Services/"}, "files": ["includes/functions.php", "includes/advanced_functions.php"]}, "scripts": {"post-install-cmd": ["@php -r \"if (!file_exists('includes/config.php')) { copy('includes/config.example.php', 'includes/config.php'); }\"", "@php -r \"if (!is_dir('logs')) { mkdir('logs', 0755, true); }\"", "@php -r \"if (!is_dir('cache')) { mkdir('cache', 0755, true); }\"", "@php -r \"if (!is_dir('public/uploads')) { mkdir('public/uploads', 0755, true); }\""], "serve": ["@php -S localhost:8000 -t public"], "test": ["vendor/bin/phpunit"], "setup": ["@composer install", "@post-install-cmd"]}, "config": {"optimize-autoloader": true, "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}