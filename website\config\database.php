<?php
/**
 * 🗄️ فئة قاعدة البيانات - Singleton Pattern
 * إدارة الاتصال بقاعدة البيانات مع حماية من SQL Injection
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}

class Database {
    private static $instance = null;
    private $connection;
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $charset;
    
    /**
     * منشئ خاص لمنع إنشاء كائنات متعددة
     */
    private function __construct() {
        $this->host = DB_HOST;
        $this->dbname = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->charset = DB_CHARSET;
        
        $this->connect();
    }
    
    /**
     * الحصول على مثيل وحيد من قاعدة البيانات
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * الاتصال بقاعدة البيانات
     */
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset={$this->charset}";
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}",
                PDO::ATTR_PERSISTENT => true
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch (PDOException $e) {
            $this->logError("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed");
        }
    }
    
    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * تحضير وتنفيذ استعلام
     */
    public function prepare($sql) {
        try {
            return $this->connection->prepare($sql);
        } catch (PDOException $e) {
            $this->logError("Prepare failed: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("Database query preparation failed");
        }
    }
    
    /**
     * تنفيذ استعلام مباشر
     */
    public function query($sql) {
        try {
            return $this->connection->query($sql);
        } catch (PDOException $e) {
            $this->logError("Query failed: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("Database query failed");
        }
    }
    
    /**
     * الحصول على آخر ID مدرج
     */
    public function lastInsertId() {
        return $this->connection->lastInsertId();
    }
    
    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->connection->commit();
    }
    
    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->connection->rollback();
    }
    
    /**
     * تسجيل الأخطاء
     */
    private function logError($message) {
        if (LOGGING_ENABLED) {
            $logFile = LOG_PATH . 'database_' . date('Y-m-d') . '.log';
            $timestamp = date('Y-m-d H:i:s');
            $logMessage = "[{$timestamp}] ERROR: {$message}" . PHP_EOL;
            
            if (!is_dir(LOG_PATH)) {
                mkdir(LOG_PATH, 0755, true);
            }
            
            file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
        }
    }
    
    /**
     * منع الاستنساخ
     */
    private function __clone() {}
    
    /**
     * منع إلغاء التسلسل
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * 🛠️ فئة مساعدة لبناء الاستعلامات
 */
class QueryBuilder {
    private $table;
    private $select = '*';
    private $where = [];
    private $joins = [];
    private $orderBy = [];
    private $groupBy = [];
    private $having = [];
    private $limit;
    private $offset;
    private $params = [];
    
    public function __construct($table) {
        $this->table = $table;
    }
    
    /**
     * تحديد الحقول المطلوبة
     */
    public function select($fields) {
        $this->select = is_array($fields) ? implode(', ', $fields) : $fields;
        return $this;
    }
    
    /**
     * إضافة شرط WHERE
     */
    public function where($column, $operator, $value = null) {
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }
        
        $placeholder = ':where_' . count($this->where);
        $this->where[] = "{$column} {$operator} {$placeholder}";
        $this->params[$placeholder] = $value;
        
        return $this;
    }
    
    /**
     * إضافة شرط WHERE IN
     */
    public function whereIn($column, $values) {
        $placeholders = [];
        foreach ($values as $i => $value) {
            $placeholder = ':wherein_' . $i;
            $placeholders[] = $placeholder;
            $this->params[$placeholder] = $value;
        }
        
        $this->where[] = "{$column} IN (" . implode(', ', $placeholders) . ")";
        return $this;
    }
    
    /**
     * إضافة JOIN
     */
    public function join($table, $condition, $type = 'INNER') {
        $this->joins[] = "{$type} JOIN {$table} ON {$condition}";
        return $this;
    }
    
    /**
     * إضافة LEFT JOIN
     */
    public function leftJoin($table, $condition) {
        return $this->join($table, $condition, 'LEFT');
    }
    
    /**
     * إضافة RIGHT JOIN
     */
    public function rightJoin($table, $condition) {
        return $this->join($table, $condition, 'RIGHT');
    }
    
    /**
     * إضافة ORDER BY
     */
    public function orderBy($column, $direction = 'ASC') {
        $this->orderBy[] = "{$column} {$direction}";
        return $this;
    }
    
    /**
     * إضافة GROUP BY
     */
    public function groupBy($column) {
        $this->groupBy[] = $column;
        return $this;
    }
    
    /**
     * إضافة HAVING
     */
    public function having($condition) {
        $this->having[] = $condition;
        return $this;
    }
    
    /**
     * تحديد LIMIT
     */
    public function limit($limit, $offset = 0) {
        $this->limit = $limit;
        $this->offset = $offset;
        return $this;
    }
    
    /**
     * بناء استعلام SELECT
     */
    public function build() {
        $sql = "SELECT {$this->select} FROM {$this->table}";
        
        if (!empty($this->joins)) {
            $sql .= ' ' . implode(' ', $this->joins);
        }
        
        if (!empty($this->where)) {
            $sql .= ' WHERE ' . implode(' AND ', $this->where);
        }
        
        if (!empty($this->groupBy)) {
            $sql .= ' GROUP BY ' . implode(', ', $this->groupBy);
        }
        
        if (!empty($this->having)) {
            $sql .= ' HAVING ' . implode(' AND ', $this->having);
        }
        
        if (!empty($this->orderBy)) {
            $sql .= ' ORDER BY ' . implode(', ', $this->orderBy);
        }
        
        if ($this->limit) {
            $sql .= " LIMIT {$this->limit}";
            if ($this->offset) {
                $sql .= " OFFSET {$this->offset}";
            }
        }
        
        return $sql;
    }
    
    /**
     * تنفيذ الاستعلام والحصول على النتائج
     */
    public function get() {
        $db = Database::getInstance();
        $stmt = $db->prepare($this->build());
        $stmt->execute($this->params);
        return $stmt->fetchAll();
    }
    
    /**
     * تنفيذ الاستعلام والحصول على أول نتيجة
     */
    public function first() {
        $this->limit(1);
        $results = $this->get();
        return !empty($results) ? $results[0] : null;
    }
    
    /**
     * عد النتائج
     */
    public function count() {
        $originalSelect = $this->select;
        $this->select = 'COUNT(*) as count';
        
        $result = $this->first();
        $this->select = $originalSelect;
        
        return $result ? (int)$result['count'] : 0;
    }
}

/**
 * 🔧 دالة مساعدة لإنشاء QueryBuilder
 */
function table($tableName) {
    return new QueryBuilder($tableName);
}

?>
