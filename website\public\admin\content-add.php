<?php
/**
 * ➕ إضافة محتوى جديد
 * نموذج إضافة الأفلام والمسلسلات
 */

// تعريف ثابت المنصة
define('STREAMING_PLATFORM', true);

// تحميل ملف التهيئة
require_once dirname(__DIR__) . '/../includes/init.php';

// التحقق من صلاحيات الإدارة
if (!IS_LOGGED_IN || (getCurrentUser()['role'] ?? '') !== 'admin') {
    header('Location: /login');
    exit;
}

$message = '';
$messageType = '';
$formData = [];

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $formData = $_POST;
    $errors = [];
    
    // التحقق من البيانات المطلوبة
    if (empty($formData['title'])) {
        $errors['title'] = 'العنوان مطلوب';
    }
    
    if (empty($formData['type'])) {
        $errors['type'] = 'نوع المحتوى مطلوب';
    }
    
    if (empty($formData['description'])) {
        $errors['description'] = 'الوصف مطلوب';
    }
    
    // إذا لم توجد أخطاء، احفظ البيانات
    if (empty($errors) && DB_CONNECTED) {
        try {
            global $db;
            
            // إنشاء slug من العنوان
            $slug = generateSlug($formData['title']);
            
            // التحقق من عدم تكرار الـ slug
            $existingSlug = $db->selectOne("SELECT id FROM content WHERE slug = ?", [$slug]);
            if ($existingSlug) {
                $slug .= '-' . time();
            }
            
            // بيانات المحتوى
            $contentData = [
                'title' => $formData['title'],
                'slug' => $slug,
                'description' => $formData['description'],
                'type' => $formData['type'],
                'genre' => $formData['genre'] ?? '',
                'release_date' => $formData['release_date'] ?? null,
                'duration' => !empty($formData['duration']) ? intval($formData['duration']) : null,
                'our_rating' => !empty($formData['our_rating']) ? floatval($formData['our_rating']) : null,
                'poster' => $formData['poster'] ?? '',
                'banner' => $formData['banner'] ?? '',
                'video_url' => $formData['video_url'] ?? '',
                'subscription_required' => $formData['subscription_required'] ?? 'free',
                'is_featured' => isset($formData['is_featured']) ? 1 : 0,
                'is_trending' => isset($formData['is_trending']) ? 1 : 0,
                'status' => $formData['status'] ?? 'draft',
                'published_at' => $formData['status'] === 'published' ? date('Y-m-d H:i:s') : null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $contentId = $db->insert('content', $contentData);
            
            if ($contentId) {
                $message = 'تم إضافة المحتوى بنجاح';
                $messageType = 'success';
                
                // إعادة توجيه إلى صفحة التعديل
                header("Location: /admin/content-edit.php?id=$contentId&success=1");
                exit;
            } else {
                $message = 'فشل في إضافة المحتوى';
                $messageType = 'error';
            }
            
        } catch (Exception $e) {
            $message = 'حدث خطأ: ' . $e->getMessage();
            $messageType = 'error';
        }
    } else {
        $message = 'يرجى تصحيح الأخطاء أدناه';
        $messageType = 'error';
    }
}

// دالة مساعدة لإنشاء slug
function generateSlug($text) {
    // تحويل النص العربي إلى slug
    $text = trim($text);
    $text = preg_replace('/[^\p{L}\p{N}\s\-_]/u', '', $text);
    $text = preg_replace('/[\s\-_]+/', '-', $text);
    $text = trim($text, '-');
    
    return strtolower($text);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة محتوى جديد - <?php echo SITE_NAME; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <meta name="csrf-token" content="<?php echo csrf_token(); ?>">
    
    <style>
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .form-section {
            background: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            box-shadow: var(--admin-shadow);
            border: 1px solid var(--admin-border);
            margin-bottom: 1.5rem;
        }
        
        .form-section-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--admin-dark);
            border-bottom: 2px solid var(--admin-primary);
            padding-bottom: 0.5rem;
        }
        
        .checkbox-group {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .form-actions {
            background: var(--admin-light);
            padding: 1.5rem;
            border-radius: 0.75rem;
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
        }
        
        .error-message {
            color: var(--admin-danger);
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        
        .form-control.error {
            border-color: var(--admin-danger);
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- الشريط الجانبي -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <a href="/admin" class="sidebar-logo">
                    🎬 <?php echo SITE_NAME; ?>
                </a>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">الرئيسية</div>
                    <a href="/admin" class="nav-item">
                        <i class="fas fa-tachometer-alt"></i>
                        لوحة التحكم
                    </a>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">المحتوى</div>
                    <a href="/admin/content.php" class="nav-item">
                        <i class="fas fa-film"></i>
                        إدارة المحتوى
                    </a>
                    <a href="/admin/content-add.php" class="nav-item active">
                        <i class="fas fa-plus"></i>
                        إضافة محتوى
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- المحتوى الرئيسي -->
        <main class="admin-main">
            <!-- الرأس -->
            <header class="admin-header">
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="header-title">إضافة محتوى جديد</h1>
                    </div>
                    <div class="header-actions">
                        <a href="/admin/content.php" class="btn btn-outline">
                            <i class="fas fa-arrow-right"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            </header>
            
            <!-- المحتوى -->
            <div class="admin-content">
                <!-- رسائل التنبيه -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
                <?php endif; ?>
                
                <!-- نموذج إضافة المحتوى -->
                <form method="POST" data-ajax="true">
                    <?php echo csrf_field(); ?>
                    
                    <!-- المعلومات الأساسية -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-info-circle"></i>
                            المعلومات الأساسية
                        </h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">عنوان المحتوى *</label>
                                <input type="text" name="title" class="form-control <?php echo isset($errors['title']) ? 'error' : ''; ?>" 
                                       value="<?php echo htmlspecialchars($formData['title'] ?? ''); ?>" 
                                       placeholder="أدخل عنوان الفيلم أو المسلسل" required>
                                <?php if (isset($errors['title'])): ?>
                                <div class="error-message"><?php echo $errors['title']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">نوع المحتوى *</label>
                                <select name="type" class="form-control form-select <?php echo isset($errors['type']) ? 'error' : ''; ?>" required>
                                    <option value="">اختر نوع المحتوى</option>
                                    <option value="movie" <?php echo ($formData['type'] ?? '') === 'movie' ? 'selected' : ''; ?>>فيلم</option>
                                    <option value="series" <?php echo ($formData['type'] ?? '') === 'series' ? 'selected' : ''; ?>>مسلسل</option>
                                    <option value="documentary" <?php echo ($formData['type'] ?? '') === 'documentary' ? 'selected' : ''; ?>>وثائقي</option>
                                </select>
                                <?php if (isset($errors['type'])): ?>
                                <div class="error-message"><?php echo $errors['type']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">الوصف *</label>
                            <textarea name="description" class="form-control <?php echo isset($errors['description']) ? 'error' : ''; ?>" 
                                      rows="4" placeholder="وصف مفصل للمحتوى" required><?php echo htmlspecialchars($formData['description'] ?? ''); ?></textarea>
                            <?php if (isset($errors['description'])): ?>
                            <div class="error-message"><?php echo $errors['description']; ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">التصنيف</label>
                                <input type="text" name="genre" class="form-control" 
                                       value="<?php echo htmlspecialchars($formData['genre'] ?? ''); ?>" 
                                       placeholder="مثل: أكشن، كوميديا، دراما">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">تاريخ الإصدار</label>
                                <input type="date" name="release_date" class="form-control" 
                                       value="<?php echo htmlspecialchars($formData['release_date'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">المدة (بالدقائق)</label>
                                <input type="number" name="duration" class="form-control" 
                                       value="<?php echo htmlspecialchars($formData['duration'] ?? ''); ?>" 
                                       placeholder="120" min="1">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">التقييم (من 10)</label>
                                <input type="number" name="our_rating" class="form-control" 
                                       value="<?php echo htmlspecialchars($formData['our_rating'] ?? ''); ?>" 
                                       placeholder="8.5" min="0" max="10" step="0.1">
                            </div>
                        </div>
                    </div>
                    
                    <!-- الصور والوسائط -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-images"></i>
                            الصور والوسائط
                        </h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">رابط الملصق (Poster)</label>
                                <input type="url" name="poster" class="form-control" 
                                       value="<?php echo htmlspecialchars($formData['poster'] ?? ''); ?>" 
                                       placeholder="https://example.com/poster.jpg">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">رابط البانر (Banner)</label>
                                <input type="url" name="banner" class="form-control" 
                                       value="<?php echo htmlspecialchars($formData['banner'] ?? ''); ?>" 
                                       placeholder="https://example.com/banner.jpg">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">رابط الفيديو</label>
                            <input type="url" name="video_url" class="form-control" 
                                   value="<?php echo htmlspecialchars($formData['video_url'] ?? ''); ?>" 
                                   placeholder="https://example.com/video.mp4">
                            <small class="form-text">رابط مباشر للفيديو أو رابط YouTube/Vimeo</small>
                        </div>
                    </div>
                    
                    <!-- إعدادات النشر -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-cog"></i>
                            إعدادات النشر
                        </h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">نوع الاشتراك المطلوب</label>
                                <select name="subscription_required" class="form-control form-select">
                                    <option value="free" <?php echo ($formData['subscription_required'] ?? 'free') === 'free' ? 'selected' : ''; ?>>مجاني</option>
                                    <option value="basic" <?php echo ($formData['subscription_required'] ?? '') === 'basic' ? 'selected' : ''; ?>>أساسي</option>
                                    <option value="premium" <?php echo ($formData['subscription_required'] ?? '') === 'premium' ? 'selected' : ''; ?>>مميز</option>
                                    <option value="vip" <?php echo ($formData['subscription_required'] ?? '') === 'vip' ? 'selected' : ''; ?>>VIP</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">حالة النشر</label>
                                <select name="status" class="form-control form-select">
                                    <option value="draft" <?php echo ($formData['status'] ?? 'draft') === 'draft' ? 'selected' : ''; ?>>مسودة</option>
                                    <option value="published" <?php echo ($formData['status'] ?? '') === 'published' ? 'selected' : ''; ?>>منشور</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">خيارات إضافية</label>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" name="is_featured" id="is_featured" 
                                           <?php echo isset($formData['is_featured']) ? 'checked' : ''; ?>>
                                    <label for="is_featured">محتوى مميز</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" name="is_trending" id="is_trending" 
                                           <?php echo isset($formData['is_trending']) ? 'checked' : ''; ?>>
                                    <label for="is_trending">محتوى رائج</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="form-actions">
                        <a href="/admin/content.php" class="btn btn-outline">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary" data-original-text="حفظ المحتوى">
                            <i class="fas fa-save"></i>
                            حفظ المحتوى
                        </button>
                    </div>
                </form>
            </div>
        </main>
    </div>
    
    <!-- JavaScript -->
    <script src="/assets/js/admin.js"></script>
    
    <script>
        // التحقق من صحة النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            const title = document.querySelector('input[name="title"]').value.trim();
            const type = document.querySelector('select[name="type"]').value;
            const description = document.querySelector('textarea[name="description"]').value.trim();
            
            if (!title || !type || !description) {
                e.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }
        });
    </script>
</body>
</html>
