# 📱 دليل تطوير التطبيقات الجوالة - منصة البث العربية

## 🎯 **نظرة عامة**

هذا الدليل يوضح كيفية تطوير تطبيقات الجوال لمنصة البث العربية باستخدام Flutter للتطبيق المتعدد المنصات و Swift/Kotlin للتطبيقات الأصلية.

### **استراتيجية التطوير الجوال**
- **Flutter**: التطبيق الأساسي متعدد المنصات
- **iOS Native**: تطبيق iOS محسن للأداء
- **Android Native**: تطبيق Android محسن للأداء
- **React Native**: تطبيق بديل للتطوير السريع
- **PWA**: تطبيق ويب تقدمي للمتصفحات

---

## 🦋 **Flutter Development**

### **Project Structure**

```
mobile_app/
├── lib/
│   ├── main.dart
│   ├── app/
│   │   ├── app.dart
│   │   ├── routes/
│   │   ├── themes/
│   │   └── constants/
│   ├── core/
│   │   ├── network/
│   │   ├── storage/
│   │   ├── utils/
│   │   └── errors/
│   ├── features/
│   │   ├── auth/
│   │   ├── home/
│   │   ├── movies/
│   │   ├── player/
│   │   └── profile/
│   ├── shared/
│   │   ├── widgets/
│   │   ├── models/
│   │   └── services/
│   └── l10n/
├── android/
├── ios/
├── test/
├── integration_test/
└── assets/
```

### **Main Application Setup**

```dart
// lib/main.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:streaming_app/app/app.dart';
import 'package:streaming_app/core/di/injection_container.dart' as di;
import 'package:streaming_app/core/utils/app_bloc_observer.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize dependencies
  await di.init();
  
  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);
  
  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
    ),
  );
  
  // Set bloc observer
  Bloc.observer = AppBlocObserver();
  
  runApp(const StreamingApp());
}

class StreamingApp extends StatelessWidget {
  const StreamingApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => di.sl<AuthBloc>()),
        BlocProvider(create: (_) => di.sl<ThemeBloc>()),
        BlocProvider(create: (_) => di.sl<LanguageBloc>()),
      ],
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themeState) {
          return BlocBuilder<LanguageBloc, LanguageState>(
            builder: (context, languageState) {
              return MaterialApp.router(
                title: 'منصة البث العربية',
                debugShowCheckedModeBanner: false,
                theme: themeState.themeData,
                locale: languageState.locale,
                localizationsDelegates: const [
                  AppLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                supportedLocales: const [
                  Locale('ar', 'SA'),
                  Locale('en', 'US'),
                ],
                routerConfig: AppRouter.router,
              );
            },
          );
        },
      ),
    );
  }
}
```

### **Video Player Implementation**

```dart
// lib/features/player/widgets/video_player_widget.dart
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';

class VideoPlayerWidget extends StatefulWidget {
  final String videoUrl;
  final String? subtitleUrl;
  final Function(Duration)? onProgressChanged;
  final Duration? initialPosition;

  const VideoPlayerWidget({
    Key? key,
    required this.videoUrl,
    this.subtitleUrl,
    this.onProgressChanged,
    this.initialPosition,
  }) : super(key: key);

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late VideoPlayerController _videoPlayerController;
  ChewieController? _chewieController;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    try {
      _videoPlayerController = VideoPlayerController.network(widget.videoUrl);
      await _videoPlayerController.initialize();

      _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController,
        autoPlay: true,
        looping: false,
        allowFullScreen: true,
        allowMuting: true,
        showControls: true,
        materialProgressColors: ChewieProgressColors(
          playedColor: Theme.of(context).primaryColor,
          handleColor: Theme.of(context).primaryColor,
          backgroundColor: Colors.grey,
          bufferedColor: Colors.grey[300]!,
        ),
        placeholder: Container(
          color: Colors.black,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
        autoInitialize: true,
        startAt: widget.initialPosition,
      );

      // Listen to position changes
      _videoPlayerController.addListener(() {
        if (widget.onProgressChanged != null) {
          widget.onProgressChanged!(_videoPlayerController.value.position);
        }
      });

      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      debugPrint('Error initializing video player: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized || _chewieController == null) {
      return Container(
        height: 200,
        color: Colors.black,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return AspectRatio(
      aspectRatio: _videoPlayerController.value.aspectRatio,
      child: Chewie(controller: _chewieController!),
    );
  }

  @override
  void dispose() {
    _videoPlayerController.dispose();
    _chewieController?.dispose();
    super.dispose();
  }
}
```

### **State Management with BLoC**

```dart
// lib/features/movies/bloc/movies_bloc.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:streaming_app/features/movies/domain/entities/movie.dart';
import 'package:streaming_app/features/movies/domain/usecases/get_movies.dart';

// Events
abstract class MoviesEvent extends Equatable {
  const MoviesEvent();

  @override
  List<Object> get props => [];
}

class GetMoviesEvent extends MoviesEvent {
  final int page;
  final String? genre;
  final String? searchQuery;

  const GetMoviesEvent({
    this.page = 1,
    this.genre,
    this.searchQuery,
  });

  @override
  List<Object> get props => [page, genre ?? '', searchQuery ?? ''];
}

class RefreshMoviesEvent extends MoviesEvent {}

// States
abstract class MoviesState extends Equatable {
  const MoviesState();

  @override
  List<Object> get props => [];
}

class MoviesInitial extends MoviesState {}

class MoviesLoading extends MoviesState {}

class MoviesLoaded extends MoviesState {
  final List<Movie> movies;
  final bool hasReachedMax;
  final int currentPage;

  const MoviesLoaded({
    required this.movies,
    required this.hasReachedMax,
    required this.currentPage,
  });

  MoviesLoaded copyWith({
    List<Movie>? movies,
    bool? hasReachedMax,
    int? currentPage,
  }) {
    return MoviesLoaded(
      movies: movies ?? this.movies,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
    );
  }

  @override
  List<Object> get props => [movies, hasReachedMax, currentPage];
}

class MoviesError extends MoviesState {
  final String message;

  const MoviesError({required this.message});

  @override
  List<Object> get props => [message];
}

// BLoC
class MoviesBloc extends Bloc<MoviesEvent, MoviesState> {
  final GetMovies getMovies;

  MoviesBloc({required this.getMovies}) : super(MoviesInitial()) {
    on<GetMoviesEvent>(_onGetMovies);
    on<RefreshMoviesEvent>(_onRefreshMovies);
  }

  Future<void> _onGetMovies(
    GetMoviesEvent event,
    Emitter<MoviesState> emit,
  ) async {
    if (state is MoviesLoading) return;

    final currentState = state;
    List<Movie> currentMovies = [];
    
    if (currentState is MoviesLoaded && event.page > 1) {
      currentMovies = currentState.movies;
    } else {
      emit(MoviesLoading());
    }

    final result = await getMovies(GetMoviesParams(
      page: event.page,
      genre: event.genre,
      searchQuery: event.searchQuery,
    ));

    result.fold(
      (failure) => emit(MoviesError(message: failure.message)),
      (movies) {
        final allMovies = event.page == 1 ? movies : currentMovies + movies;
        emit(MoviesLoaded(
          movies: allMovies,
          hasReachedMax: movies.length < 20,
          currentPage: event.page,
        ));
      },
    );
  }

  Future<void> _onRefreshMovies(
    RefreshMoviesEvent event,
    Emitter<MoviesState> emit,
  ) async {
    emit(MoviesInitial());
    add(const GetMoviesEvent(page: 1));
  }
}
```

### **Network Layer**

```dart
// lib/core/network/api_client.dart
import 'package:dio/dio.dart';
import 'package:streaming_app/core/constants/api_constants.dart';
import 'package:streaming_app/core/storage/secure_storage.dart';

class ApiClient {
  late final Dio _dio;
  final SecureStorage _secureStorage;

  ApiClient(this._secureStorage) {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConstants.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final token = await _secureStorage.getToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onResponse: (response, handler) {
        handler.next(response);
      },
      onError: (error, handler) async {
        if (error.response?.statusCode == 401) {
          await _secureStorage.deleteToken();
          // Navigate to login
        }
        handler.next(error);
      },
    ));

    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (object) => debugPrint(object.toString()),
    ));
  }

  Future<Response> get(String path, {Map<String, dynamic>? queryParameters}) {
    return _dio.get(path, queryParameters: queryParameters);
  }

  Future<Response> post(String path, {dynamic data}) {
    return _dio.post(path, data: data);
  }

  Future<Response> put(String path, {dynamic data}) {
    return _dio.put(path, data: data);
  }

  Future<Response> delete(String path) {
    return _dio.delete(path);
  }
}
```

---

## 🍎 **iOS Native Development**

### **SwiftUI Video Player**

```swift
// iOS/StreamingApp/Views/VideoPlayerView.swift
import SwiftUI
import AVKit
import AVFoundation

struct VideoPlayerView: UIViewControllerRepresentable {
    let videoURL: URL
    let onProgressChanged: ((Double) -> Void)?
    let initialTime: Double?
    
    func makeUIViewController(context: Context) -> AVPlayerViewController {
        let controller = AVPlayerViewController()
        let player = AVPlayer(url: videoURL)
        
        controller.player = player
        controller.showsPlaybackControls = true
        controller.allowsPictureInPicturePlayback = true
        
        // Set initial time if provided
        if let initialTime = initialTime {
            let time = CMTime(seconds: initialTime, preferredTimescale: 600)
            player.seek(to: time)
        }
        
        // Add progress observer
        if let onProgressChanged = onProgressChanged {
            let interval = CMTime(seconds: 1.0, preferredTimescale: 600)
            player.addPeriodicTimeObserver(forInterval: interval, queue: .main) { time in
                onProgressChanged(time.seconds)
            }
        }
        
        return controller
    }
    
    func updateUIViewController(_ uiViewController: AVPlayerViewController, context: Context) {
        // Update if needed
    }
}

struct ContentView: View {
    @State private var currentTime: Double = 0
    
    var body: some View {
        NavigationView {
            VStack {
                VideoPlayerView(
                    videoURL: URL(string: "https://example.com/video.mp4")!,
                    onProgressChanged: { time in
                        currentTime = time
                    },
                    initialTime: 0
                )
                .frame(height: 250)
                
                Text("Current Time: \(Int(currentTime))s")
                    .padding()
                
                Spacer()
            }
            .navigationTitle("منصة البث العربية")
        }
    }
}
```

### **iOS Network Manager**

```swift
// iOS/StreamingApp/Services/NetworkManager.swift
import Foundation
import Combine

class NetworkManager: ObservableObject {
    static let shared = NetworkManager()
    private let session = URLSession.shared
    private let baseURL = "https://api.streaming-platform.com/v1"
    
    private init() {}
    
    func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod = .GET,
        body: Data? = nil,
        type: T.Type
    ) -> AnyPublisher<T, Error> {
        guard let url = URL(string: baseURL + endpoint) else {
            return Fail(error: NetworkError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Add auth token if available
        if let token = KeychainManager.shared.getToken() {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        if let body = body {
            request.httpBody = body
        }
        
        return session.dataTaskPublisher(for: request)
            .map(\.data)
            .decode(type: type, decoder: JSONDecoder())
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
}

enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
}

enum NetworkError: Error {
    case invalidURL
    case noData
    case decodingError
}
```

---

## 🤖 **Android Native Development**

### **Kotlin Video Player**

```kotlin
// android/app/src/main/java/com/streamingplatform/VideoPlayerActivity.kt
package com.streamingplatform

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.ui.PlayerView
import com.streamingplatform.ui.theme.StreamingPlatformTheme

class VideoPlayerActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val videoUrl = intent.getStringExtra("video_url") ?: ""
        
        setContent {
            StreamingPlatformTheme {
                VideoPlayerScreen(videoUrl = videoUrl)
            }
        }
    }
}

@Composable
fun VideoPlayerScreen(videoUrl: String) {
    val context = LocalContext.current
    var currentPosition by remember { mutableStateOf(0L) }
    
    val exoPlayer = remember {
        ExoPlayer.Builder(context).build().apply {
            setMediaItem(MediaItem.fromUri(videoUrl))
            prepare()
            playWhenReady = true
        }
    }
    
    DisposableEffect(exoPlayer) {
        onDispose {
            exoPlayer.release()
        }
    }
    
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        AndroidView(
            factory = { context ->
                PlayerView(context).apply {
                    player = exoPlayer
                    useController = true
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(250.dp)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "الوقت الحالي: ${currentPosition / 1000}s",
            style = MaterialTheme.typography.bodyLarge
        )
        
        LaunchedEffect(exoPlayer) {
            while (true) {
                currentPosition = exoPlayer.currentPosition
                kotlinx.coroutines.delay(1000)
            }
        }
    }
}
```

### **Android Network Service**

```kotlin
// android/app/src/main/java/com/streamingplatform/network/ApiService.kt
package com.streamingplatform.network

import retrofit2.Response
import retrofit2.http.*

interface ApiService {
    @GET("movies")
    suspend fun getMovies(
        @Query("page") page: Int = 1,
        @Query("genre") genre: String? = null,
        @Query("search") search: String? = null
    ): Response<MoviesResponse>
    
    @GET("movies/{id}")
    suspend fun getMovie(@Path("id") id: Int): Response<Movie>
    
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequest): Response<AuthResponse>
    
    @GET("user/profile")
    suspend fun getUserProfile(@Header("Authorization") token: String): Response<User>
    
    @POST("user/favorites")
    suspend fun addToFavorites(
        @Header("Authorization") token: String,
        @Body request: FavoriteRequest
    ): Response<ApiResponse>
}

// Network Module
class NetworkModule {
    companion object {
        private const val BASE_URL = "https://api.streaming-platform.com/v1/"
        
        fun provideApiService(): ApiService {
            val okHttpClient = OkHttpClient.Builder()
                .addInterceptor(AuthInterceptor())
                .addInterceptor(LoggingInterceptor())
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build()
            
            val retrofit = Retrofit.Builder()
                .baseUrl(BASE_URL)
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
            
            return retrofit.create(ApiService::class.java)
        }
    }
}
```

---

## ⚛️ **React Native Alternative**

### **React Native Video Player**

```javascript
// src/components/VideoPlayer.js
import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Video from 'react-native-video';
import Orientation from 'react-native-orientation-locker';

const VideoPlayer = ({ videoUrl, onProgressChange, initialTime = 0 }) => {
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [paused, setPaused] = useState(false);
  const videoRef = useRef(null);

  useEffect(() => {
    // Lock orientation to landscape for video
    Orientation.lockToLandscape();
    
    return () => {
      Orientation.unlockAllOrientations();
    };
  }, []);

  const onProgress = (data) => {
    setCurrentTime(data.currentTime);
    if (onProgressChange) {
      onProgressChange(data.currentTime);
    }
  };

  const onLoad = (data) => {
    setDuration(data.duration);
    if (initialTime > 0) {
      videoRef.current?.seek(initialTime);
    }
  };

  return (
    <View style={styles.container}>
      <Video
        ref={videoRef}
        source={{ uri: videoUrl }}
        style={styles.video}
        controls={true}
        resizeMode="contain"
        paused={paused}
        onProgress={onProgress}
        onLoad={onLoad}
        onError={(error) => console.log('Video Error:', error)}
        progressUpdateInterval={1000}
      />
      
      <View style={styles.info}>
        <Text style={styles.timeText}>
          {Math.floor(currentTime)}s / {Math.floor(duration)}s
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  video: {
    width: Dimensions.get('window').width,
    height: 250,
  },
  info: {
    position: 'absolute',
    bottom: 50,
    left: 20,
  },
  timeText: {
    color: 'white',
    fontSize: 16,
  },
});

export default VideoPlayer;
```

---

## 🔄 **Cross-Platform State Management**

### **Redux Toolkit Setup**

```javascript
// src/store/store.js
import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import moviesSlice from './slices/moviesSlice';
import playerSlice from './slices/playerSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    movies: moviesSlice,
    player: playerSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

### **Movies Slice**

```javascript
// src/store/slices/moviesSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiService } from '../../services/apiService';

export const fetchMovies = createAsyncThunk(
  'movies/fetchMovies',
  async ({ page = 1, genre, search }, { rejectWithValue }) => {
    try {
      const response = await apiService.getMovies({ page, genre, search });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

const moviesSlice = createSlice({
  name: 'movies',
  initialState: {
    movies: [],
    loading: false,
    error: null,
    hasMore: true,
    currentPage: 1,
  },
  reducers: {
    clearMovies: (state) => {
      state.movies = [];
      state.currentPage = 1;
      state.hasMore = true;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchMovies.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMovies.fulfilled, (state, action) => {
        state.loading = false;
        const { movies, pagination } = action.payload;
        
        if (pagination.page === 1) {
          state.movies = movies;
        } else {
          state.movies = [...state.movies, ...movies];
        }
        
        state.currentPage = pagination.page;
        state.hasMore = pagination.page < pagination.totalPages;
      })
      .addCase(fetchMovies.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'حدث خطأ في تحميل الأفلام';
      });
  },
});

export const { clearMovies, setError } = moviesSlice.actions;
export default moviesSlice.reducer;
```

---

## 📱 **PWA Configuration**

### **Service Worker for Mobile**

```javascript
// public/sw.js
const CACHE_NAME = 'streaming-platform-mobile-v1';
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json',
  '/offline.html'
];

// Install event
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

// Fetch event
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request);
      })
      .catch(() => {
        // Return offline page for navigation requests
        if (event.request.mode === 'navigate') {
          return caches.match('/offline.html');
        }
      })
  );
});

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  // Sync offline data when connection is restored
  const offlineActions = await getOfflineActions();
  for (const action of offlineActions) {
    try {
      await syncAction(action);
      await removeOfflineAction(action.id);
    } catch (error) {
      console.log('Sync failed for action:', action.id);
    }
  }
}
```

---

## 🔧 **Build and Deployment**

### **Flutter Build Script**

```bash
#!/bin/bash
# scripts/build-flutter.sh

set -e

echo "🦋 Building Flutter app..."

# Clean previous builds
flutter clean
flutter pub get

# Build for Android
echo "🤖 Building Android APK..."
flutter build apk --release --split-per-abi

echo "🤖 Building Android App Bundle..."
flutter build appbundle --release

# Build for iOS (macOS only)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 Building iOS app..."
    flutter build ios --release --no-codesign
fi

# Build for Web
echo "🌐 Building Web app..."
flutter build web --release

echo "✅ Flutter build completed!"
echo "📱 APK: build/app/outputs/flutter-apk/"
echo "📱 App Bundle: build/app/outputs/bundle/release/"
echo "🌐 Web: build/web/"
```

### **CI/CD for Mobile**

```yaml
# .github/workflows/mobile-ci.yml
name: Mobile CI/CD

on:
  push:
    branches: [main, develop]
    paths: ['mobile_app/**']
  pull_request:
    branches: [main]
    paths: ['mobile_app/**']

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
    
    - name: Install dependencies
      run: flutter pub get
      working-directory: mobile_app
    
    - name: Run tests
      run: flutter test
      working-directory: mobile_app
    
    - name: Analyze code
      run: flutter analyze
      working-directory: mobile_app

  build-android:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
    
    - name: Build APK
      run: flutter build apk --release
      working-directory: mobile_app
    
    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: app-release.apk
        path: mobile_app/build/app/outputs/flutter-apk/app-release.apk

  build-ios:
    needs: test
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
    
    - name: Build iOS
      run: flutter build ios --release --no-codesign
      working-directory: mobile_app
```

---

**📱 دليل تطوير تطبيقات جوالة شامل ومتطور لمنصة البث العربية**

آخر تحديث: 15 يناير 2024
