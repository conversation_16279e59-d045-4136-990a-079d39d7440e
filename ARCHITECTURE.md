# 🏗️ هندسة النظام - منصة البث العربية

## 🎯 **نظرة عامة**

هذا الدليل يوضح الهندسة الشاملة لمنصة البث العربية، بما في ذلك البنية التحتية، المكونات، وتدفق البيانات.

### **المبادئ الأساسية**
- **قابلية التوسع**: نظام قابل للتوسع الأفقي والعمودي
- **الموثوقية**: توفر عالي مع تحمل الأخطاء
- **الأداء**: استجابة سريعة وإنتاجية عالية
- **الأمان**: حماية شاملة للبيانات والمستخدمين
- **المرونة**: سهولة التطوير والصيانة

---

## 🏛️ **البنية العامة**

### **نمط الهندسة: Microservices + Monolith Hybrid**

```
┌─────────────────────────────────────────────────────────────┐
│                    Load Balancer (Nginx)                    │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                     CDN (CloudFlare)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                  Web Application                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   Frontend  │ │   Backend   │ │     API     │           │
│  │   (React)   │ │   (Laravel) │ │  (RESTful)  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                   Services Layer                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   Auth      │ │   Media     │ │  Analytics  │           │
│  │  Service    │ │  Service    │ │   Service   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                   Data Layer                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   MySQL     │ │    Redis    │ │ Elasticsearch│           │
│  │ (Primary)   │ │   (Cache)   │ │   (Search)  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

---

## 🔧 **المكونات الأساسية**

### **1. طبقة العرض (Presentation Layer)**

#### **Frontend (React + TypeScript)**
```typescript
// Component Architecture
src/
├── components/           # مكونات قابلة للإعادة
│   ├── common/          # مكونات عامة
│   ├── forms/           # نماذج
│   ├── layout/          # تخطيط الصفحة
│   └── media/           # مكونات الوسائط
├── pages/               # صفحات التطبيق
├── hooks/               # React Hooks مخصصة
├── services/            # خدمات API
├── store/               # إدارة الحالة (Redux)
├── utils/               # وظائف مساعدة
└── types/               # تعريفات TypeScript
```

#### **State Management Pattern**
```typescript
// Redux Store Structure
interface RootState {
  auth: AuthState;
  movies: MoviesState;
  player: PlayerState;
  ui: UIState;
  cache: CacheState;
}

// Action Types
enum ActionTypes {
  FETCH_MOVIES_REQUEST = 'FETCH_MOVIES_REQUEST',
  FETCH_MOVIES_SUCCESS = 'FETCH_MOVIES_SUCCESS',
  FETCH_MOVIES_FAILURE = 'FETCH_MOVIES_FAILURE',
  PLAY_VIDEO = 'PLAY_VIDEO',
  PAUSE_VIDEO = 'PAUSE_VIDEO',
}
```

### **2. طبقة التطبيق (Application Layer)**

#### **Backend (Laravel + PHP 8.1)**
```php
<?php
// Service Layer Architecture
app/
├── Http/
│   ├── Controllers/     # متحكمات HTTP
│   ├── Middleware/      # وسطاء HTTP
│   └── Requests/        # طلبات التحقق
├── Services/            # منطق الأعمال
│   ├── AuthService.php
│   ├── MovieService.php
│   ├── StreamingService.php
│   └── AnalyticsService.php
├── Repositories/        # طبقة الوصول للبيانات
│   ├── MovieRepository.php
│   ├── UserRepository.php
│   └── Contracts/       # واجهات المستودعات
├── Models/              # نماذج البيانات
├── Events/              # أحداث النظام
├── Listeners/           # مستمعي الأحداث
└── Jobs/                # مهام الخلفية
```

#### **Service Pattern Example**
```php
<?php
namespace App\Services;

class MovieService
{
    public function __construct(
        private MovieRepository $movieRepository,
        private CacheService $cacheService,
        private AnalyticsService $analyticsService
    ) {}
    
    public function getPopularMovies(int $limit = 20): Collection
    {
        return $this->cacheService->remember(
            "popular_movies_{$limit}",
            3600,
            fn() => $this->movieRepository->getPopular($limit)
        );
    }
    
    public function streamMovie(int $movieId, User $user): StreamResponse
    {
        $movie = $this->movieRepository->findOrFail($movieId);
        
        // تسجيل المشاهدة
        $this->analyticsService->recordView($movie, $user);
        
        // إنشاء رابط البث
        return $this->generateStreamUrl($movie, $user);
    }
}
```

### **3. طبقة البيانات (Data Layer)**

#### **Database Schema**
```sql
-- المستخدمون
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    subscription_type ENUM('free', 'premium', 'vip') DEFAULT 'free',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_subscription (subscription_type)
);

-- الأفلام
CREATE TABLE movies (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    poster_url VARCHAR(500),
    trailer_url VARCHAR(500),
    video_url VARCHAR(500),
    duration INT NOT NULL, -- بالدقائق
    year YEAR NOT NULL,
    rating DECIMAL(3,1) DEFAULT 0.0,
    genre_id BIGINT,
    director_id BIGINT,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_title (title),
    INDEX idx_slug (slug),
    INDEX idx_year (year),
    INDEX idx_rating (rating),
    INDEX idx_genre (genre_id),
    INDEX idx_status (status),
    FULLTEXT idx_search (title, description)
);

-- المشاهدات
CREATE TABLE views (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    movie_id BIGINT,
    duration_watched INT DEFAULT 0, -- بالثواني
    quality VARCHAR(10) DEFAULT '720p',
    device_type VARCHAR(50),
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_movie (user_id, movie_id),
    INDEX idx_movie_date (movie_id, created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE
);
```

---

## 🔄 **تدفق البيانات (Data Flow)**

### **1. تدفق المصادقة**
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as Auth API
    participant D as Database
    participant R as Redis
    
    U->>F: تسجيل دخول
    F->>A: POST /api/auth/login
    A->>D: التحقق من البيانات
    D-->>A: بيانات المستخدم
    A->>R: حفظ الجلسة
    A-->>F: JWT Token
    F-->>U: تسجيل دخول ناجح
```

### **2. تدفق مشاهدة الفيديو**
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant M as Media API
    participant S as Streaming Service
    participant A as Analytics
    participant C as CDN
    
    U->>F: طلب مشاهدة فيلم
    F->>M: GET /api/movies/{id}/stream
    M->>S: إنشاء رابط البث
    S->>A: تسجيل بداية المشاهدة
    S-->>M: رابط البث المؤقت
    M-->>F: معلومات البث
    F->>C: طلب ملف الفيديو
    C-->>F: تدفق الفيديو
    F-->>U: تشغيل الفيديو
```

---

## 🏗️ **أنماط التصميم المستخدمة**

### **1. Repository Pattern**
```php
<?php
interface MovieRepositoryInterface
{
    public function find(int $id): ?Movie;
    public function getPopular(int $limit): Collection;
    public function search(string $query): Collection;
    public function create(array $data): Movie;
    public function update(int $id, array $data): Movie;
    public function delete(int $id): bool;
}

class EloquentMovieRepository implements MovieRepositoryInterface
{
    public function find(int $id): ?Movie
    {
        return Movie::find($id);
    }
    
    public function getPopular(int $limit): Collection
    {
        return Movie::orderBy('rating', 'desc')
                   ->orderBy('views_count', 'desc')
                   ->limit($limit)
                   ->get();
    }
}
```

### **2. Observer Pattern**
```php
<?php
// Event
class MovieViewed
{
    public function __construct(
        public Movie $movie,
        public User $user,
        public int $duration
    ) {}
}

// Listener
class UpdateMovieStatistics
{
    public function handle(MovieViewed $event): void
    {
        $event->movie->increment('views_count');
        $event->movie->updateRating();
        
        // تحديث إحصائيات المستخدم
        $event->user->updateWatchHistory($event->movie);
    }
}
```

### **3. Factory Pattern**
```php
<?php
class StreamUrlFactory
{
    public static function create(Movie $movie, User $user): string
    {
        $quality = self::determineQuality($user->subscription_type);
        $token = self::generateSecureToken($movie, $user);
        
        return match($user->subscription_type) {
            'premium', 'vip' => self::createPremiumUrl($movie, $quality, $token),
            default => self::createFreeUrl($movie, $token)
        };
    }
    
    private static function determineQuality(string $subscription): string
    {
        return match($subscription) {
            'vip' => '4K',
            'premium' => '1080p',
            default => '720p'
        };
    }
}
```

---

## 📊 **قابلية التوسع (Scalability)**

### **التوسع الأفقي**
```yaml
# Kubernetes Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: streaming-platform-web
spec:
  replicas: 3
  selector:
    matchLabels:
      app: streaming-platform-web
  template:
    metadata:
      labels:
        app: streaming-platform-web
    spec:
      containers:
      - name: web
        image: streaming-platform:latest
        ports:
        - containerPort: 80
        env:
        - name: DB_HOST
          value: "mysql-service"
        - name: REDIS_HOST
          value: "redis-service"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

### **Database Sharding Strategy**
```php
<?php
class DatabaseShardingService
{
    private array $shards = [
        'shard1' => 'mysql-shard1.example.com',
        'shard2' => 'mysql-shard2.example.com',
        'shard3' => 'mysql-shard3.example.com',
    ];
    
    public function getShardForUser(int $userId): string
    {
        $shardIndex = $userId % count($this->shards);
        return array_values($this->shards)[$shardIndex];
    }
    
    public function getShardForMovie(int $movieId): string
    {
        // توزيع الأفلام حسب النوع أو السنة
        $movie = Movie::find($movieId);
        return $this->getShardByGenre($movie->genre_id);
    }
}
```

---

## 🔒 **الأمان (Security Architecture)**

### **طبقات الأمان**
```
┌─────────────────────────────────────────┐
│           WAF (Web Application          │
│              Firewall)                  │
└─────────────┬───────────────────────────┘
              │
┌─────────────┴───────────────────────────┐
│        Load Balancer + DDoS             │
│           Protection                    │
└─────────────┬───────────────────────────┘
              │
┌─────────────┴───────────────────────────┐
│      Application Security               │
│   • JWT Authentication                 │
│   • CSRF Protection                    │
│   • XSS Prevention                     │
│   • SQL Injection Prevention           │
└─────────────┬───────────────────────────┘
              │
┌─────────────┴───────────────────────────┐
│        Database Security                │
│   • Encryption at Rest                 │
│   • Connection Encryption              │
│   • Access Control                     │
└─────────────────────────────────────────┘
```

### **JWT Security Implementation**
```php
<?php
class JWTService
{
    public function generateToken(User $user): string
    {
        $payload = [
            'sub' => $user->id,
            'iat' => time(),
            'exp' => time() + (24 * 60 * 60), // 24 hours
            'aud' => config('app.url'),
            'iss' => config('app.name'),
            'jti' => Str::uuid(),
            'permissions' => $user->permissions->pluck('name')->toArray()
        ];
        
        return JWT::encode($payload, config('jwt.secret'), 'HS256');
    }
    
    public function validateToken(string $token): ?User
    {
        try {
            $decoded = JWT::decode($token, config('jwt.secret'), ['HS256']);
            return User::find($decoded->sub);
        } catch (Exception $e) {
            return null;
        }
    }
}
```

---

## 📈 **المراقبة والتحليل**

### **Metrics Collection**
```php
<?php
class MetricsCollector
{
    public function recordApiCall(string $endpoint, float $duration, int $statusCode): void
    {
        $metrics = [
            'endpoint' => $endpoint,
            'duration' => $duration,
            'status_code' => $statusCode,
            'timestamp' => microtime(true),
            'memory_usage' => memory_get_usage(),
            'peak_memory' => memory_get_peak_usage()
        ];
        
        // إرسال للـ monitoring system
        $this->sendToPrometheus($metrics);
        $this->sendToElasticsearch($metrics);
    }
    
    public function recordUserAction(string $action, User $user, array $context = []): void
    {
        $event = [
            'action' => $action,
            'user_id' => $user->id,
            'context' => $context,
            'timestamp' => now(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ];
        
        // تسجيل في قاعدة البيانات
        UserActivity::create($event);
        
        // إرسال للتحليل الفوري
        $this->sendToAnalytics($event);
    }
}
```

---

## 🔄 **CI/CD Pipeline**

### **Deployment Strategy**
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run Tests
        run: |
          composer install
          php artisan test
          npm test
  
  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Build Docker Image
        run: |
          docker build -t streaming-platform:${{ github.sha }} .
          docker push registry.example.com/streaming-platform:${{ github.sha }}
  
  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Kubernetes
        run: |
          kubectl set image deployment/streaming-platform-web \
            web=registry.example.com/streaming-platform:${{ github.sha }}
          kubectl rollout status deployment/streaming-platform-web
```

---

## 📚 **التوثيق والمراجع**

### **Architecture Decision Records (ADRs)**
- [ADR-001: اختيار Laravel كإطار عمل خلفي](docs/adr/001-laravel-backend.md)
- [ADR-002: استخدام React للواجهة الأمامية](docs/adr/002-react-frontend.md)
- [ADR-003: نمط Microservices Hybrid](docs/adr/003-microservices-hybrid.md)
- [ADR-004: استراتيجية التخزين المؤقت](docs/adr/004-caching-strategy.md)

### **API Documentation**
- [REST API Reference](API_DOCUMENTATION.md)
- [GraphQL Schema](docs/graphql-schema.md)
- [WebSocket Events](docs/websocket-events.md)

---

**🏗️ هندسة نظام شاملة ومتطورة لمنصة البث العربية**

آخر تحديث: 15 يناير 2024
