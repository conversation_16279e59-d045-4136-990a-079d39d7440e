import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';

import '../constants/app_constants.dart';
import '../models/content_model.dart';
import '../services/api_service.dart';

/// 🎬 مزود المحتوى
/// يدير حالة المحتوى في التطبيق
final contentProvider = StateNotifierProvider<ContentNotifier, ContentState>((ref) {
  return ContentNotifier(ref.read(apiServiceProvider));
});

/// 📊 حالة المحتوى
class ContentState {
  final List<ContentModel> featuredContent;
  final List<ContentModel> latestMovies;
  final List<ContentModel> latestSeries;
  final List<ContentModel> trendingContent;
  final List<ContentModel> topRatedContent;
  final List<ContentModel> continueWatching;
  final List<ContentModel> myList;
  final List<ContentModel> searchResults;
  final bool isLoading;
  final String? error;
  final int currentPage;
  final bool hasMoreData;

  const ContentState({
    this.featuredContent = const [],
    this.latestMovies = const [],
    this.latestSeries = const [],
    this.trendingContent = const [],
    this.topRatedContent = const [],
    this.continueWatching = const [],
    this.myList = const [],
    this.searchResults = const [],
    this.isLoading = false,
    this.error,
    this.currentPage = 1,
    this.hasMoreData = true,
  });

  ContentState copyWith({
    List<ContentModel>? featuredContent,
    List<ContentModel>? latestMovies,
    List<ContentModel>? latestSeries,
    List<ContentModel>? trendingContent,
    List<ContentModel>? topRatedContent,
    List<ContentModel>? continueWatching,
    List<ContentModel>? myList,
    List<ContentModel>? searchResults,
    bool? isLoading,
    String? error,
    int? currentPage,
    bool? hasMoreData,
  }) {
    return ContentState(
      featuredContent: featuredContent ?? this.featuredContent,
      latestMovies: latestMovies ?? this.latestMovies,
      latestSeries: latestSeries ?? this.latestSeries,
      trendingContent: trendingContent ?? this.trendingContent,
      topRatedContent: topRatedContent ?? this.topRatedContent,
      continueWatching: continueWatching ?? this.continueWatching,
      myList: myList ?? this.myList,
      searchResults: searchResults ?? this.searchResults,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      currentPage: currentPage ?? this.currentPage,
      hasMoreData: hasMoreData ?? this.hasMoreData,
    );
  }
}

/// 🎯 مدير المحتوى
class ContentNotifier extends StateNotifier<ContentState> {
  final ApiService _apiService;

  ContentNotifier(this._apiService) : super(const ContentState());

  /// تحميل المحتوى المميز
  Future<void> loadFeaturedContent() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final response = await _apiService.get('/content/featured');
      
      if (response.data['success']) {
        final List<dynamic> data = response.data['data'];
        final featuredContent = data
            .map((json) => ContentModel.fromJson(json))
            .toList();
        
        state = state.copyWith(
          featuredContent: featuredContent,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          error: response.data['error'] ?? 'فشل في تحميل المحتوى المميز',
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: 'خطأ في الاتصال: $e',
        isLoading: false,
      );
    }
  }

  /// تحميل أحدث الأفلام
  Future<void> loadLatestMovies({int limit = 20}) async {
    try {
      final response = await _apiService.get('/content/latest', queryParameters: {
        'type': 'movie',
        'limit': limit,
      });
      
      if (response.data['success']) {
        final List<dynamic> data = response.data['data'];
        final latestMovies = data
            .map((json) => ContentModel.fromJson(json))
            .toList();
        
        state = state.copyWith(latestMovies: latestMovies);
      }
    } catch (e) {
      state = state.copyWith(error: 'فشل في تحميل أحدث الأفلام');
    }
  }

  /// تحميل أحدث المسلسلات
  Future<void> loadLatestSeries({int limit = 20}) async {
    try {
      final response = await _apiService.get('/content/latest', queryParameters: {
        'type': 'series',
        'limit': limit,
      });
      
      if (response.data['success']) {
        final List<dynamic> data = response.data['data'];
        final latestSeries = data
            .map((json) => ContentModel.fromJson(json))
            .toList();
        
        state = state.copyWith(latestSeries: latestSeries);
      }
    } catch (e) {
      state = state.copyWith(error: 'فشل في تحميل أحدث المسلسلات');
    }
  }

  /// تحميل المحتوى الرائج
  Future<void> loadTrendingContent({int limit = 20}) async {
    try {
      final response = await _apiService.get('/content/trending', queryParameters: {
        'limit': limit,
      });
      
      if (response.data['success']) {
        final List<dynamic> data = response.data['data'];
        final trendingContent = data
            .map((json) => ContentModel.fromJson(json))
            .toList();
        
        state = state.copyWith(trendingContent: trendingContent);
      }
    } catch (e) {
      state = state.copyWith(error: 'فشل في تحميل المحتوى الرائج');
    }
  }

  /// تحميل المحتوى الأعلى تقييماً
  Future<void> loadTopRatedContent({int limit = 20}) async {
    try {
      final response = await _apiService.get('/content/top-rated', queryParameters: {
        'limit': limit,
      });
      
      if (response.data['success']) {
        final List<dynamic> data = response.data['data'];
        final topRatedContent = data
            .map((json) => ContentModel.fromJson(json))
            .toList();
        
        state = state.copyWith(topRatedContent: topRatedContent);
      }
    } catch (e) {
      state = state.copyWith(error: 'فشل في تحميل المحتوى الأعلى تقييماً');
    }
  }

  /// تحميل قائمة متابعة المشاهدة
  Future<void> loadContinueWatching() async {
    try {
      final response = await _apiService.get('/user/continue-watching');
      
      if (response.data['success']) {
        final List<dynamic> data = response.data['data'];
        final continueWatching = data
            .map((json) => ContentModel.fromJson(json))
            .toList();
        
        state = state.copyWith(continueWatching: continueWatching);
      }
    } catch (e) {
      state = state.copyWith(error: 'فشل في تحميل قائمة متابعة المشاهدة');
    }
  }

  /// تحميل قائمتي
  Future<void> loadMyList() async {
    try {
      final response = await _apiService.get('/user/my-list');
      
      if (response.data['success']) {
        final List<dynamic> data = response.data['data'];
        final myList = data
            .map((json) => ContentModel.fromJson(json))
            .toList();
        
        state = state.copyWith(myList: myList);
      }
    } catch (e) {
      state = state.copyWith(error: 'فشل في تحميل قائمتي');
    }
  }

  /// البحث في المحتوى
  Future<void> searchContent(String query, {
    String? type,
    String? genre,
    int? year,
    double? rating,
    String sortBy = 'relevance',
    String sortOrder = 'desc',
    int page = 1,
    int limit = 20,
  }) async {
    try {
      if (page == 1) {
        state = state.copyWith(
          isLoading: true,
          error: null,
          searchResults: [],
          currentPage: 1,
          hasMoreData: true,
        );
      }

      final response = await _apiService.get('/content/search', queryParameters: {
        'q': query,
        if (type != null) 'type': type,
        if (genre != null) 'genre': genre,
        if (year != null) 'year': year,
        if (rating != null) 'rating': rating,
        'sort_by': sortBy,
        'sort_order': sortOrder,
        'page': page,
        'limit': limit,
      });
      
      if (response.data['success']) {
        final List<dynamic> data = response.data['data'];
        final searchResults = data
            .map((json) => ContentModel.fromJson(json))
            .toList();
        
        final pagination = response.data['pagination'];
        final hasMoreData = pagination['has_next_page'] ?? false;
        
        state = state.copyWith(
          searchResults: page == 1 
              ? searchResults 
              : [...state.searchResults, ...searchResults],
          isLoading: false,
          currentPage: page,
          hasMoreData: hasMoreData,
        );
      } else {
        state = state.copyWith(
          error: response.data['error'] ?? 'فشل في البحث',
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: 'خطأ في البحث: $e',
        isLoading: false,
      );
    }
  }

  /// تحميل المزيد من نتائج البحث
  Future<void> loadMoreSearchResults(String query, {
    String? type,
    String? genre,
    int? year,
    double? rating,
    String sortBy = 'relevance',
    String sortOrder = 'desc',
  }) async {
    if (!state.hasMoreData || state.isLoading) return;
    
    await searchContent(
      query,
      type: type,
      genre: genre,
      year: year,
      rating: rating,
      sortBy: sortBy,
      sortOrder: sortOrder,
      page: state.currentPage + 1,
    );
  }

  /// الحصول على تفاصيل المحتوى
  Future<ContentModel?> getContentDetails(int contentId) async {
    try {
      final response = await _apiService.get('/content/$contentId');
      
      if (response.data['success']) {
        return ContentModel.fromJson(response.data['data']);
      } else {
        state = state.copyWith(
          error: response.data['error'] ?? 'فشل في تحميل تفاصيل المحتوى',
        );
        return null;
      }
    } catch (e) {
      state = state.copyWith(error: 'خطأ في تحميل تفاصيل المحتوى: $e');
      return null;
    }
  }

  /// إضافة/إزالة من المفضلة
  Future<bool> toggleFavorite(int contentId) async {
    try {
      final response = await _apiService.post('/favorites/toggle', data: {
        'content_id': contentId,
      });
      
      if (response.data['success']) {
        // تحديث حالة المحتوى في القوائم المختلفة
        _updateContentInLists(contentId, (content) {
          return content.copyWith(isFavorite: response.data['is_favorite']);
        });
        
        return response.data['is_favorite'];
      } else {
        state = state.copyWith(
          error: response.data['error'] ?? 'فشل في تحديث المفضلة',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: 'خطأ في تحديث المفضلة: $e');
      return false;
    }
  }

  /// إضافة/إزالة من قائمة المشاهدة
  Future<bool> toggleWatchlist(int contentId) async {
    try {
      final response = await _apiService.post('/watchlist/toggle', data: {
        'content_id': contentId,
      });
      
      if (response.data['success']) {
        // تحديث حالة المحتوى في القوائم المختلفة
        _updateContentInLists(contentId, (content) {
          return content.copyWith(inWatchlist: response.data['in_watchlist']);
        });
        
        return response.data['in_watchlist'];
      } else {
        state = state.copyWith(
          error: response.data['error'] ?? 'فشل في تحديث قائمة المشاهدة',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: 'خطأ في تحديث قائمة المشاهدة: $e');
      return false;
    }
  }

  /// تقييم المحتوى
  Future<bool> rateContent(int contentId, double rating, {String? review}) async {
    try {
      final response = await _apiService.post('/content/rate', data: {
        'content_id': contentId,
        'rating': rating,
        if (review != null) 'review': review,
      });
      
      if (response.data['success']) {
        // تحديث التقييم في القوائم المختلفة
        _updateContentInLists(contentId, (content) {
          return content.copyWith(userRating: rating);
        });
        
        return true;
      } else {
        state = state.copyWith(
          error: response.data['error'] ?? 'فشل في حفظ التقييم',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: 'خطأ في حفظ التقييم: $e');
      return false;
    }
  }

  /// تحديث تقدم المشاهدة
  Future<void> updateWatchProgress(int contentId, int watchTime, int totalDuration) async {
    try {
      await _apiService.post('/content/progress', data: {
        'content_id': contentId,
        'watch_time': watchTime,
        'total_duration': totalDuration,
      });
      
      // تحديث التقدم في القوائم المختلفة
      final progressPercentage = (watchTime / totalDuration * 100).clamp(0, 100);
      _updateContentInLists(contentId, (content) {
        return content.copyWith(watchProgress: progressPercentage);
      });
    } catch (e) {
      // تجاهل أخطاء تحديث التقدم
    }
  }

  /// تحديث المحتوى في جميع القوائم
  void _updateContentInLists(int contentId, ContentModel Function(ContentModel) updater) {
    state = state.copyWith(
      featuredContent: _updateContentInList(state.featuredContent, contentId, updater),
      latestMovies: _updateContentInList(state.latestMovies, contentId, updater),
      latestSeries: _updateContentInList(state.latestSeries, contentId, updater),
      trendingContent: _updateContentInList(state.trendingContent, contentId, updater),
      topRatedContent: _updateContentInList(state.topRatedContent, contentId, updater),
      continueWatching: _updateContentInList(state.continueWatching, contentId, updater),
      myList: _updateContentInList(state.myList, contentId, updater),
      searchResults: _updateContentInList(state.searchResults, contentId, updater),
    );
  }

  /// تحديث المحتوى في قائمة محددة
  List<ContentModel> _updateContentInList(
    List<ContentModel> list,
    int contentId,
    ContentModel Function(ContentModel) updater,
  ) {
    return list.map((content) {
      if (content.id == contentId) {
        return updater(content);
      }
      return content;
    }).toList();
  }

  /// مسح الأخطاء
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// مسح نتائج البحث
  void clearSearchResults() {
    state = state.copyWith(
      searchResults: [],
      currentPage: 1,
      hasMoreData: true,
    );
  }

  /// إعادة تحميل جميع البيانات
  Future<void> refreshAll() async {
    await Future.wait([
      loadFeaturedContent(),
      loadLatestMovies(),
      loadLatestSeries(),
      loadTrendingContent(),
      loadTopRatedContent(),
      loadContinueWatching(),
      loadMyList(),
    ]);
  }
}

/// 🔍 مزود البحث المتقدم
final searchFiltersProvider = StateProvider<SearchFilters>((ref) {
  return const SearchFilters();
});

/// 🎛️ فلاتر البحث
class SearchFilters {
  final String? type;
  final String? genre;
  final int? year;
  final double? rating;
  final String sortBy;
  final String sortOrder;

  const SearchFilters({
    this.type,
    this.genre,
    this.year,
    this.rating,
    this.sortBy = 'relevance',
    this.sortOrder = 'desc',
  });

  SearchFilters copyWith({
    String? type,
    String? genre,
    int? year,
    double? rating,
    String? sortBy,
    String? sortOrder,
  }) {
    return SearchFilters(
      type: type ?? this.type,
      genre: genre ?? this.genre,
      year: year ?? this.year,
      rating: rating ?? this.rating,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (type != null) 'type': type,
      if (genre != null) 'genre': genre,
      if (year != null) 'year': year,
      if (rating != null) 'rating': rating,
      'sort_by': sortBy,
      'sort_order': sortOrder,
    };
  }
}
