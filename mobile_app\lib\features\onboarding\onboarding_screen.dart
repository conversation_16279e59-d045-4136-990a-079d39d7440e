import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../core/constants/app_constants.dart';
import '../../core/services/storage_service.dart';

/// 👋 شاشة التعريف بالتطبيق
class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({super.key});

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      title: 'مرحباً بك في منصة البث الشاملة',
      description: 'استمتع بمشاهدة آلاف الأفلام والمسلسلات بجودة عالية',
      icon: Icons.movie_outlined,
      color: AppConstants.primaryColor,
    ),
    OnboardingPage(
      title: 'شاهد في أي مكان وزمان',
      description: 'حمل المحتوى المفضل لديك وشاهده بدون إنترنت',
      icon: Icons.download_outlined,
      color: AppConstants.infoColor,
    ),
    OnboardingPage(
      title: 'جودة عالية ومتعددة',
      description: 'استمتع بالمشاهدة بجودة 4K و HD مع ترجمات متعددة',
      icon: Icons.hd_outlined,
      color: AppConstants.successColor,
    ),
    OnboardingPage(
      title: 'مشاهدة جماعية مع الأصدقاء',
      description: 'شاهد مع أصدقائك في نفس الوقت واستمتعوا معاً',
      icon: Icons.group_outlined,
      color: AppConstants.warningColor,
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: AppConstants.animationDuration,
        curve: Curves.easeInOut,
      );
    } else {
      _finishOnboarding();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: AppConstants.animationDuration,
        curve: Curves.easeInOut,
      );
    }
  }

  void _skipOnboarding() {
    _finishOnboarding();
  }

  void _finishOnboarding() async {
    // تعيين أن التطبيق لم يعد في التشغيل الأول
    await StorageService.instance.setFirstLaunch(false);
    
    // الانتقال لتسجيل الدخول
    if (mounted) {
      context.go('/auth/login');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.darkColor,
      body: SafeArea(
        child: Column(
          children: [
            // شريط التخطي
            _buildTopBar(),
            
            // محتوى الصفحات
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: _onPageChanged,
                itemCount: _pages.length,
                itemBuilder: (context, index) {
                  return _buildPage(_pages[index]);
                },
              ),
            ),
            
            // مؤشر الصفحات والأزرار
            _buildBottomSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildTopBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // زر الرجوع
          if (_currentPage > 0)
            IconButton(
              onPressed: _previousPage,
              icon: const Icon(
                Icons.arrow_back_ios,
                color: AppConstants.textMutedColor,
              ),
            )
          else
            const SizedBox(width: 48),
          
          // زر التخطي
          TextButton(
            onPressed: _skipOnboarding,
            child: const Text(
              'تخطي',
              style: TextStyle(
                color: AppConstants.textMutedColor,
                fontSize: AppConstants.fontSizeMedium,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPage(OnboardingPage page) {
    return Padding(
      padding: const EdgeInsets.all(32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // الأيقونة المتحركة
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 800),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: page.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(60),
                    border: Border.all(
                      color: page.color.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    page.icon,
                    size: 60,
                    color: page.color,
                  ),
                ),
              );
            },
          ),
          
          const SizedBox(height: 48),
          
          // العنوان
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 600),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Opacity(
                opacity: value,
                child: Transform.translate(
                  offset: Offset(0, 20 * (1 - value)),
                  child: Text(
                    page.title,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: AppConstants.textColor,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              );
            },
          ),
          
          const SizedBox(height: 24),
          
          // الوصف
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 800),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Opacity(
                opacity: value,
                child: Transform.translate(
                  offset: Offset(0, 20 * (1 - value)),
                  child: Text(
                    page.description,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: AppConstants.textMutedColor,
                      height: 1.6,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBottomSection() {
    return Padding(
      padding: const EdgeInsets.all(32.0),
      child: Column(
        children: [
          // مؤشر الصفحات
          SmoothPageIndicator(
            controller: _pageController,
            count: _pages.length,
            effect: WormEffect(
              dotColor: AppConstants.textMutedColor.withOpacity(0.3),
              activeDotColor: AppConstants.primaryColor,
              dotHeight: 8,
              dotWidth: 8,
              spacing: 16,
            ),
          ),
          
          const SizedBox(height: 32),
          
          // زر المتابعة
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _nextPage,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                _currentPage == _pages.length - 1 ? 'ابدأ الآن' : 'التالي',
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeLarge,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 📄 نموذج صفحة التعريف
class OnboardingPage {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  const OnboardingPage({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}

/// 🎨 ويدجت الخلفية المتحركة
class AnimatedBackground extends StatefulWidget {
  final Widget child;

  const AnimatedBackground({super.key, required this.child});

  @override
  State<AnimatedBackground> createState() => _AnimatedBackgroundState();
}

class _AnimatedBackgroundState extends State<AnimatedBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(_controller);
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // الخلفية المتدرجة
        Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppConstants.darkColor,
                AppConstants.secondaryColor,
              ],
            ),
          ),
        ),
        
        // الدوائر المتحركة
        AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return CustomPaint(
              painter: CirclesPainter(_animation.value),
              size: Size.infinite,
            );
          },
        ),
        
        // المحتوى
        widget.child,
      ],
    );
  }
}

/// 🎨 رسام الدوائر المتحركة
class CirclesPainter extends CustomPainter {
  final double animationValue;

  CirclesPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..color = AppConstants.primaryColor.withOpacity(0.05);

    // رسم دوائر متحركة
    for (int i = 0; i < 5; i++) {
      final radius = 50 + (i * 30) + (animationValue * 20);
      final x = size.width * (0.2 + i * 0.15);
      final y = size.height * (0.3 + (animationValue * 0.4));
      
      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
