<?php
/**
 * 🔐 صفحة تسجيل الدخول
 */
?>

<div class="auth-container min-vh-100 d-flex align-items-center py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="auth-card card bg-secondary border-0 shadow-lg">
                    <div class="card-body p-5">
                        <!-- Logo -->
                        <div class="text-center mb-4">
                            <img src="<?= asset('images/logo.png') ?>" 
                                 alt="<?= DynamicSettings::get('site_name', 'منصة البث الشاملة') ?>" 
                                 height="60" class="mb-3">
                            <h2 class="text-white fw-bold">تسجيل الدخول</h2>
                            <p class="text-muted">مرحباً بك مرة أخرى</p>
                        </div>

                        <!-- Login Form -->
                        <form id="loginForm" novalidate>
                            <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                            
                            <!-- Email Field -->
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-2"></i>البريد الإلكتروني
                                </label>
                                <input type="email" 
                                       class="form-control form-control-lg" 
                                       id="email" 
                                       name="email" 
                                       placeholder="أدخل بريدك الإلكتروني"
                                       required
                                       autocomplete="email">
                            </div>

                            <!-- Password Field -->
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>كلمة المرور
                                </label>
                                <div class="input-group">
                                    <input type="password" 
                                           class="form-control form-control-lg" 
                                           id="password" 
                                           name="password" 
                                           placeholder="أدخل كلمة المرور"
                                           required
                                           autocomplete="current-password">
                                    <button class="btn btn-outline-secondary" 
                                            type="button" 
                                            id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Remember Me & Forgot Password -->
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="remember" 
                                           name="remember">
                                    <label class="form-check-label text-muted" for="remember">
                                        تذكرني
                                    </label>
                                </div>
                                <a href="<?= url('/forgot-password') ?>" 
                                   class="text-primary text-decoration-none">
                                    نسيت كلمة المرور؟
                                </a>
                            </div>

                            <!-- Login Button -->
                            <button type="submit" 
                                    class="btn btn-primary btn-lg w-100 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </form>

                        <!-- Social Login -->
                        <?php if ($googleLoginEnabled || $facebookLoginEnabled): ?>
                            <div class="social-login">
                                <div class="divider text-center mb-3">
                                    <span class="text-muted bg-secondary px-3">أو</span>
                                </div>

                                <div class="d-grid gap-2">
                                    <?php if ($googleLoginEnabled): ?>
                                        <a href="<?= url('/auth/google') ?>" 
                                           class="btn btn-outline-light btn-lg">
                                            <i class="fab fa-google me-2 text-danger"></i>
                                            تسجيل الدخول بجوجل
                                        </a>
                                    <?php endif; ?>

                                    <?php if ($facebookLoginEnabled): ?>
                                        <a href="<?= url('/auth/facebook') ?>" 
                                           class="btn btn-outline-light btn-lg">
                                            <i class="fab fa-facebook-f me-2 text-primary"></i>
                                            تسجيل الدخول بفيسبوك
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Register Link -->
                        <div class="text-center mt-4">
                            <p class="text-muted mb-0">
                                ليس لديك حساب؟ 
                                <a href="<?= url('/register') ?>" 
                                   class="text-primary text-decoration-none fw-bold">
                                    إنشاء حساب جديد
                                </a>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Features -->
                <div class="auth-features mt-4">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="feature-item">
                                <i class="fas fa-film fa-2x text-primary mb-2"></i>
                                <p class="text-muted small mb-0">آلاف الأفلام</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="feature-item">
                                <i class="fas fa-tv fa-2x text-primary mb-2"></i>
                                <p class="text-muted small mb-0">مسلسلات حصرية</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="feature-item">
                                <i class="fas fa-mobile-alt fa-2x text-primary mb-2"></i>
                                <p class="text-muted small mb-0">مشاهدة في أي مكان</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional CSS -->
<style>
.auth-container {
    background: linear-gradient(135deg, var(--dark-color) 0%, var(--secondary-color) 100%);
    position: relative;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('<?= asset('images/auth-bg.jpg') ?>');
    background-size: cover;
    background-position: center;
    opacity: 0.1;
    z-index: -1;
}

.auth-card {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-card .card-body {
    background: rgba(34, 31, 31, 0.9);
    border-radius: 0.5rem;
}

.divider {
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
    z-index: -1;
}

.feature-item {
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
    border-color: var(--primary-color);
}

/* تحسين الأزرار الاجتماعية */
.btn-outline-light {
    border-color: rgba(255, 255, 255, 0.3);
    color: var(--text-color);
}

.btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: var(--text-color);
}

/* تحسين حقول الإدخال */
.form-control {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-color);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-control:focus {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

/* تحسين الروابط */
.text-primary {
    color: var(--primary-color) !important;
}

.text-primary:hover {
    color: #b8070f !important;
}

/* تأثيرات التحميل */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<!-- Additional JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    
    if (togglePassword && passwordInput) {
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const icon = this.querySelector('i');
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        });
    }
    
    // Form submission handling
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Add loading state
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;
            
            // Let the main JavaScript handle the actual submission
            StreamingPlatform.handleLogin(e).finally(() => {
                // Remove loading state
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    }
    
    // Auto-focus on email field
    const emailInput = document.getElementById('email');
    if (emailInput) {
        emailInput.focus();
    }
});
</script>
