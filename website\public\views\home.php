<?php
/**
 * 🏠 صفحة الرئيسية لمنصة البث الشاملة
 */
?>

<!-- Hero Section -->
<section class="hero-section position-relative overflow-hidden">
    <?php if (!empty($featuredContent)): ?>
        <div id="heroCarousel" class="carousel slide" data-bs-ride="carousel">
            <div class="carousel-inner">
                <?php foreach (array_slice($featuredContent, 0, 5) as $index => $content): ?>
                    <div class="carousel-item <?= $index === 0 ? 'active' : '' ?>">
                        <div class="hero-slide position-relative">
                            <img src="<?= media('images/' . ($content['banner'] ?: $content['poster'])) ?>" 
                                 class="d-block w-100" 
                                 alt="<?= htmlspecialchars($content['title']) ?>"
                                 style="height: 70vh; object-fit: cover;">
                            
                            <div class="hero-overlay position-absolute top-0 start-0 w-100 h-100" 
                                 style="background: linear-gradient(45deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.3) 100%);">
                            </div>
                            
                            <div class="hero-content position-absolute top-50 start-0 translate-middle-y text-white p-5">
                                <div class="container">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <span class="badge bg-primary mb-3 fs-6">
                                                <?= $content['type'] === 'movie' ? 'فيلم' : 'مسلسل' ?>
                                            </span>
                                            <h1 class="display-4 fw-bold mb-3"><?= htmlspecialchars($content['title']) ?></h1>
                                            <p class="lead mb-4"><?= truncateText($content['description'], 200) ?></p>
                                            
                                            <div class="hero-meta mb-4">
                                                <?php if ($content['rating']): ?>
                                                    <span class="me-3">
                                                        <i class="fas fa-star text-warning"></i>
                                                        <?= number_format($content['rating'], 1) ?>
                                                    </span>
                                                <?php endif; ?>
                                                
                                                <?php if ($content['release_date']): ?>
                                                    <span class="me-3">
                                                        <i class="fas fa-calendar"></i>
                                                        <?= date('Y', strtotime($content['release_date'])) ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <div class="hero-actions">
                                                <a href="<?= url('/content/' . $content['slug']) ?>" 
                                                   class="btn btn-primary btn-lg me-3">
                                                    <i class="fas fa-play"></i> مشاهدة الآن
                                                </a>
                                                <button class="btn btn-outline-light btn-lg me-3 favorite-btn" 
                                                        data-content-id="<?= $content['id'] ?>">
                                                    <i class="far fa-heart"></i> إضافة للمفضلة
                                                </button>
                                                <button class="btn btn-outline-light btn-lg watchlist-btn" 
                                                        data-content-id="<?= $content['id'] ?>">
                                                    <i class="far fa-bookmark"></i> قائمة المشاهدة
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon"></span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon"></span>
            </button>
            
            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                <?php foreach (array_slice($featuredContent, 0, 5) as $index => $content): ?>
                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="<?= $index ?>" 
                            class="<?= $index === 0 ? 'active' : '' ?>"></button>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
</section>

<!-- Quick Stats -->
<section class="stats-section py-4 bg-secondary">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-4">
                <div class="stat-item">
                    <h3 class="text-primary mb-1"><?= formatNumber($stats['total_content']) ?></h3>
                    <p class="text-muted mb-0">محتوى متاح</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-item">
                    <h3 class="text-primary mb-1"><?= formatNumber($stats['total_users']) ?></h3>
                    <p class="text-muted mb-0">مستخدم مسجل</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-item">
                    <h3 class="text-primary mb-1"><?= formatNumber($stats['total_views']) ?>+</h3>
                    <p class="text-muted mb-0">مشاهدة</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<?php if (!empty($categories)): ?>
<section class="categories-section py-5">
    <div class="container">
        <h2 class="section-title mb-4">
            <i class="fas fa-th-large text-primary me-2"></i>
            تصفح حسب الفئة
        </h2>
        
        <div class="row">
            <?php foreach ($categories as $category): ?>
                <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
                    <a href="<?= url('/category/' . $category['slug']) ?>" class="category-card text-decoration-none">
                        <div class="card bg-secondary border-0 h-100 text-center">
                            <?php if ($category['image']): ?>
                                <img src="<?= media('images/' . $category['image']) ?>" 
                                     class="card-img-top" 
                                     alt="<?= htmlspecialchars($category['name']) ?>"
                                     style="height: 120px; object-fit: cover;">
                            <?php else: ?>
                                <div class="card-img-top d-flex align-items-center justify-content-center bg-dark" 
                                     style="height: 120px;">
                                    <i class="fas fa-folder-open fa-3x text-primary"></i>
                                </div>
                            <?php endif; ?>
                            
                            <div class="card-body">
                                <h6 class="card-title text-white mb-0"><?= htmlspecialchars($category['name']) ?></h6>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Trending Content -->
<?php if (!empty($trendingContent)): ?>
<section class="trending-section py-5 bg-dark">
    <div class="container">
        <h2 class="section-title mb-4">
            <i class="fas fa-fire text-danger me-2"></i>
            الأكثر مشاهدة
        </h2>
        
        <div class="row">
            <?php foreach ($trendingContent as $content): ?>
                <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
                    <div class="content-card" data-slug="<?= $content['slug'] ?>">
                        <div class="position-relative">
                            <img src="<?= media('images/' . $content['poster']) ?>" 
                                 class="card-img-top" 
                                 alt="<?= htmlspecialchars($content['title']) ?>"
                                 loading="lazy">
                            
                            <div class="content-type-badge">
                                <?= $content['type'] === 'movie' ? 'فيلم' : 'مسلسل' ?>
                            </div>
                            
                            <div class="content-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center opacity-0">
                                <button class="btn btn-primary btn-lg rounded-circle">
                                    <i class="fas fa-play"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="content-card-body">
                            <h6 class="content-card-title"><?= truncateText($content['title'], 30) ?></h6>
                            
                            <div class="content-card-meta">
                                <?php if ($content['rating']): ?>
                                    <div class="content-rating">
                                        <i class="fas fa-star"></i>
                                        <span><?= number_format($content['rating'], 1) ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="content-views">
                                    <i class="fas fa-eye"></i>
                                    <span><?= formatNumber($content['views_count']) ?></span>
                                </div>
                            </div>
                            
                            <div class="content-actions mt-2">
                                <button class="btn btn-sm btn-outline-light favorite-btn" 
                                        data-content-id="<?= $content['id'] ?>">
                                    <i class="far fa-heart"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-light watchlist-btn" 
                                        data-content-id="<?= $content['id'] ?>">
                                    <i class="far fa-bookmark"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?= url('/trending') ?>" class="btn btn-outline-primary">
                عرض المزيد <i class="fas fa-arrow-left ms-2"></i>
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Latest Content -->
<?php if (!empty($latestContent)): ?>
<section class="latest-section py-5">
    <div class="container">
        <h2 class="section-title mb-4">
            <i class="fas fa-clock text-info me-2"></i>
            أحدث الإضافات
        </h2>
        
        <div class="row">
            <?php foreach ($latestContent as $content): ?>
                <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
                    <div class="content-card" data-slug="<?= $content['slug'] ?>">
                        <div class="position-relative">
                            <img src="<?= media('images/' . $content['poster']) ?>" 
                                 class="card-img-top" 
                                 alt="<?= htmlspecialchars($content['title']) ?>"
                                 loading="lazy">
                            
                            <div class="content-type-badge">
                                <?= $content['type'] === 'movie' ? 'فيلم' : 'مسلسل' ?>
                            </div>
                            
                            <div class="content-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center opacity-0">
                                <button class="btn btn-primary btn-lg rounded-circle">
                                    <i class="fas fa-play"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="content-card-body">
                            <h6 class="content-card-title"><?= truncateText($content['title'], 30) ?></h6>
                            <p class="content-card-text"><?= truncateText($content['description'], 60) ?></p>
                            
                            <div class="content-card-meta">
                                <?php if ($content['rating']): ?>
                                    <div class="content-rating">
                                        <i class="fas fa-star"></i>
                                        <span><?= number_format($content['rating'], 1) ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($content['release_date']): ?>
                                    <div class="content-date">
                                        <i class="fas fa-calendar"></i>
                                        <span><?= date('Y', strtotime($content['release_date'])) ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="content-actions mt-2">
                                <button class="btn btn-sm btn-outline-light favorite-btn" 
                                        data-content-id="<?= $content['id'] ?>">
                                    <i class="far fa-heart"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-light watchlist-btn" 
                                        data-content-id="<?= $content['id'] ?>">
                                    <i class="far fa-bookmark"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?= url('/latest') ?>" class="btn btn-outline-primary">
                عرض المزيد <i class="fas fa-arrow-left ms-2"></i>
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Call to Action -->
<?php if (!isLoggedIn()): ?>
<section class="cta-section py-5 bg-primary">
    <div class="container text-center">
        <h2 class="text-white mb-3">ابدأ مشاهدة المحتوى المفضل لديك الآن</h2>
        <p class="text-white mb-4 lead">انضم إلى آلاف المستخدمين واستمتع بأفضل الأفلام والمسلسلات</p>
        
        <div class="cta-actions">
            <a href="<?= url('/register') ?>" class="btn btn-light btn-lg me-3">
                <i class="fas fa-user-plus"></i> إنشاء حساب مجاني
            </a>
            <a href="<?= url('/subscription') ?>" class="btn btn-outline-light btn-lg">
                <i class="fas fa-crown"></i> عرض الباقات
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Additional CSS for this page -->
<style>
.hero-slide img {
    filter: brightness(0.7);
}

.content-card {
    transition: all 0.3s ease;
}

.content-card:hover .content-overlay {
    opacity: 1 !important;
    background: rgba(0, 0, 0, 0.8);
}

.category-card:hover .card {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.section-title {
    font-weight: 600;
    position: relative;
    padding-bottom: 10px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background: var(--primary-color);
}

.stats-section .stat-item h3 {
    font-size: 2.5rem;
    font-weight: 700;
}

.cta-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #b8070f 100%);
}
</style>
