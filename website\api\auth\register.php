<?php
/**
 * 📝 API التسجيل
 * يتعامل مع طلبات إنشاء حساب جديد عبر AJAX
 */

// تعريف الثابت للسماح بالوصول
define('STREAMING_PLATFORM', true);

// تحميل الملفات المطلوبة
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// تعيين رأس JSON
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// التعامل مع طلبات OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'طريقة الطلب غير مدعومة'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من وجود البيانات المطلوبة
    $requiredFields = ['first_name', 'last_name', 'email', 'password', 'confirm_password'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty(trim($input[$field]))) {
            throw new Exception("الحقل {$field} مطلوب");
        }
    }
    
    // تنظيف البيانات
    $userData = [
        'first_name' => sanitizeInput($input['first_name']),
        'last_name' => sanitizeInput($input['last_name']),
        'email' => sanitizeInput($input['email']),
        'password' => $input['password'],
        'confirm_password' => $input['confirm_password'],
        'language' => sanitizeInput($input['language'] ?? DEFAULT_LANGUAGE),
        'terms_accepted' => isset($input['terms_accepted']) ? (bool)$input['terms_accepted'] : false
    ];
    
    // التحقق من الشروط والأحكام
    if (!$userData['terms_accepted']) {
        throw new Exception('يجب الموافقة على الشروط والأحكام');
    }
    
    // التحقق من صحة البريد الإلكتروني
    if (!isValidEmail($userData['email'])) {
        throw new Exception('البريد الإلكتروني غير صحيح');
    }
    
    // التحقق من طول الاسم
    if (strlen($userData['first_name']) < 2 || strlen($userData['last_name']) < 2) {
        throw new Exception('الاسم الأول والأخير يجب أن يكونا حرفين على الأقل');
    }
    
    // التحقق من قوة كلمة المرور
    if (!isStrongPassword($userData['password'])) {
        throw new Exception('كلمة المرور ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل مع أحرف كبيرة وصغيرة وأرقام');
    }
    
    // التحقق من تطابق كلمات المرور
    if ($userData['password'] !== $userData['confirm_password']) {
        throw new Exception('كلمات المرور غير متطابقة');
    }
    
    // التحقق من CSRF Token إذا كان متوفراً
    if (isset($input['csrf_token'])) {
        if (!verifyCSRFToken($input['csrf_token'])) {
            throw new Exception('رمز الأمان غير صحيح');
        }
    }
    
    // التحقق من عدم وجود البريد الإلكتروني مسبقاً
    global $db;
    $existingUser = $db->selectOne(
        "SELECT id FROM " . DB_PREFIX . "users WHERE email = ?",
        [$userData['email']]
    );
    
    if ($existingUser) {
        throw new Exception('البريد الإلكتروني مستخدم بالفعل');
    }
    
    // محاولة التسجيل
    $auth = new Auth();
    $result = $auth->register($userData);
    
    if ($result['success']) {
        // تسجيل نشاط التسجيل
        logUserActivity('register_api', [
            'user_id' => $result['user_id'],
            'email' => $userData['email'],
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        
        // تتبع حدث التسجيل
        trackEvent('user_register', [
            'user_id' => $result['user_id'],
            'registration_method' => 'api',
            'language' => $userData['language']
        ]);
        
        // إعداد الاستجابة
        $response = [
            'success' => true,
            'message' => 'تم إنشاء الحساب بنجاح',
            'user_id' => $result['user_id']
        ];
        
        // إضافة معلومات التحقق إذا كان مطلوباً
        if ($result['requires_verification']) {
            $response['requires_verification'] = true;
            $response['verification_message'] = 'تم إرسال رابط التحقق إلى بريدك الإلكتروني';
            $response['redirect_url'] = '/auth/verify-email';
        } else {
            $response['redirect_url'] = '/auth/login';
        }
        
        // إرجاع الاستجابة الناجحة
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        
    } else {
        // تسجيل محاولة تسجيل فاشلة
        logUserActivity('register_failed_api', [
            'email' => $userData['email'],
            'errors' => $result['errors'] ?? $result['error'],
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        
        // تتبع حدث فشل التسجيل
        trackEvent('register_failed', [
            'email' => $userData['email'],
            'errors' => $result['errors'] ?? $result['error'],
            'registration_method' => 'api'
        ]);
        
        // إرجاع رسالة الخطأ
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $result['error'] ?? 'حدث خطأ أثناء إنشاء الحساب',
            'errors' => $result['errors'] ?? []
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    // تسجيل الخطأ
    logError('Register API Error: ' . $e->getMessage(), [
        'file' => __FILE__,
        'line' => $e->getLine(),
        'input' => $input ?? null,
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);
    
    // إرجاع رسالة خطأ عامة
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
