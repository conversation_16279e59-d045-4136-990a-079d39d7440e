@echo off
echo ========================================
echo    🚀 بدء تشغيل منصة البث
echo ========================================
echo.

:: التحقق من وجود PHP
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت PHP أولاً من: https://www.php.net/downloads
    pause
    exit /b 1
)

:: التحقق من وجود Composer
composer --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Composer غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Composer أولاً من: https://getcomposer.org/download
    pause
    exit /b 1
)

:: التحقق من وجود MySQL
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت MySQL أولاً أو تشغيل XAMPP/WAMP
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.

:: إنشاء قاعدة البيانات
echo 📊 إعداد قاعدة البيانات...
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS streaming_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if %errorlevel% neq 0 (
    echo ❌ فشل في إنشاء قاعدة البيانات
    pause
    exit /b 1
)

:: تشغيل ملف SQL
echo 📋 تشغيل ملف إعداد قاعدة البيانات...
mysql -u root -p streaming_platform < setup.sql
if %errorlevel% neq 0 (
    echo ❌ فشل في تشغيل ملف SQL
    pause
    exit /b 1
)

:: تثبيت تبعيات PHP
echo 📦 تثبيت تبعيات PHP...
cd website
composer install --no-dev --optimize-autoloader
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت تبعيات PHP
    pause
    exit /b 1
)
cd ..

:: إنشاء مجلدات مطلوبة
echo 📁 إنشاء المجلدات المطلوبة...
if not exist "website\public\uploads" mkdir "website\public\uploads"
if not exist "website\public\uploads\posters" mkdir "website\public\uploads\posters"
if not exist "website\public\uploads\banners" mkdir "website\public\uploads\banners"
if not exist "website\public\uploads\videos" mkdir "website\public\uploads\videos"
if not exist "website\public\uploads\avatars" mkdir "website\public\uploads\avatars"
if not exist "website\logs" mkdir "website\logs"
if not exist "website\cache" mkdir "website\cache"

:: نسخ ملفات الأصول الافتراضية
echo 🎨 نسخ الأصول الافتراضية...
if not exist "website\public\assets\images\default-poster.jpg" (
    echo Creating default poster...
    echo. > "website\public\assets\images\default-poster.jpg"
)

:: تعيين الصلاحيات (في Windows لا نحتاج chmod)
echo 🔐 تعيين الصلاحيات...
echo Permissions set for Windows

echo.
echo ========================================
echo ✅ تم إعداد المنصة بنجاح!
echo ========================================
echo.
echo 🌐 بيانات تسجيل الدخول الافتراضية:
echo    البريد الإلكتروني: <EMAIL>
echo    كلمة المرور: password
echo.
echo 🚀 لبدء تشغيل الخادم:
echo    cd website
echo    php -S localhost:8000 -t public
echo.
echo 📱 لتشغيل التطبيق الجوال:
echo    cd mobile_app
echo    flutter pub get
echo    flutter run
echo.

:: سؤال المستخدم إذا كان يريد بدء الخادم الآن
set /p start_server="هل تريد بدء تشغيل خادم الويب الآن؟ (y/n): "
if /i "%start_server%"=="y" (
    echo.
    echo 🌐 بدء تشغيل خادم الويب على http://localhost:8000
    echo اضغط Ctrl+C لإيقاف الخادم
    echo.
    cd website
    php -S localhost:8000 -t public
) else (
    echo.
    echo 👍 يمكنك بدء تشغيل الخادم لاحقاً باستخدام:
    echo    cd website
    echo    php -S localhost:8000 -t public
)

pause
