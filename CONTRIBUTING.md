# 🤝 دليل المساهمة في منصة البث العربية

نرحب بمساهماتكم في تطوير منصة البث العربية! هذا الدليل سيساعدكم على فهم كيفية المساهمة بفعالية.

## 📋 جدول المحتويات

- [كيفية المساهمة](#كيفية-المساهمة)
- [الإبلاغ عن الأخطاء](#الإبلاغ-عن-الأخطاء)
- [اقتراح الميزات](#اقتراح-الميزات)
- [إرشادات الكود](#إرشادات-الكود)
- [عملية المراجعة](#عملية-المراجعة)
- [إعداد بيئة التطوير](#إعداد-بيئة-التطوير)

## 🚀 كيفية المساهمة

### 1. Fork المشروع
```bash
# انسخ المشروع إلى حسابك
git clone https://github.com/your-username/streaming-platform.git
cd streaming-platform
```

### 2. إنشاء فرع جديد
```bash
# أنشئ فرع للميزة الجديدة
git checkout -b feature/new-feature-name

# أو للإصلاح
git checkout -b fix/bug-description
```

### 3. تطوير التغييرات
- اكتب كود نظيف ومفهوم
- أضف تعليقات باللغة العربية
- اتبع معايير الكود المحددة
- اكتب اختبارات للميزات الجديدة

### 4. اختبار التغييرات
```bash
# اختبار PHP
cd website
composer test

# اختبار Flutter
cd mobile_app
flutter test

# اختبار التكامل
./deploy.sh test
```

### 5. إرسال Pull Request
```bash
git add .
git commit -m "feat: إضافة ميزة جديدة"
git push origin feature/new-feature-name
```

## 🐛 الإبلاغ عن الأخطاء

عند العثور على خطأ، يرجى:

### 1. التحقق من Issues الموجودة
تأكد من عدم وجود تقرير مماثل مسبقاً.

### 2. إنشاء Issue جديد
استخدم القالب التالي:

```markdown
## وصف الخطأ
وصف واضح ومختصر للخطأ.

## خطوات إعادة الإنتاج
1. اذهب إلى '...'
2. انقر على '...'
3. مرر إلى '...'
4. شاهد الخطأ

## السلوك المتوقع
وصف واضح لما كان متوقعاً أن يحدث.

## لقطات الشاشة
إذا كان ذلك مناسباً، أضف لقطات شاشة لتوضيح المشكلة.

## معلومات البيئة
- نظام التشغيل: [مثل iOS, Android, Windows, macOS]
- المتصفح: [مثل Chrome, Safari, Firefox]
- الإصدار: [مثل 22]

## معلومات إضافية
أي معلومات أخرى حول المشكلة.
```

## 💡 اقتراح الميزات

لاقتراح ميزة جديدة:

### 1. تحقق من الخطة المستقبلية
راجع Issues والمشاريع المخططة.

### 2. أنشئ Feature Request
```markdown
## ملخص الميزة
وصف مختصر للميزة المقترحة.

## المشكلة المحلولة
وصف المشكلة التي تحلها هذه الميزة.

## الحل المقترح
وصف واضح لما تريد أن يحدث.

## البدائل المدروسة
وصف أي حلول أو ميزات بديلة فكرت فيها.

## معلومات إضافية
أي معلومات أخرى أو لقطات شاشة حول طلب الميزة.
```

## 📝 إرشادات الكود

### PHP
```php
<?php
/**
 * وصف الكلاس أو الدالة
 */
class ExampleClass 
{
    /**
     * وصف الدالة
     * 
     * @param string $parameter وصف المعامل
     * @return bool وصف القيمة المرجعة
     */
    public function exampleMethod(string $parameter): bool
    {
        // تعليق باللغة العربية
        return true;
    }
}
```

### JavaScript
```javascript
/**
 * وصف الدالة
 * @param {string} parameter - وصف المعامل
 * @returns {boolean} وصف القيمة المرجعة
 */
function exampleFunction(parameter) {
    // تعليق باللغة العربية
    return true;
}
```

### Flutter/Dart
```dart
/// وصف الكلاس
class ExampleWidget extends StatelessWidget {
  /// وصف المعامل
  final String title;
  
  /// منشئ الكلاس
  const ExampleWidget({Key? key, required this.title}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // تعليق باللغة العربية
    return Container();
  }
}
```

### معايير الكود

#### PHP
- اتبع PSR-12 coding standard
- استخدم type hints
- اكتب docblocks شاملة
- استخدم meaningful names

#### JavaScript
- استخدم ES6+ features
- اتبع ESLint rules
- استخدم const/let بدلاً من var
- اكتب JSDoc comments

#### Flutter
- اتبع Dart style guide
- استخدم meaningful widget names
- اكتب documentation comments
- استخدم const constructors

### Git Commit Messages

استخدم التنسيق التالي:
```
type(scope): وصف مختصر

وصف مفصل إذا لزم الأمر

Closes #123
```

الأنواع المتاحة:
- `feat`: ميزة جديدة
- `fix`: إصلاح خطأ
- `docs`: تحديث الوثائق
- `style`: تغييرات التنسيق
- `refactor`: إعادة هيكلة الكود
- `test`: إضافة أو تحديث الاختبارات
- `chore`: مهام الصيانة

## 🔍 عملية المراجعة

### 1. المراجعة التلقائية
- تشغيل CI/CD pipeline
- فحص جودة الكود
- تشغيل الاختبارات
- فحص الأمان

### 2. المراجعة البشرية
- مراجعة الكود من قبل المطورين
- التحقق من اتباع المعايير
- اختبار الوظائف الجديدة
- مراجعة الوثائق

### 3. الموافقة والدمج
- الحصول على موافقة مراجعين
- حل التعليقات والملاحظات
- دمج التغييرات في الفرع الرئيسي

## 🛠️ إعداد بيئة التطوير

### 1. المتطلبات
- PHP 8.1+
- Node.js 18+
- Flutter 3.13+
- Docker & Docker Compose
- Git

### 2. الإعداد
```bash
# استنساخ المشروع
git clone https://github.com/streaming-platform/streaming-platform.git
cd streaming-platform

# إعداد PHP
cd website
composer install
cp includes/config.example.php includes/config.php

# إعداد Flutter
cd ../mobile_app
flutter pub get

# تشغيل بيئة التطوير
cd ..
./deploy.sh
```

### 3. أدوات التطوير المفيدة
- **IDE**: VS Code, PHPStorm, Android Studio
- **Extensions**: PHP Intelephense, Flutter, GitLens
- **Tools**: Postman (API testing), Redis Commander

## 🎯 أولويات المساهمة

### عالية الأولوية
- إصلاح الأخطاء الأمنية
- تحسين الأداء
- إصلاح الأخطاء الحرجة
- تحسين تجربة المستخدم

### متوسطة الأولوية
- ميزات جديدة
- تحسين الكود
- تحديث الوثائق
- إضافة اختبارات

### منخفضة الأولوية
- تحسينات التصميم
- ميزات تجريبية
- تحسينات الأدوات

## 📞 التواصل

- **GitHub Issues**: للأخطاء والميزات
- **GitHub Discussions**: للنقاشات العامة
- **Email**: للاستفسارات الخاصة

## 🏆 الاعتراف بالمساهمين

نقدر جميع المساهمات ونعترف بها في:
- ملف CONTRIBUTORS.md
- صفحة الشكر في الموقع
- ملاحظات الإصدار

## 📜 ميثاق السلوك

نتوقع من جميع المساهمين:
- الاحترام المتبادل
- التواصل البناء
- التركيز على التحسين
- مساعدة الآخرين

## 🎉 شكراً لمساهمتكم!

كل مساهمة، مهما كانت صغيرة، تساعد في تحسين منصة البث العربية. شكراً لكم على وقتكم وجهدكم! 

---

**تم إعداد هذا الدليل بـ ❤️ لمجتمع المطورين العرب**
