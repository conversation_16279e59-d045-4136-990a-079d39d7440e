# 🚀 دليل التثبيت والإعداد - منصة البث الشاملة

## 📋 المتطلبات الأساسية

### للخادم (الموقع الإلكتروني)
- **XAMPP** أو **WAMP** أو أي خادم ويب يدعم:
  - PHP 8.0 أو أحدث
  - MySQL 8.0 أو أحدث
  - Apache 2.4 أو أحدث
- **Composer** لإدارة تبعيات PHP
- **FFmpeg** لمعالجة الفيديو (اختياري)

### للتطبيق الجوال
- **Flutter SDK 3.8+**
- **Dart SDK 3.0+**
- **Android Studio** أو **VS Code**
- **Android SDK** (للأندرويد)
- **Xcode** (لـ iOS - Mac فقط)

## 🔧 خطوات التثبيت

### 1. تحضير البيئة

#### تثبيت XAMPP
1. حم<PERSON> XAMPP من [الموقع الرسمي](https://www.apachefriends.org/)
2. ثبت XAMPP واتبع التعليمات
3. شغل XAMPP Control Panel
4. شغل Apache و MySQL

#### تثبيت Flutter
1. حمل Flutter من [الموقع الرسمي](https://flutter.dev/docs/get-started/install)
2. استخرج الملفات وأضف مسار Flutter إلى PATH
3. شغل `flutter doctor` للتحقق من التثبيت

### 2. إعداد المشروع

#### نسخ الملفات
```bash
# استنساخ المشروع
git clone https://github.com/your-username/streaming-platform.git
cd streaming-platform

# نسخ ملفات الموقع إلى XAMPP
copy website C:\xampp\htdocs\streaming_platform
```

#### إعداد قاعدة البيانات
```bash
# تشغيل سكريبت الإعداد التلقائي
setup_database.bat

# أو يدوياً:
mysql -u root -p
CREATE DATABASE streaming_platform;
USE streaming_platform;
SOURCE website/database/schema.sql;
```

#### إعداد ملف التكوين
1. انسخ `website/includes/config.example.php` إلى `website/includes/config.php`
2. عدّل إعدادات قاعدة البيانات:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'streaming_platform');
define('DB_USER', 'root');
define('DB_PASS', ''); // كلمة مرور MySQL
```

### 3. تشغيل الموقع

1. تأكد من تشغيل Apache و MySQL في XAMPP
2. افتح المتصفح وانتقل إلى: `http://localhost/streaming_platform`
3. يجب أن تظهر الصفحة الرئيسية للموقع

### 4. إعداد التطبيق الجوال

```bash
# الانتقال لمجلد التطبيق
cd mobile_app

# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق
flutter run
```

## 🚀 التشغيل السريع

### استخدام السكريبتات الجاهزة

#### Windows
```bash
# إعداد قاعدة البيانات
setup_database.bat

# تشغيل التطبيق
run_project.bat
```

#### Linux/Mac
```bash
# إعطاء صلاحيات التنفيذ
chmod +x run_project.sh

# تشغيل التطبيق
./run_project.sh
```

## ⚙️ إعدادات إضافية

### إعداد Firebase (للإشعارات)
1. أنشئ مشروع في [Firebase Console](https://console.firebase.google.com/)
2. أضف التطبيق الأندرويد/iOS
3. حمل ملف `google-services.json` (أندرويد) أو `GoogleService-Info.plist` (iOS)
4. ضع الملفات في المجلدات المناسبة:
   - أندرويد: `mobile_app/android/app/`
   - iOS: `mobile_app/ios/Runner/`

### إعداد الدفع (Stripe)
1. أنشئ حساب في [Stripe](https://stripe.com/)
2. احصل على مفاتيح API
3. أضفها في ملف التكوين:
```php
define('STRIPE_PUBLIC_KEY', 'pk_test_...');
define('STRIPE_SECRET_KEY', 'sk_test_...');
```

### إعداد البريد الإلكتروني
```php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في الاتصال بقاعدة البيانات
```
Error: Connection refused
```
**الحل:**
- تأكد من تشغيل MySQL
- تحقق من بيانات الاتصال في config.php
- تأكد من وجود قاعدة البيانات

#### خطأ في أذونات الملفات
```
Error: Permission denied
```
**الحل:**
```bash
# Windows
icacls uploads /grant Everyone:F /T
icacls cache /grant Everyone:F /T

# Linux/Mac
chmod 755 -R uploads/
chmod 755 -R cache/
```

#### مشاكل Flutter
```bash
# تنظيف المشروع
flutter clean
flutter pub get

# إعادة تثبيت التبعيات
flutter pub deps
```

#### خطأ في تشغيل الفيديو
- تأكد من تثبيت FFmpeg
- تحقق من صيغ الفيديو المدعومة
- تأكد من صلاحيات مجلد uploads

## 📱 اختبار التطبيق

### على المحاكي
```bash
# تشغيل محاكي أندرويد
flutter emulators --launch <emulator_id>

# تشغيل التطبيق
flutter run
```

### على جهاز حقيقي
1. فعّل وضع المطور في الجهاز
2. فعّل USB Debugging
3. وصل الجهاز بالكمبيوتر
4. شغل `flutter devices` للتأكد من اكتشاف الجهاز
5. شغل `flutter run`

## 🔐 إعدادات الأمان

### للإنتاج
1. غيّر مفاتيح التشفير في config.php
2. فعّل HTTPS
3. حدّث كلمات المرور الافتراضية
4. فعّل جدار الحماية
5. حدّث صلاحيات الملفات

### نصائح أمنية
- استخدم كلمات مرور قوية
- فعّل التحقق بخطوتين
- راقب سجلات الأخطاء
- حدّث النظام بانتظام

## 📊 مراقبة الأداء

### سجلات النظام
- سجلات PHP: `website/logs/`
- سجلات قاعدة البيانات: MySQL logs
- سجلات التطبيق: Flutter console

### أدوات المراقبة
- Google Analytics للموقع
- Firebase Analytics للتطبيق
- MySQL Performance Schema

## 🆘 الحصول على المساعدة

### الموارد المفيدة
- [وثائق Flutter](https://flutter.dev/docs)
- [وثائق PHP](https://www.php.net/docs.php)
- [وثائق MySQL](https://dev.mysql.com/doc/)

### التواصل
- GitHub Issues: لتقارير الأخطاء
- البريد الإلكتروني: <EMAIL>
- المجتمع: [Discord/Telegram]

## ✅ قائمة التحقق

### قبل التشغيل
- [ ] تثبيت XAMPP/WAMP
- [ ] تثبيت Flutter
- [ ] إنشاء قاعدة البيانات
- [ ] نسخ ملفات الموقع
- [ ] تكوين الإعدادات

### بعد التشغيل
- [ ] اختبار الموقع
- [ ] اختبار التطبيق
- [ ] اختبار تسجيل الدخول
- [ ] اختبار رفع الملفات
- [ ] اختبار مشغل الفيديو

---

**ملاحظة:** هذا الدليل يغطي الإعداد الأساسي. للإعداد المتقدم والإنتاج، راجع الوثائق التفصيلية.
