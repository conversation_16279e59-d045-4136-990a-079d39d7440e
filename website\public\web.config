<?xml version="1.0" encoding="UTF-8"?>
<!-- 🔧 ملف web.config لخادم IIS - منصة البث العربية -->
<configuration>
    <system.webServer>
        
        <!-- إعادة الكتابة والتوجيه -->
        <rewrite>
            <rules>
                <!-- إعادة توجيه HTTP إلى HTTPS -->
                <rule name="Redirect to HTTPS" stopProcessing="true">
                    <match url=".*" />
                    <conditions>
                        <add input="{HTTPS}" pattern="off" ignoreCase="true" />
                    </conditions>
                    <action type="Redirect" url="https://{HTTP_HOST}/{R:0}" redirectType="Permanent" />
                </rule>
                
                <!-- إعادة توجيه www إلى non-www -->
                <rule name="Remove WWW" stopProcessing="true">
                    <match url=".*" />
                    <conditions>
                        <add input="{HTTP_HOST}" pattern="^www\.(.*)$" />
                    </conditions>
                    <action type="Redirect" url="https://{C:1}/{R:0}" redirectType="Permanent" />
                </rule>
                
                <!-- URLs صديقة لمحركات البحث -->
                <rule name="Movie Page" stopProcessing="true">
                    <match url="^movie/([^/]+)/?$" />
                    <action type="Rewrite" url="movie.php?slug={R:1}" />
                </rule>
                
                <rule name="Series Page" stopProcessing="true">
                    <match url="^series/([^/]+)/?$" />
                    <action type="Rewrite" url="series.php?slug={R:1}" />
                </rule>
                
                <rule name="Episode Page" stopProcessing="true">
                    <match url="^series/([^/]+)/episode/([0-9]+)/?$" />
                    <action type="Rewrite" url="episode.php?series={R:1}&amp;episode={R:2}" />
                </rule>
                
                <rule name="Category Page" stopProcessing="true">
                    <match url="^category/([^/]+)/?$" />
                    <action type="Rewrite" url="category.php?slug={R:1}" />
                </rule>
                
                <rule name="Genre Page" stopProcessing="true">
                    <match url="^genre/([^/]+)/?$" />
                    <action type="Rewrite" url="genre.php?slug={R:1}" />
                </rule>
                
                <rule name="Actor Page" stopProcessing="true">
                    <match url="^actor/([^/]+)/?$" />
                    <action type="Rewrite" url="actor.php?slug={R:1}" />
                </rule>
                
                <rule name="Director Page" stopProcessing="true">
                    <match url="^director/([^/]+)/?$" />
                    <action type="Rewrite" url="director.php?slug={R:1}" />
                </rule>
                
                <rule name="Search Page" stopProcessing="true">
                    <match url="^search/?$" />
                    <action type="Rewrite" url="search.php" />
                </rule>
                
                <rule name="Movies Page" stopProcessing="true">
                    <match url="^movies/?$" />
                    <action type="Rewrite" url="movies.php" />
                </rule>
                
                <rule name="Series List Page" stopProcessing="true">
                    <match url="^series/?$" />
                    <action type="Rewrite" url="series.php" />
                </rule>
                
                <rule name="Documentaries Page" stopProcessing="true">
                    <match url="^documentaries/?$" />
                    <action type="Rewrite" url="documentaries.php" />
                </rule>
                
                <!-- API Routes -->
                <rule name="API Endpoint" stopProcessing="true">
                    <match url="^api/([^/]+)/?$" />
                    <action type="Rewrite" url="api/index.php?endpoint={R:1}" />
                </rule>
                
                <rule name="API Action" stopProcessing="true">
                    <match url="^api/([^/]+)/([^/]+)/?$" />
                    <action type="Rewrite" url="api/index.php?endpoint={R:1}&amp;action={R:2}" />
                </rule>
                
                <rule name="API ID" stopProcessing="true">
                    <match url="^api/([^/]+)/([^/]+)/([^/]+)/?$" />
                    <action type="Rewrite" url="api/index.php?endpoint={R:1}&amp;action={R:2}&amp;id={R:3}" />
                </rule>
                
                <!-- Admin Routes -->
                <rule name="Admin Home" stopProcessing="true">
                    <match url="^admin/?$" />
                    <action type="Rewrite" url="admin/index.php" />
                </rule>
                
                <rule name="Admin Page" stopProcessing="true">
                    <match url="^admin/([^/]+)/?$" />
                    <action type="Rewrite" url="admin/{R:1}.php" />
                </rule>
                
                <rule name="Admin Action" stopProcessing="true">
                    <match url="^admin/([^/]+)/([^/]+)/?$" />
                    <action type="Rewrite" url="admin/{R:1}.php?action={R:2}" />
                </rule>
                
                <!-- إزالة trailing slash -->
                <rule name="Remove Trailing Slash" stopProcessing="true">
                    <match url="(.*)/$" />
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                    </conditions>
                    <action type="Redirect" url="{R:1}" redirectType="Permanent" />
                </rule>
            </rules>
        </rewrite>
        
        <!-- رؤوس HTTP للأمان -->
        <httpProtocol>
            <customHeaders>
                <add name="X-Content-Type-Options" value="nosniff" />
                <add name="X-Frame-Options" value="SAMEORIGIN" />
                <add name="X-XSS-Protection" value="1; mode=block" />
                <add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
                <add name="Permissions-Policy" value="geolocation=(), microphone=(), camera=()" />
                <add name="Content-Security-Policy" value="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.googleapis.com *.gstatic.com *.google-analytics.com; style-src 'self' 'unsafe-inline' *.googleapis.com *.gstatic.com; img-src 'self' data: *.googleapis.com *.gstatic.com; font-src 'self' *.googleapis.com *.gstatic.com; connect-src 'self' *.google-analytics.com; media-src 'self' blob:; object-src 'none'; base-uri 'self'; form-action 'self';" />
            </customHeaders>
        </httpProtocol>
        
        <!-- ضغط المحتوى -->
        <httpCompression>
            <scheme name="gzip" dll="%Windir%\system32\inetsrv\gzip.dll" />
            <dynamicTypes>
                <add mimeType="text/*" enabled="true" />
                <add mimeType="message/*" enabled="true" />
                <add mimeType="application/javascript" enabled="true" />
                <add mimeType="application/json" enabled="true" />
                <add mimeType="application/xml" enabled="true" />
                <add mimeType="application/atom+xml" enabled="true" />
                <add mimeType="application/xaml+xml" enabled="true" />
                <add mimeType="image/svg+xml" enabled="true" />
            </dynamicTypes>
            <staticTypes>
                <add mimeType="text/*" enabled="true" />
                <add mimeType="message/*" enabled="true" />
                <add mimeType="application/javascript" enabled="true" />
                <add mimeType="application/json" enabled="true" />
                <add mimeType="application/xml" enabled="true" />
                <add mimeType="application/atom+xml" enabled="true" />
                <add mimeType="application/xaml+xml" enabled="true" />
                <add mimeType="image/svg+xml" enabled="true" />
            </staticTypes>
        </httpCompression>
        
        <!-- التخزين المؤقت -->
        <staticContent>
            <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="365.00:00:00" />
            
            <!-- MIME Types -->
            <mimeMap fileExtension=".json" mimeType="application/json" />
            <mimeMap fileExtension=".woff" mimeType="font/woff" />
            <mimeMap fileExtension=".woff2" mimeType="font/woff2" />
            <mimeMap fileExtension=".mp4" mimeType="video/mp4" />
            <mimeMap fileExtension=".webm" mimeType="video/webm" />
            <mimeMap fileExtension=".ogv" mimeType="video/ogg" />
            <mimeMap fileExtension=".m3u8" mimeType="application/x-mpegURL" />
            <mimeMap fileExtension=".ts" mimeType="video/MP2T" />
            <mimeMap fileExtension=".mpd" mimeType="application/dash+xml" />
            <mimeMap fileExtension=".webmanifest" mimeType="application/manifest+json" />
        </staticContent>
        
        <!-- صفحات الخطأ المخصصة -->
        <httpErrors errorMode="Custom">
            <remove statusCode="400" />
            <remove statusCode="401" />
            <remove statusCode="403" />
            <remove statusCode="404" />
            <remove statusCode="405" />
            <remove statusCode="500" />
            <remove statusCode="502" />
            <remove statusCode="503" />
            <remove statusCode="504" />
            
            <error statusCode="400" path="/error.php" responseMode="ExecuteURL" />
            <error statusCode="401" path="/error.php" responseMode="ExecuteURL" />
            <error statusCode="403" path="/error.php" responseMode="ExecuteURL" />
            <error statusCode="404" path="/error.php" responseMode="ExecuteURL" />
            <error statusCode="405" path="/error.php" responseMode="ExecuteURL" />
            <error statusCode="500" path="/error.php" responseMode="ExecuteURL" />
            <error statusCode="502" path="/error.php" responseMode="ExecuteURL" />
            <error statusCode="503" path="/error.php" responseMode="ExecuteURL" />
            <error statusCode="504" path="/error.php" responseMode="ExecuteURL" />
        </httpErrors>
        
        <!-- الأمان -->
        <security>
            <requestFiltering>
                <!-- حد أقصى لحجم المحتوى -->
                <requestLimits maxAllowedContentLength="104857600" />
                
                <!-- منع الملفات الخطيرة -->
                <fileExtensions>
                    <add fileExtension=".inc" allowed="false" />
                    <add fileExtension=".bak" allowed="false" />
                    <add fileExtension=".sql" allowed="false" />
                    <add fileExtension=".log" allowed="false" />
                    <add fileExtension=".config" allowed="false" />
                    <add fileExtension=".env" allowed="false" />
                </fileExtensions>
                
                <!-- منع المجلدات الحساسة -->
                <hiddenSegments>
                    <add segment="includes" />
                    <add segment="vendor" />
                    <add segment="cache" />
                    <add segment="logs" />
                    <add segment="backups" />
                    <add segment="config" />
                    <add segment="database" />
                    <add segment="storage" />
                    <add segment="tmp" />
                    <add segment="temp" />
                    <add segment=".git" />
                    <add segment=".svn" />
                    <add segment=".hg" />
                </hiddenSegments>
                
                <!-- منع الأفعال الخطيرة -->
                <verbs>
                    <add verb="TRACE" allowed="false" />
                    <add verb="TRACK" allowed="false" />
                </verbs>
            </requestFiltering>
        </security>
        
        <!-- إعدادات الافتراضية -->
        <defaultDocument>
            <files>
                <clear />
                <add value="index.php" />
                <add value="index.html" />
                <add value="default.php" />
                <add value="default.html" />
            </files>
        </defaultDocument>
        
        <!-- منع عرض محتوى المجلدات -->
        <directoryBrowse enabled="false" />
        
    </system.webServer>
    
    <!-- إعدادات PHP -->
    <system.web>
        <httpRuntime maxRequestLength="102400" executionTimeout="300" />
        <compilation debug="false" />
        <customErrors mode="On" defaultRedirect="/error.php" />
        <sessionState timeout="60" />
    </system.web>
    
</configuration>
