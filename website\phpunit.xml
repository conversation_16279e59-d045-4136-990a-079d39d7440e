<?xml version="1.0" encoding="UTF-8"?>
<!-- 🧪 إعدادات PHPUnit لاختبارات منصة البث العربية -->
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.6/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         verbose="true"
         stopOnFailure="false"
         processIsolation="false"
         backupGlobals="false"
         backupStaticAttributes="false"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         beStrictAboutTestsThatDoNotTestAnything="true"
         beStrictAboutOutputDuringTests="true"
         beStrictAboutTodoAnnotatedTests="true"
         beStrictAboutResourceUsageDuringSmallTests="true"
         enforceTimeLimit="true"
         defaultTimeLimit="10"
         timeoutForSmallTests="5"
         timeoutForMediumTests="20"
         timeoutForLargeTests="60">

    <!-- مجموعات الاختبار -->
    <testsuites>
        <testsuite name="Unit Tests">
            <directory>tests/Unit</directory>
        </testsuite>
        
        <testsuite name="Feature Tests">
            <directory>tests/Feature</directory>
        </testsuite>
        
        <testsuite name="Integration Tests">
            <directory>tests/Integration</directory>
        </testsuite>
        
        <testsuite name="API Tests">
            <directory>tests/Api</directory>
        </testsuite>
        
        <testsuite name="Database Tests">
            <directory>tests/Database</directory>
        </testsuite>
    </testsuites>

    <!-- تغطية الكود -->
    <coverage processUncoveredFiles="true">
        <include>
            <directory suffix=".php">src</directory>
            <directory suffix=".php">includes</directory>
        </include>
        
        <exclude>
            <directory>vendor</directory>
            <directory>tests</directory>
            <directory>cache</directory>
            <directory>logs</directory>
            <file>includes/config.php</file>
        </exclude>
        
        <report>
            <html outputDirectory="coverage/html"/>
            <text outputFile="coverage/coverage.txt"/>
            <xml outputDirectory="coverage/xml"/>
            <clover outputFile="coverage/clover.xml"/>
        </report>
    </coverage>

    <!-- متغيرات البيئة للاختبار -->
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="HASH_DRIVER" value="bcrypt"/>
        <env name="LOG_CHANNEL" value="single"/>
        <env name="LOG_LEVEL" value="debug"/>
    </php>

    <!-- إعدادات السجلات -->
    <logging>
        <junit outputFile="tests/results/junit.xml"/>
        <teamcity outputFile="tests/results/teamcity.txt"/>
        <testdoxHtml outputFile="tests/results/testdox.html"/>
        <testdoxText outputFile="tests/results/testdox.txt"/>
        <testdoxXml outputFile="tests/results/testdox.xml"/>
    </logging>

    <!-- المستمعين -->
    <listeners>
        <listener class="Symfony\Bridge\PhpUnit\SymfonyTestsListener"/>
    </listeners>

    <!-- الإضافات -->
    <extensions>
        <extension class="DAMA\DoctrineTestBundle\PHPUnit\PHPUnitExtension"/>
    </extensions>

</phpunit>
