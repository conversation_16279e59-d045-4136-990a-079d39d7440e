# 🔄 GitHub Actions للتكامل والنشر المستمر - منصة البث العربية

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # اختبار الكود
  test:
    name: 🧪 اختبار الكود
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: streaming_platform_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - name: 📥 استنساخ الكود
      uses: actions/checkout@v4
    
    - name: 🐘 إعداد PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'
        extensions: mbstring, xml, ctype, iconv, intl, pdo, pdo_mysql, dom, filter, gd, json, zip
        coverage: xdebug
    
    - name: 📦 تثبيت Composer
      run: composer install --prefer-dist --no-progress --no-suggest
      working-directory: ./website
    
    - name: 🔧 إعداد البيئة
      run: |
        cp website/includes/config.example.php website/includes/config.php
        sed -i 's/localhost/127.0.0.1/g' website/includes/config.php
        sed -i 's/streaming_platform/streaming_platform_test/g' website/includes/config.php
    
    - name: 🗄️ إعداد قاعدة البيانات
      run: |
        mysql -h 127.0.0.1 -u root -proot streaming_platform_test < setup.sql
    
    - name: 🧪 تشغيل اختبارات PHP
      run: |
        cd website
        vendor/bin/phpunit --coverage-text --coverage-clover=coverage.xml
    
    - name: 📊 رفع تقرير التغطية
      uses: codecov/codecov-action@v3
      with:
        file: ./website/coverage.xml
        flags: php
        name: php-coverage
    
    - name: 🔍 فحص جودة الكود
      run: |
        cd website
        vendor/bin/phpcs --standard=PSR12 src/ includes/
        vendor/bin/phpstan analyse src/ includes/ --level=5

  # اختبار التطبيق الجوال
  test-mobile:
    name: 📱 اختبار التطبيق الجوال
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 استنساخ الكود
      uses: actions/checkout@v4
    
    - name: 🎯 إعداد Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.13.0'
        channel: 'stable'
    
    - name: 📦 تثبيت التبعيات
      run: |
        cd mobile_app
        flutter pub get
    
    - name: 🔍 تحليل الكود
      run: |
        cd mobile_app
        flutter analyze
    
    - name: 🧪 تشغيل الاختبارات
      run: |
        cd mobile_app
        flutter test --coverage
    
    - name: 📊 رفع تقرير التغطية
      uses: codecov/codecov-action@v3
      with:
        file: ./mobile_app/coverage/lcov.info
        flags: flutter
        name: flutter-coverage

  # بناء الصور
  build:
    name: 🏗️ بناء الصور
    runs-on: ubuntu-latest
    needs: [test, test-mobile]
    if: github.event_name != 'pull_request'
    
    permissions:
      contents: read
      packages: write
    
    steps:
    - name: 📥 استنساخ الكود
      uses: actions/checkout@v4
    
    - name: 🐳 إعداد Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: 🔐 تسجيل الدخول للسجل
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: 📝 استخراج البيانات الوصفية
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: 🏗️ بناء ونشر الصورة
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # اختبار الأمان
  security:
    name: 🔒 اختبار الأمان
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 استنساخ الكود
      uses: actions/checkout@v4
    
    - name: 🔍 فحص الثغرات الأمنية
      uses: securecodewarrior/github-action-add-sarif@v1
      with:
        sarif-file: 'security-scan-results.sarif'
    
    - name: 🛡️ فحص التبعيات
      run: |
        cd website
        composer audit
    
    - name: 🔐 فحص أسرار Git
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD

  # النشر للإنتاج
  deploy:
    name: 🚀 النشر للإنتاج
    runs-on: ubuntu-latest
    needs: [build, security]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    environment:
      name: production
      url: https://your-domain.com
    
    steps:
    - name: 📥 استنساخ الكود
      uses: actions/checkout@v4
    
    - name: 🔧 إعداد SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
    
    - name: 🚀 النشر للخادم
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} << 'EOF'
          cd /var/www/streaming-platform
          git pull origin main
          docker-compose pull
          docker-compose up -d --force-recreate
          docker system prune -f
        EOF
    
    - name: 🧪 اختبار النشر
      run: |
        sleep 30
        curl -f https://your-domain.com/test.php || exit 1
    
    - name: 📧 إشعار النشر
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      if: always()

  # النشر للتطوير
  deploy-staging:
    name: 🧪 النشر للتطوير
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    
    environment:
      name: staging
      url: https://staging.your-domain.com
    
    steps:
    - name: 📥 استنساخ الكود
      uses: actions/checkout@v4
    
    - name: 🚀 النشر لخادم التطوير
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.STAGING_SSH_USER }}@${{ secrets.STAGING_SSH_HOST }} << 'EOF'
          cd /var/www/streaming-platform-staging
          git pull origin develop
          docker-compose -f docker-compose.staging.yml pull
          docker-compose -f docker-compose.staging.yml up -d --force-recreate
        EOF

  # إنشاء الإصدار
  release:
    name: 📦 إنشاء الإصدار
    runs-on: ubuntu-latest
    needs: [deploy]
    if: github.event_name == 'release'
    
    steps:
    - name: 📥 استنساخ الكود
      uses: actions/checkout@v4
    
    - name: 📦 إنشاء حزمة الإصدار
      run: |
        tar -czf streaming-platform-${{ github.event.release.tag_name }}.tar.gz \
          --exclude='.git' \
          --exclude='node_modules' \
          --exclude='vendor' \
          --exclude='.github' \
          .
    
    - name: 📤 رفع حزمة الإصدار
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ github.event.release.upload_url }}
        asset_path: ./streaming-platform-${{ github.event.release.tag_name }}.tar.gz
        asset_name: streaming-platform-${{ github.event.release.tag_name }}.tar.gz
        asset_content_type: application/gzip
