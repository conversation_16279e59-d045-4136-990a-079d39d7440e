/**
 * 🚀 اختبارات الأداء والحمولة - منصة البث العربية
 * Performance and Load Tests for Arabic Streaming Platform
 */

const autocannon = require('autocannon');
const { expect } = require('chai');
const fs = require('fs');
const path = require('path');

describe('⚡ اختبارات الأداء والحمولة', () => {
    const baseUrl = process.env.TEST_URL || 'http://localhost:8000';
    let performanceResults = {};

    before(() => {
        console.log('🔧 إعداد اختبارات الأداء...');
        console.log(`🌐 URL الاختبار: ${baseUrl}`);
    });

    after(() => {
        console.log('📊 حفظ نتائج الأداء...');
        savePerformanceResults(performanceResults);
    });

    describe('🏠 اختبارات أداء الصفحة الرئيسية', () => {
        
        it('يجب أن تحمل الصفحة الرئيسية بسرعة', async () => {
            const result = await autocannon({
                url: `${baseUrl}/`,
                connections: 10,
                duration: 10,
                headers: {
                    'User-Agent': 'Performance-Test-Bot'
                }
            });

            performanceResults.homepage = result;

            // التحقق من الأداء
            expect(result.latency.average).to.be.lessThan(500); // أقل من 500ms
            expect(result.requests.average).to.be.greaterThan(50); // أكثر من 50 طلب/ثانية
            expect(result.errors).to.equal(0); // لا أخطاء
            
            console.log(`📈 الصفحة الرئيسية - متوسط الاستجابة: ${result.latency.average}ms`);
            console.log(`📈 الصفحة الرئيسية - الطلبات/ثانية: ${result.requests.average}`);
        });

        it('يجب أن تتحمل حمولة عالية', async () => {
            const result = await autocannon({
                url: `${baseUrl}/`,
                connections: 100,
                duration: 30,
                headers: {
                    'User-Agent': 'Load-Test-Bot'
                }
            });

            performanceResults.homepage_load = result;

            // التحقق من تحمل الحمولة
            expect(result.latency.average).to.be.lessThan(2000); // أقل من 2 ثانية
            expect(result.errors).to.be.lessThan(result.requests.total * 0.01); // أقل من 1% أخطاء
            
            console.log(`🔥 اختبار الحمولة - متوسط الاستجابة: ${result.latency.average}ms`);
            console.log(`🔥 اختبار الحمولة - إجمالي الطلبات: ${result.requests.total}`);
        });
    });

    describe('🎬 اختبارات أداء مشغل الفيديو', () => {
        
        it('يجب أن يحمل مشغل الفيديو بسرعة', async () => {
            const result = await autocannon({
                url: `${baseUrl}/player.php?id=1`,
                connections: 20,
                duration: 15,
                headers: {
                    'User-Agent': 'Video-Player-Test'
                }
            });

            performanceResults.video_player = result;

            expect(result.latency.average).to.be.lessThan(800); // أقل من 800ms
            expect(result.requests.average).to.be.greaterThan(30);
            
            console.log(`🎥 مشغل الفيديو - متوسط الاستجابة: ${result.latency.average}ms`);
        });

        it('يجب أن يتعامل مع مشاهدين متعددين', async () => {
            const result = await autocannon({
                url: `${baseUrl}/player.php?id=1`,
                connections: 200,
                duration: 20,
                headers: {
                    'User-Agent': 'Multiple-Viewers-Test'
                }
            });

            performanceResults.multiple_viewers = result;

            expect(result.latency.average).to.be.lessThan(3000); // أقل من 3 ثوانٍ
            expect(result.errors).to.be.lessThan(result.requests.total * 0.05); // أقل من 5% أخطاء
            
            console.log(`👥 مشاهدين متعددين - متوسط الاستجابة: ${result.latency.average}ms`);
        });
    });

    describe('🔍 اختبارات أداء البحث', () => {
        
        it('يجب أن يستجيب البحث بسرعة', async () => {
            const searchTerms = ['فيلم', 'مسلسل', 'وثائقي', 'كوميدي', 'دراما'];
            
            for (const term of searchTerms) {
                const result = await autocannon({
                    url: `${baseUrl}/api/search/videos?q=${encodeURIComponent(term)}`,
                    connections: 15,
                    duration: 10,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                performanceResults[`search_${term}`] = result;

                expect(result.latency.average).to.be.lessThan(600); // أقل من 600ms
                
                console.log(`🔍 البحث عن "${term}" - متوسط الاستجابة: ${result.latency.average}ms`);
            }
        });

        it('يجب أن يتعامل مع بحث متزامن', async () => {
            const result = await autocannon({
                url: `${baseUrl}/api/search/videos?q=test`,
                connections: 50,
                duration: 15,
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            performanceResults.concurrent_search = result;

            expect(result.latency.average).to.be.lessThan(1000); // أقل من ثانية
            expect(result.requests.average).to.be.greaterThan(40);
            
            console.log(`🔄 البحث المتزامن - متوسط الاستجابة: ${result.latency.average}ms`);
        });
    });

    describe('📱 اختبارات أداء API', () => {
        
        it('يجب أن تستجيب نقاط النهاية بسرعة', async () => {
            const endpoints = [
                '/api/content/videos',
                '/api/content/categories',
                '/api/analytics/trending'
            ];

            for (const endpoint of endpoints) {
                const result = await autocannon({
                    url: `${baseUrl}${endpoint}`,
                    connections: 25,
                    duration: 12,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                performanceResults[`api_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`] = result;

                expect(result.latency.average).to.be.lessThan(400); // أقل من 400ms
                expect(result.requests.average).to.be.greaterThan(60);
                
                console.log(`📡 API ${endpoint} - متوسط الاستجابة: ${result.latency.average}ms`);
            }
        });

        it('يجب أن تتحمل API حمولة عالية', async () => {
            const result = await autocannon({
                url: `${baseUrl}/api/content/videos`,
                connections: 100,
                duration: 25,
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            performanceResults.api_load_test = result;

            expect(result.latency.average).to.be.lessThan(1500); // أقل من 1.5 ثانية
            expect(result.errors).to.be.lessThan(result.requests.total * 0.02); // أقل من 2% أخطاء
            
            console.log(`🚀 API تحت الحمولة - متوسط الاستجابة: ${result.latency.average}ms`);
        });
    });

    describe('🔐 اختبارات أداء المصادقة', () => {
        
        it('يجب أن تستجيب عملية تسجيل الدخول بسرعة', async () => {
            const result = await autocannon({
                url: `${baseUrl}/api/auth/login`,
                method: 'POST',
                connections: 20,
                duration: 10,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: '<EMAIL>',
                    password: 'testpassword'
                })
            });

            performanceResults.auth_login = result;

            expect(result.latency.average).to.be.lessThan(700); // أقل من 700ms
            
            console.log(`🔐 تسجيل الدخول - متوسط الاستجابة: ${result.latency.average}ms`);
        });
    });

    describe('📊 اختبارات أداء قاعدة البيانات', () => {
        
        it('يجب أن تستجيب استعلامات قاعدة البيانات بسرعة', async () => {
            // اختبار استعلامات مختلفة
            const dbEndpoints = [
                '/api/content/videos?limit=50',
                '/api/analytics/stats',
                '/api/user/favorites'
            ];

            for (const endpoint of dbEndpoints) {
                const result = await autocannon({
                    url: `${baseUrl}${endpoint}`,
                    connections: 30,
                    duration: 15,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                performanceResults[`db_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`] = result;

                expect(result.latency.average).to.be.lessThan(500); // أقل من 500ms
                
                console.log(`🗄️ قاعدة البيانات ${endpoint} - متوسط الاستجابة: ${result.latency.average}ms`);
            }
        });
    });

    describe('🎯 اختبارات أداء التخزين المؤقت', () => {
        
        it('يجب أن يحسن التخزين المؤقت الأداء', async () => {
            // اختبار بدون تخزين مؤقت
            const withoutCache = await autocannon({
                url: `${baseUrl}/api/content/videos`,
                connections: 20,
                duration: 10,
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });

            // اختبار مع التخزين المؤقت
            const withCache = await autocannon({
                url: `${baseUrl}/api/content/videos`,
                connections: 20,
                duration: 10
            });

            performanceResults.cache_comparison = {
                without_cache: withoutCache,
                with_cache: withCache
            };

            // التخزين المؤقت يجب أن يحسن الأداء
            expect(withCache.latency.average).to.be.lessThan(withoutCache.latency.average);
            
            console.log(`💾 بدون تخزين مؤقت: ${withoutCache.latency.average}ms`);
            console.log(`💾 مع التخزين المؤقت: ${withCache.latency.average}ms`);
        });
    });

    describe('📈 اختبارات الضغط المتدرج', () => {
        
        it('يجب أن يتعامل مع زيادة الحمولة تدريجياً', async () => {
            const connections = [10, 25, 50, 100, 200];
            const stressResults = [];

            for (const conn of connections) {
                console.log(`🔄 اختبار مع ${conn} اتصال متزامن...`);
                
                const result = await autocannon({
                    url: `${baseUrl}/`,
                    connections: conn,
                    duration: 15,
                    headers: {
                        'User-Agent': `Stress-Test-${conn}`
                    }
                });

                stressResults.push({
                    connections: conn,
                    latency: result.latency.average,
                    throughput: result.requests.average,
                    errors: result.errors
                });

                // التحقق من عدم تدهور الأداء بشكل كبير
                if (conn <= 100) {
                    expect(result.latency.average).to.be.lessThan(2000);
                }
                
                console.log(`📊 ${conn} اتصال - الاستجابة: ${result.latency.average}ms, الإنتاجية: ${result.requests.average} req/s`);
            }

            performanceResults.stress_test = stressResults;

            // التحقق من أن النظام لا ينهار تحت الضغط
            const lastResult = stressResults[stressResults.length - 1];
            expect(lastResult.errors).to.be.lessThan(lastResult.connections * 10); // معدل خطأ معقول
        });
    });

    describe('🔄 اختبارات التحمل طويل المدى', () => {
        
        it('يجب أن يحافظ على الأداء لفترة طويلة', async () => {
            console.log('⏱️ بدء اختبار التحمل طويل المدى (5 دقائق)...');
            
            const result = await autocannon({
                url: `${baseUrl}/`,
                connections: 50,
                duration: 300, // 5 دقائق
                headers: {
                    'User-Agent': 'Endurance-Test'
                }
            });

            performanceResults.endurance_test = result;

            // التحقق من الاستقرار
            expect(result.latency.average).to.be.lessThan(1000); // أقل من ثانية
            expect(result.errors).to.be.lessThan(result.requests.total * 0.01); // أقل من 1% أخطاء
            
            console.log(`⏱️ اختبار التحمل - متوسط الاستجابة: ${result.latency.average}ms`);
            console.log(`⏱️ اختبار التحمل - إجمالي الطلبات: ${result.requests.total}`);
        });
    });
});

// ===== وظائف مساعدة =====

function savePerformanceResults(results) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `performance-results-${timestamp}.json`;
    const filepath = path.join(__dirname, 'reports', filename);

    // إنشاء مجلد التقارير إذا لم يكن موجوداً
    const reportsDir = path.dirname(filepath);
    if (!fs.existsSync(reportsDir)) {
        fs.mkdirSync(reportsDir, { recursive: true });
    }

    // حفظ النتائج
    fs.writeFileSync(filepath, JSON.stringify(results, null, 2));
    
    // إنشاء تقرير HTML
    generateHtmlReport(results, filepath.replace('.json', '.html'));
    
    console.log(`📄 تم حفظ نتائج الأداء في: ${filename}`);
}

function generateHtmlReport(results, filepath) {
    const html = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير أداء منصة البث العربية</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; }
        .metric { background: #ecf0f1; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .good { border-left: 5px solid #27ae60; }
        .warning { border-left: 5px solid #f39c12; }
        .error { border-left: 5px solid #e74c3c; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: right; border-bottom: 1px solid #ddd; }
        th { background-color: #34495e; color: white; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 تقرير أداء منصة البث العربية</h1>
        <p>تاريخ الاختبار: ${new Date().toLocaleString('ar-SA')}</p>
    </div>
    
    <h2>📈 ملخص النتائج</h2>
    ${generateSummaryTable(results)}
    
    <h2>📋 تفاصيل الاختبارات</h2>
    ${generateDetailedResults(results)}
    
    <h2>💡 التوصيات</h2>
    ${generateRecommendations(results)}
</body>
</html>`;

    fs.writeFileSync(filepath, html);
}

function generateSummaryTable(results) {
    let html = '<table><tr><th>الاختبار</th><th>متوسط الاستجابة (ms)</th><th>الطلبات/ثانية</th><th>الأخطاء</th><th>الحالة</th></tr>';
    
    for (const [test, result] of Object.entries(results)) {
        if (result.latency) {
            const status = result.latency.average < 500 ? 'جيد' : result.latency.average < 1000 ? 'متوسط' : 'يحتاج تحسين';
            const statusClass = result.latency.average < 500 ? 'good' : result.latency.average < 1000 ? 'warning' : 'error';
            
            html += `<tr class="${statusClass}">
                <td>${test}</td>
                <td>${Math.round(result.latency.average)}</td>
                <td>${Math.round(result.requests.average)}</td>
                <td>${result.errors || 0}</td>
                <td>${status}</td>
            </tr>`;
        }
    }
    
    html += '</table>';
    return html;
}

function generateDetailedResults(results) {
    let html = '';
    
    for (const [test, result] of Object.entries(results)) {
        if (result.latency) {
            html += `<div class="metric">
                <h3>${test}</h3>
                <p><strong>متوسط الاستجابة:</strong> ${Math.round(result.latency.average)}ms</p>
                <p><strong>أقل استجابة:</strong> ${result.latency.min}ms</p>
                <p><strong>أعلى استجابة:</strong> ${result.latency.max}ms</p>
                <p><strong>الطلبات/ثانية:</strong> ${Math.round(result.requests.average)}</p>
                <p><strong>إجمالي الطلبات:</strong> ${result.requests.total}</p>
                <p><strong>الأخطاء:</strong> ${result.errors || 0}</p>
            </div>`;
        }
    }
    
    return html;
}

function generateRecommendations(results) {
    const recommendations = [];
    
    for (const [test, result] of Object.entries(results)) {
        if (result.latency && result.latency.average > 1000) {
            recommendations.push(`⚠️ ${test}: الاستجابة بطيئة (${Math.round(result.latency.average)}ms) - يحتاج تحسين`);
        }
        if (result.errors > 0) {
            recommendations.push(`❌ ${test}: يحتوي على ${result.errors} خطأ - يحتاج مراجعة`);
        }
    }
    
    if (recommendations.length === 0) {
        recommendations.push('✅ جميع الاختبارات تعمل بشكل ممتاز!');
    }
    
    return '<ul><li>' + recommendations.join('</li><li>') + '</li></ul>';
}

module.exports = {
    savePerformanceResults,
    generateHtmlReport
};
