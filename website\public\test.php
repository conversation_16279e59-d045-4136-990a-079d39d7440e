<?php
/**
 * 🧪 صفحة اختبار المنصة
 * للتحقق من عمل جميع المكونات
 */

// تعريف ثابت المنصة
define('STREAMING_PLATFORM', true);

// إعدادات أساسية
define('DB_HOST', 'localhost');
define('DB_NAME', 'streaming_platform');
define('DB_USER', 'root');
define('DB_PASS', '');
define('SITE_NAME', 'منصة البث');
define('DEBUG_MODE', true);

// بدء الجلسة
session_start();

// متغيرات الحالة
$dbConnected = false;
$dbError = '';
$dbStats = [];

// اختبار الاتصال بقاعدة البيانات
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnected = true;
    
    // الحصول على إحصائيات
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $dbStats['users'] = $stmt->fetch()['count'] ?? 0;
    } catch (Exception $e) {
        $dbStats['users'] = 'غير متاح';
    }
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM content");
        $dbStats['content'] = $stmt->fetch()['count'] ?? 0;
    } catch (Exception $e) {
        $dbStats['content'] = 'غير متاح';
    }
    
} catch (PDOException $e) {
    $dbError = $e->getMessage();
}

// معلومات النظام
$systemInfo = [
    'PHP Version' => phpversion(),
    'Server Software' => $_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف',
    'Document Root' => $_SERVER['DOCUMENT_ROOT'] ?? 'غير معروف',
    'Current Directory' => __DIR__,
    'Memory Limit' => ini_get('memory_limit'),
    'Max Execution Time' => ini_get('max_execution_time') . ' ثانية',
    'Upload Max Filesize' => ini_get('upload_max_filesize'),
    'Post Max Size' => ini_get('post_max_size')
];

// اختبار الملفات المطلوبة
$requiredFiles = [
    '../includes/config.php',
    '../includes/database.php',
    '../includes/functions.php',
    '../includes/security.php',
    '../includes/init.php',
    '404.php',
    '500.php'
];

$fileStatus = [];
foreach ($requiredFiles as $file) {
    $fileStatus[$file] = file_exists(__DIR__ . '/' . $file);
}

// اختبار المجلدات المطلوبة
$requiredDirs = [
    'assets',
    'assets/css',
    'assets/js',
    'assets/images',
    'uploads',
    '../logs',
    '../cache'
];

$dirStatus = [];
foreach ($requiredDirs as $dir) {
    $dirStatus[$dir] = is_dir(__DIR__ . '/' . $dir);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المنصة - <?php echo SITE_NAME; ?></title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }

        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }

        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }

        .status-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .status-message {
            line-height: 1.6;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .info-table th,
        .info-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }

        .info-table th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .status-icon {
            font-size: 1.2rem;
            margin-left: 10px;
        }

        .success-icon { color: #28a745; }
        .error-icon { color: #dc3545; }
        .warning-icon { color: #ffc107; }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .alert {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 اختبار منصة البث</h1>
            <p>فحص شامل لجميع مكونات المنصة</p>
        </div>

        <div class="content">
            <!-- حالة قاعدة البيانات -->
            <div class="section">
                <h2 class="section-title">🗄️ حالة قاعدة البيانات</h2>
                
                <?php if ($dbConnected): ?>
                    <div class="alert alert-success">
                        <span class="status-icon success-icon">✅</span>
                        <strong>نجح الاتصال بقاعدة البيانات!</strong>
                    </div>
                    
                    <table class="info-table">
                        <tr>
                            <th>الخادم</th>
                            <td><?php echo DB_HOST; ?></td>
                        </tr>
                        <tr>
                            <th>قاعدة البيانات</th>
                            <td><?php echo DB_NAME; ?></td>
                        </tr>
                        <tr>
                            <th>المستخدم</th>
                            <td><?php echo DB_USER; ?></td>
                        </tr>
                        <tr>
                            <th>عدد المستخدمين</th>
                            <td><?php echo $dbStats['users']; ?></td>
                        </tr>
                        <tr>
                            <th>عدد المحتوى</th>
                            <td><?php echo $dbStats['content']; ?></td>
                        </tr>
                    </table>
                <?php else: ?>
                    <div class="alert alert-error">
                        <span class="status-icon error-icon">❌</span>
                        <strong>فشل الاتصال بقاعدة البيانات!</strong>
                        <div class="code-block"><?php echo $dbError; ?></div>
                        
                        <h4>خطوات الحل:</h4>
                        <ol>
                            <li>تأكد من تشغيل خادم MySQL</li>
                            <li>أنشئ قاعدة بيانات باسم 'streaming_platform'</li>
                            <li>تحقق من بيانات الاتصال</li>
                            <li>شغل ملف setup.sql</li>
                        </ol>
                    </div>
                <?php endif; ?>
            </div>

            <!-- حالة الملفات -->
            <div class="section">
                <h2 class="section-title">📁 حالة الملفات المطلوبة</h2>
                
                <div class="status-grid">
                    <?php foreach ($fileStatus as $file => $exists): ?>
                    <div class="status-card <?php echo $exists ? 'success' : 'error'; ?>">
                        <div class="status-title">
                            <span class="status-icon <?php echo $exists ? 'success-icon' : 'error-icon'; ?>">
                                <?php echo $exists ? '✅' : '❌'; ?>
                            </span>
                            <?php echo $file; ?>
                        </div>
                        <div class="status-message">
                            <?php echo $exists ? 'موجود' : 'مفقود'; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- حالة المجلدات -->
            <div class="section">
                <h2 class="section-title">📂 حالة المجلدات المطلوبة</h2>
                
                <div class="status-grid">
                    <?php foreach ($dirStatus as $dir => $exists): ?>
                    <div class="status-card <?php echo $exists ? 'success' : 'warning'; ?>">
                        <div class="status-title">
                            <span class="status-icon <?php echo $exists ? 'success-icon' : 'warning-icon'; ?>">
                                <?php echo $exists ? '✅' : '⚠️'; ?>
                            </span>
                            <?php echo $dir; ?>
                        </div>
                        <div class="status-message">
                            <?php echo $exists ? 'موجود' : 'سيتم إنشاؤه تلقائياً'; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="section">
                <h2 class="section-title">⚙️ معلومات النظام</h2>
                
                <table class="info-table">
                    <?php foreach ($systemInfo as $key => $value): ?>
                    <tr>
                        <th><?php echo $key; ?></th>
                        <td><?php echo $value; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </table>
            </div>

            <!-- الإجراءات -->
            <div class="section">
                <h2 class="section-title">🚀 الإجراءات التالية</h2>
                
                <?php if ($dbConnected): ?>
                    <div class="alert alert-success">
                        <strong>المنصة جاهزة للاستخدام!</strong>
                        يمكنك الآن الوصول إلى الصفحة الرئيسية وبدء الاستخدام.
                    </div>
                    
                    <a href="index.php" class="btn">🏠 الصفحة الرئيسية</a>
                    <a href="quick-start.html" class="btn">📖 العرض التوضيحي</a>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <strong>يجب إعداد قاعدة البيانات أولاً</strong>
                        
                        <div class="code-block">
-- أنشئ قاعدة البيانات
CREATE DATABASE streaming_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استخدم قاعدة البيانات
USE streaming_platform;

-- شغل ملف الإعداد
SOURCE setup.sql;
                        </div>
                    </div>
                    
                    <a href="quick-start.html" class="btn">📖 العرض التوضيحي</a>
                    <button onclick="location.reload()" class="btn">🔄 إعادة الاختبار</button>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // تحديث تلقائي كل 30 ثانية
        setTimeout(function() {
            location.reload();
        }, 30000);
        
        console.log('🎬 منصة البث - صفحة الاختبار');
        console.log('حالة قاعدة البيانات:', <?php echo $dbConnected ? 'true' : 'false'; ?>);
    </script>
</body>
</html>
