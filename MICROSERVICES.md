# 🔧 هندسة الخدمات المصغرة - منصة البث العربية

## 🎯 **نظرة عامة**

هذا الدليل يوضح كيفية تطبيق هندسة الخدمات المصغرة (Microservices) في منصة البث العربية لتحقيق قابلية التوسع والمرونة والموثوقية.

### **مبادئ الخدمات المصغرة**
- **الاستقلالية**: كل خدمة مستقلة ومنفصلة
- **المسؤولية الواحدة**: كل خدمة لها غرض محدد
- **التواصل عبر API**: التفاعل عبر REST/GraphQL
- **قاعدة البيانات المنفصلة**: كل خدمة لها قاعدة بيانات خاصة
- **النشر المستقل**: إمكانية نشر كل خدمة بشكل منفصل

---

## 🏗️ **بنية الخدمات المصغرة**

### **الخدمات الأساسية**

```
┌─────────────────────────────────────────────────────────────┐
│                    API Gateway (Kong/Nginx)                 │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                   Service Mesh (Istio)                      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │    Auth     │ │   Content   │ │   Streaming │           │
│  │   Service   │ │   Service   │ │   Service   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   User      │ │  Analytics  │ │ Notification│           │
│  │  Service    │ │   Service   │ │   Service   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  Payment    │ │   Search    │ │   Upload    │           │
│  │  Service    │ │   Service   │ │   Service   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

---

## 🔐 **خدمة المصادقة (Auth Service)**

### **المسؤوليات**
- إدارة تسجيل الدخول والخروج
- إصدار والتحقق من JWT tokens
- إدارة الصلاحيات والأدوار
- المصادقة الثنائية
- إدارة كلمات المرور

### **API Endpoints**
```yaml
# auth-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: auth-service
spec:
  selector:
    app: auth-service
  ports:
  - port: 8001
    targetPort: 8080
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: streaming-platform/auth-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: DB_HOST
          value: "auth-db"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: auth-secrets
              key: jwt-secret
```

### **Database Schema**
```sql
-- Auth Service Database
CREATE DATABASE auth_service;

CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    role VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🎬 **خدمة المحتوى (Content Service)**

### **المسؤوليات**
- إدارة الأفلام والمسلسلات
- إدارة البيانات الوصفية
- إدارة التصنيفات والأنواع
- إدارة التقييمات والمراجعات
- إدارة قوائم التشغيل

### **Service Implementation**
```javascript
// content-service/src/app.js
const express = require('express');
const { Pool } = require('pg');
const redis = require('redis');

const app = express();
const db = new Pool({
  connectionString: process.env.DATABASE_URL
});
const cache = redis.createClient(process.env.REDIS_URL);

// Get all movies
app.get('/api/movies', async (req, res) => {
  try {
    const { page = 1, limit = 20, genre, year } = req.query;
    const offset = (page - 1) * limit;
    
    let query = 'SELECT * FROM movies WHERE 1=1';
    const params = [];
    
    if (genre) {
      query += ' AND genre = $' + (params.length + 1);
      params.push(genre);
    }
    
    if (year) {
      query += ' AND year = $' + (params.length + 1);
      params.push(year);
    }
    
    query += ' ORDER BY created_at DESC LIMIT $' + (params.length + 1) + ' OFFSET $' + (params.length + 2);
    params.push(limit, offset);
    
    const result = await db.query(query, params);
    
    res.json({
      success: true,
      data: {
        movies: result.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: result.rowCount
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get movie by ID
app.get('/api/movies/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check cache first
    const cached = await cache.get(`movie:${id}`);
    if (cached) {
      return res.json({
        success: true,
        data: JSON.parse(cached)
      });
    }
    
    const result = await db.query('SELECT * FROM movies WHERE id = $1', [id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Movie not found'
      });
    }
    
    const movie = result.rows[0];
    
    // Cache for 1 hour
    await cache.setex(`movie:${id}`, 3600, JSON.stringify(movie));
    
    res.json({
      success: true,
      data: movie
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

app.listen(8080, () => {
  console.log('Content Service running on port 8080');
});
```

---

## 📺 **خدمة البث (Streaming Service)**

### **المسؤوليات**
- إنشاء روابط البث الآمنة
- إدارة جودات الفيديو المختلفة
- تتبع تقدم المشاهدة
- إدارة الترجمات
- تحليل استهلاك البيانات

### **Service Implementation**
```python
# streaming-service/app.py
from flask import Flask, request, jsonify
import jwt
import redis
import hashlib
import time
from datetime import datetime, timedelta

app = Flask(__name__)
redis_client = redis.Redis(host='redis', port=6379, db=0)

class StreamingService:
    def __init__(self):
        self.cdn_base_url = "https://cdn.streaming-platform.com"
        self.jwt_secret = "your-jwt-secret"
    
    def generate_stream_url(self, movie_id, user_id, quality='720p'):
        """Generate secure streaming URL with expiration"""
        
        # Create token payload
        payload = {
            'movie_id': movie_id,
            'user_id': user_id,
            'quality': quality,
            'exp': datetime.utcnow() + timedelta(hours=2),  # 2 hours expiry
            'iat': datetime.utcnow()
        }
        
        # Generate JWT token
        token = jwt.encode(payload, self.jwt_secret, algorithm='HS256')
        
        # Create secure URL
        stream_url = f"{self.cdn_base_url}/stream/{movie_id}/{quality}?token={token}"
        
        return {
            'stream_url': stream_url,
            'expires_at': payload['exp'].isoformat(),
            'quality': quality
        }
    
    def validate_stream_token(self, token):
        """Validate streaming token"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
    
    def track_viewing_progress(self, user_id, movie_id, progress):
        """Track user's viewing progress"""
        key = f"progress:{user_id}:{movie_id}"
        data = {
            'progress': progress,
            'timestamp': time.time()
        }
        redis_client.hset(key, mapping=data)
        redis_client.expire(key, 86400 * 30)  # 30 days

streaming_service = StreamingService()

@app.route('/api/stream/<movie_id>', methods=['POST'])
def get_stream_url(movie_id):
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        quality = data.get('quality', '720p')
        
        # Validate user subscription for quality
        if quality in ['1080p', '4K'] and not check_premium_subscription(user_id):
            return jsonify({
                'success': False,
                'error': 'Premium subscription required for this quality'
            }), 403
        
        stream_data = streaming_service.generate_stream_url(movie_id, user_id, quality)
        
        return jsonify({
            'success': True,
            'data': stream_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/progress', methods=['POST'])
def update_progress():
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        movie_id = data.get('movie_id')
        progress = data.get('progress')
        
        streaming_service.track_viewing_progress(user_id, movie_id, progress)
        
        return jsonify({
            'success': True,
            'message': 'Progress updated'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def check_premium_subscription(user_id):
    """Check if user has premium subscription"""
    # This would typically call the User Service
    return True  # Placeholder

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080)
```

---

## 👤 **خدمة المستخدمين (User Service)**

### **المسؤوليات**
- إدارة ملفات المستخدمين الشخصية
- إدارة الاشتراكات والخطط
- إدارة المفضلة وقوائم التشغيل
- إدارة تاريخ المشاهدة
- إدارة الإعدادات الشخصية

### **Service Implementation**
```go
// user-service/main.go
package main

import (
    "encoding/json"
    "log"
    "net/http"
    "github.com/gorilla/mux"
    "github.com/jinzhu/gorm"
    _ "github.com/jinzhu/gorm/dialects/postgres"
)

type User struct {
    ID           uint   `json:"id" gorm:"primary_key"`
    Name         string `json:"name"`
    Email        string `json:"email"`
    Avatar       string `json:"avatar"`
    Subscription string `json:"subscription"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
}

type Favorite struct {
    ID      uint `json:"id" gorm:"primary_key"`
    UserID  uint `json:"user_id"`
    MovieID uint `json:"movie_id"`
    Type    string `json:"type"` // movie, series
}

type UserService struct {
    db *gorm.DB
}

func NewUserService() *UserService {
    db, err := gorm.Open("postgres", "host=user-db user=postgres dbname=user_service sslmode=disable")
    if err != nil {
        log.Fatal("Failed to connect to database:", err)
    }
    
    db.AutoMigrate(&User{}, &Favorite{})
    
    return &UserService{db: db}
}

func (us *UserService) GetUserProfile(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    userID := vars["id"]
    
    var user User
    if err := us.db.First(&user, userID).Error; err != nil {
        http.Error(w, "User not found", http.StatusNotFound)
        return
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data": user,
    })
}

func (us *UserService) UpdateUserProfile(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    userID := vars["id"]
    
    var user User
    if err := us.db.First(&user, userID).Error; err != nil {
        http.Error(w, "User not found", http.StatusNotFound)
        return
    }
    
    var updateData User
    if err := json.NewDecoder(r.Body).Decode(&updateData); err != nil {
        http.Error(w, "Invalid JSON", http.StatusBadRequest)
        return
    }
    
    us.db.Model(&user).Updates(updateData)
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data": user,
    })
}

func (us *UserService) AddToFavorites(w http.ResponseWriter, r *http.Request) {
    var favorite Favorite
    if err := json.NewDecoder(r.Body).Decode(&favorite); err != nil {
        http.Error(w, "Invalid JSON", http.StatusBadRequest)
        return
    }
    
    us.db.Create(&favorite)
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "message": "Added to favorites",
    })
}

func main() {
    userService := NewUserService()
    
    r := mux.NewRouter()
    r.HandleFunc("/api/users/{id}", userService.GetUserProfile).Methods("GET")
    r.HandleFunc("/api/users/{id}", userService.UpdateUserProfile).Methods("PUT")
    r.HandleFunc("/api/favorites", userService.AddToFavorites).Methods("POST")
    
    log.Println("User Service running on port 8080")
    log.Fatal(http.ListenAndServe(":8080", r))
}
```

---

## 📊 **خدمة التحليلات (Analytics Service)**

### **المسؤوليات**
- تجميع إحصائيات المشاهدة
- تحليل سلوك المستخدمين
- إنشاء التقارير والرسوم البيانية
- تتبع الأداء والاستخدام
- التنبؤ بالاتجاهات

### **Service Implementation**
```java
// analytics-service/src/main/java/AnalyticsService.java
@RestController
@RequestMapping("/api/analytics")
public class AnalyticsService {
    
    @Autowired
    private AnalyticsRepository analyticsRepository;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @PostMapping("/track")
    public ResponseEntity<?> trackEvent(@RequestBody TrackingEvent event) {
        try {
            // Store in database
            analyticsRepository.save(event);
            
            // Update real-time counters in Redis
            String key = "analytics:" + event.getType() + ":" + LocalDate.now();
            redisTemplate.opsForHash().increment(key, event.getAction(), 1);
            
            return ResponseEntity.ok(Map.of("success", true));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                .body(Map.of("success", false, "error", e.getMessage()));
        }
    }
    
    @GetMapping("/dashboard")
    public ResponseEntity<?> getDashboardData() {
        try {
            Map<String, Object> dashboard = new HashMap<>();
            
            // Get today's stats
            String today = LocalDate.now().toString();
            dashboard.put("todayViews", getTodayViews());
            dashboard.put("activeUsers", getActiveUsers());
            dashboard.put("popularContent", getPopularContent());
            dashboard.put("revenueStats", getRevenueStats());
            
            return ResponseEntity.ok(Map.of("success", true, "data", dashboard));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                .body(Map.of("success", false, "error", e.getMessage()));
        }
    }
    
    @GetMapping("/reports/{type}")
    public ResponseEntity<?> generateReport(@PathVariable String type,
                                          @RequestParam String startDate,
                                          @RequestParam String endDate) {
        try {
            ReportGenerator generator = new ReportGenerator();
            Report report = generator.generate(type, startDate, endDate);
            
            return ResponseEntity.ok(Map.of("success", true, "data", report));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                .body(Map.of("success", false, "error", e.getMessage()));
        }
    }
    
    private long getTodayViews() {
        String key = "analytics:view:" + LocalDate.now();
        return redisTemplate.opsForHash().size(key);
    }
    
    private long getActiveUsers() {
        String key = "active_users:" + LocalDate.now();
        return redisTemplate.opsForSet().size(key);
    }
}
```

---

## 🔔 **خدمة الإشعارات (Notification Service)**

### **المسؤوليات**
- إرسال الإشعارات الفورية
- إدارة قوالب الإشعارات
- إرسال البريد الإلكتروني
- إرسال الرسائل النصية
- إشعارات التطبيق الجوال

### **Service Implementation**
```php
<?php
// notification-service/src/NotificationService.php

class NotificationService
{
    private $redis;
    private $emailService;
    private $pushService;
    private $smsService;
    
    public function __construct()
    {
        $this->redis = new Redis();
        $this->redis->connect('redis', 6379);
        $this->emailService = new EmailService();
        $this->pushService = new PushNotificationService();
        $this->smsService = new SMSService();
    }
    
    public function sendNotification($userId, $type, $data)
    {
        $notification = [
            'id' => uniqid(),
            'user_id' => $userId,
            'type' => $type,
            'data' => $data,
            'created_at' => date('Y-m-d H:i:s'),
            'read' => false
        ];
        
        // Store notification
        $this->storeNotification($notification);
        
        // Get user preferences
        $preferences = $this->getUserPreferences($userId);
        
        // Send via preferred channels
        if ($preferences['email']) {
            $this->emailService->send($userId, $type, $data);
        }
        
        if ($preferences['push']) {
            $this->pushService->send($userId, $type, $data);
        }
        
        if ($preferences['sms'] && $type === 'urgent') {
            $this->smsService->send($userId, $type, $data);
        }
        
        return $notification;
    }
    
    public function getNotifications($userId, $limit = 20, $offset = 0)
    {
        $key = "notifications:user:{$userId}";
        $notifications = $this->redis->lRange($key, $offset, $offset + $limit - 1);
        
        return array_map(function($notification) {
            return json_decode($notification, true);
        }, $notifications);
    }
    
    public function markAsRead($userId, $notificationId)
    {
        $key = "notifications:user:{$userId}";
        $notifications = $this->redis->lRange($key, 0, -1);
        
        foreach ($notifications as $index => $notification) {
            $data = json_decode($notification, true);
            if ($data['id'] === $notificationId) {
                $data['read'] = true;
                $this->redis->lSet($key, $index, json_encode($data));
                break;
            }
        }
    }
    
    private function storeNotification($notification)
    {
        $key = "notifications:user:{$notification['user_id']}";
        $this->redis->lPush($key, json_encode($notification));
        $this->redis->lTrim($key, 0, 99); // Keep only last 100 notifications
    }
    
    private function getUserPreferences($userId)
    {
        $key = "user_preferences:{$userId}";
        $preferences = $this->redis->hGetAll($key);
        
        return [
            'email' => $preferences['email'] ?? true,
            'push' => $preferences['push'] ?? true,
            'sms' => $preferences['sms'] ?? false
        ];
    }
}
```

---

## 🌐 **API Gateway Configuration**

### **Kong Configuration**
```yaml
# kong.yml
_format_version: "2.1"

services:
- name: auth-service
  url: http://auth-service:8080
  routes:
  - name: auth-routes
    paths:
    - /api/auth

- name: content-service
  url: http://content-service:8080
  routes:
  - name: content-routes
    paths:
    - /api/movies
    - /api/series

- name: streaming-service
  url: http://streaming-service:8080
  routes:
  - name: streaming-routes
    paths:
    - /api/stream

- name: user-service
  url: http://user-service:8080
  routes:
  - name: user-routes
    paths:
    - /api/users

plugins:
- name: rate-limiting
  config:
    minute: 100
    hour: 1000

- name: cors
  config:
    origins:
    - "*"
    methods:
    - GET
    - POST
    - PUT
    - DELETE
    headers:
    - Accept
    - Authorization
    - Content-Type
```

---

## 📈 **مراقبة الخدمات المصغرة**

### **Prometheus Configuration**
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
- job_name: 'auth-service'
  static_configs:
  - targets: ['auth-service:8080']

- job_name: 'content-service'
  static_configs:
  - targets: ['content-service:8080']

- job_name: 'streaming-service'
  static_configs:
  - targets: ['streaming-service:8080']

- job_name: 'user-service'
  static_configs:
  - targets: ['user-service:8080']

- job_name: 'analytics-service'
  static_configs:
  - targets: ['analytics-service:8080']

- job_name: 'notification-service'
  static_configs:
  - targets: ['notification-service:8080']
```

### **Service Health Checks**
```bash
#!/bin/bash
# health-check-services.sh

services=("auth-service" "content-service" "streaming-service" "user-service" "analytics-service" "notification-service")

for service in "${services[@]}"; do
    echo "Checking $service..."
    response=$(curl -s -o /dev/null -w "%{http_code}" http://$service:8080/health)
    
    if [ $response -eq 200 ]; then
        echo "✅ $service is healthy"
    else
        echo "❌ $service is unhealthy (HTTP $response)"
    fi
done
```

---

## 🔄 **التواصل بين الخدمات**

### **Event-Driven Architecture**
```javascript
// event-bus/eventBus.js
const EventEmitter = require('events');
const redis = require('redis');

class EventBus extends EventEmitter {
    constructor() {
        super();
        this.publisher = redis.createClient(process.env.REDIS_URL);
        this.subscriber = redis.createClient(process.env.REDIS_URL);
        
        this.subscriber.on('message', (channel, message) => {
            const event = JSON.parse(message);
            this.emit(event.type, event.data);
        });
    }
    
    publish(eventType, data) {
        const event = {
            type: eventType,
            data: data,
            timestamp: new Date().toISOString(),
            id: require('uuid').v4()
        };
        
        this.publisher.publish('events', JSON.stringify(event));
    }
    
    subscribe(eventType, handler) {
        this.on(eventType, handler);
        this.subscriber.subscribe('events');
    }
}

module.exports = new EventBus();
```

---

**🔧 هندسة خدمات مصغرة شاملة ومتطورة لمنصة البث العربية**

آخر تحديث: 15 يناير 2024
