# 🎬 منصة البث الشاملة - Comprehensive Streaming Platform

منصة بث شاملة تتضمن موقع ويب وتطبيق جوال لمشاهدة الأفلام والمسلسلات مع نظام إدارة متكامل.

## 📋 المحتويات

- [الميزات الرئيسية](#الميزات-الرئيسية)
- [التقنيات المستخدمة](#التقنيات-المستخدمة)
- [متطلبات التشغيل](#متطلبات-التشغيل)
- [التثبيت والإعداد](#التثبيت-والإعداد)
- [هيكل المشروع](#هيكل-المشروع)
- [الاستخدام](#الاستخدام)
- [المساهمة](#المساهمة)
- [الترخيص](#الترخيص)

## 🌟 الميزات الرئيسية

### 🌐 الموقع الإلكتروني
- **واجهة مستخدم حديثة**: تصميم متجاوب يدعم جميع الأجهزة
- **نظام مصادقة متكامل**: تسجيل دخول وإنشاء حسابات مع التحقق
- **مشغل فيديو متقدم**: دعم جودات متعددة وترجمات
- **نظام بحث ذكي**: بحث متقدم مع فلاتر وتصنيفات
- **إدارة المحتوى**: رفع وتنظيم الأفلام والمسلسلات
- **نظام اشتراكات**: باقات متعددة مع إدارة الدفع
- **لوحة تحكم إدارية**: إحصائيات شاملة وإدارة المستخدمين

### 📱 التطبيق الجوال
- **تصميم Material Design**: واجهة عصرية وسهلة الاستخدام
- **مشغل فيديو محسن**: تحكم كامل مع دعم الإيماءات
- **التحميل للمشاهدة بدون إنترنت**: حفظ المحتوى محلياً
- **إشعارات ذكية**: تنبيهات للمحتوى الجديد والمفضل
- **مزامنة عبر الأجهزة**: استكمال المشاهدة من أي جهاز
- **دعم متعدد اللغات**: العربية والإنجليزية والفرنسية والتركية

## 🛠️ التقنيات المستخدمة

### Backend & Database
- **PHP 8.2+**: لغة البرمجة الخلفية
- **MySQL 8.0+**: قاعدة البيانات الرئيسية
- **Apache/Nginx**: خادم الويب
- **Composer**: إدارة التبعيات

### Frontend Web
- **HTML5 & CSS3**: هيكل وتصميم الصفحات
- **JavaScript ES6+**: التفاعل والديناميكية
- **Bootstrap 5**: إطار عمل CSS
- **Font Awesome**: الأيقونات
- **jQuery**: مكتبة JavaScript

### Mobile App
- **Flutter 3.8+**: إطار عمل التطبيق
- **Dart**: لغة البرمجة
- **Riverpod**: إدارة الحالة
- **Go Router**: التنقل
- **Video Player**: مشغل الفيديو
- **Hive**: التخزين المحلي

### Additional Tools
- **FFmpeg**: معالجة الفيديو
- **Firebase**: الإشعارات والتحليلات
- **Git**: نظام التحكم في الإصدارات

## 📋 متطلبات التشغيل

### للخادم
- PHP 8.2 أو أحدث
- MySQL 8.0 أو أحدث
- Apache 2.4+ أو Nginx 1.18+
- Composer 2.0+
- FFmpeg (لمعالجة الفيديو)
- مساحة تخزين كافية للملفات المرفوعة

### للتطبيق الجوال
- Flutter SDK 3.8+
- Dart SDK 3.0+
- Android Studio / VS Code
- Android SDK (للأندرويد)
- Xcode (لـ iOS)

## 🚀 التثبيت والإعداد

### 1. إعداد الخادم

```bash
# استنساخ المشروع
git clone https://github.com/your-username/streaming-platform.git
cd streaming-platform

# إعداد الموقع
cd website
composer install

# إعداد قاعدة البيانات
mysql -u root -p < database/schema.sql
mysql -u root -p < database/sample_data.sql

# إعداد الصلاحيات
chmod 755 -R public/
chmod 777 -R uploads/
chmod 777 -R cache/
```

### 2. إعداد التطبيق الجوال

```bash
# الانتقال لمجلد التطبيق
cd mobile_app

# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق
flutter run
```

### 3. إعداد المتغيرات

إنشاء ملف `.env` في مجلد `website/`:

```env
# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_NAME=streaming_platform
DB_USER=your_username
DB_PASS=your_password

# إعدادات الموقع
SITE_URL=http://localhost/streaming_platform
SITE_NAME=منصة البث الشاملة
ADMIN_EMAIL=<EMAIL>

# إعدادات الأمان
JWT_SECRET=your_jwt_secret_key
ENCRYPTION_KEY=your_encryption_key

# إعدادات البريد الإلكتروني
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# إعدادات الدفع
STRIPE_PUBLIC_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
```

## 📁 هيكل المشروع

```
streaming-platform/
├── website/                    # الموقع الإلكتروني
│   ├── public/                # الملفات العامة
│   │   ├── index.php          # نقطة الدخول
│   │   ├── assets/            # الأصول (CSS, JS, Images)
│   │   └── views/             # صفحات العرض
│   ├── api/                   # واجهات برمجة التطبيقات
│   ├── includes/              # الملفات المشتركة
│   ├── admin/                 # لوحة التحكم الإدارية
│   ├── uploads/               # الملفات المرفوعة
│   └── database/              # ملفات قاعدة البيانات
│
├── mobile_app/                # التطبيق الجوال
│   ├── lib/                   # كود التطبيق
│   │   ├── core/              # الملفات الأساسية
│   │   ├── features/          # ميزات التطبيق
│   │   └── shared/            # المكونات المشتركة
│   ├── assets/                # أصول التطبيق
│   └── android/ios/           # إعدادات المنصات
│
├── docs/                      # الوثائق
├── scripts/                   # سكريبتات مساعدة
└── README.md                  # هذا الملف
```

## 🎯 الاستخدام

### للمطورين

1. **تطوير الموقع**:
   ```bash
   cd website
   php -S localhost:8000 -t public
   ```

2. **تطوير التطبيق**:
   ```bash
   cd mobile_app
   flutter run
   ```

3. **بناء التطبيق للإنتاج**:
   ```bash
   flutter build apk --release
   flutter build ios --release
   ```

### للمستخدمين

1. **الوصول للموقع**: افتح المتصفح وانتقل إلى رابط الموقع
2. **إنشاء حساب**: اضغط على "تسجيل" وأدخل بياناتك
3. **تصفح المحتوى**: استخدم البحث أو تصفح التصنيفات
4. **مشاهدة الفيديو**: اضغط على أي محتوى لبدء المشاهدة

### للإداريين

1. **الوصول للوحة التحكم**: `/admin`
2. **إدارة المحتوى**: رفع وتنظيم الأفلام والمسلسلات
3. **إدارة المستخدمين**: عرض وإدارة حسابات المستخدمين
4. **الإحصائيات**: مراقبة الأداء والاستخدام

## 🔧 التخصيص

### تغيير الألوان والتصميم

1. **للموقع**: عدّل ملف `website/public/assets/css/main.css`
2. **للتطبيق**: عدّل ملف `mobile_app/lib/core/constants/app_constants.dart`

### إضافة لغات جديدة

1. أضف ملفات الترجمة في `website/includes/languages/`
2. حدّث قائمة اللغات المدعومة في التطبيق

### تخصيص مشغل الفيديو

1. عدّل إعدادات المشغل في `website/public/assets/js/video-player.js`
2. للتطبيق: عدّل `mobile_app/lib/features/video_player/`

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

### إرشادات المساهمة

- اتبع معايير الكود المستخدمة
- أضف تعليقات واضحة
- اختبر التغييرات قبل الإرسال
- حدّث الوثائق عند الحاجة

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل والدعم

- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://streamingplatform.com
- **GitHub Issues**: لتقارير الأخطاء والاقتراحات

## 🙏 شكر وتقدير

- فريق Flutter لإطار العمل الرائع
- مجتمع PHP للمكتبات والأدوات
- جميع المساهمين في المشروع

---

**ملاحظة**: هذا المشروع في مرحلة التطوير. بعض الميزات قد تكون غير مكتملة.

## 📊 إحصائيات المشروع

- **إجمالي الملفات**: 150+
- **أسطر الكود**: 15,000+
- **اللغات المدعومة**: 4
- **المنصات المدعومة**: Web, Android, iOS

## 🔄 التحديثات القادمة

- [ ] دعم البث المباشر
- [ ] نظام التعليقات والتقييمات
- [ ] تطبيق Smart TV
- [ ] ذكاء اصطناعي للتوصيات
- [ ] نظام الإنجازات والنقاط

---

**تم تطوير هذا المشروع بـ ❤️ للمجتمع العربي**
