# 🔒 ملف .htaccess الرئيسي لمنصة البث - حماية المجلد الجذر
# حماية ملفات الإعدادات والمجلدات الحساسة

# منع الوصول للملفات الحساسة
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|sql|conf)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول لمجلدات النظام
RedirectMatch 403 ^.*/\.(git|svn|hg)/.*$
RedirectMatch 403 ^.*/includes/.*$
RedirectMatch 403 ^.*/vendor/.*$
RedirectMatch 403 ^.*/logs/.*$
RedirectMatch 403 ^.*/cache/.*$
RedirectMatch 403 ^.*/backups/.*$

# منع عرض محتويات المجلدات
Options -Indexes

# إعادة توجيه للمجلد العام
RewriteEngine On
RewriteCond %{REQUEST_URI} !^/public/
RewriteRule ^(.*)$ public/$1 [L]

# حماية إضافية
<Files "composer.json">
    Order Allow,Deny
    Deny from all
</Files>

<Files "composer.lock">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.md">
    Order Allow,Deny
    Deny from all
</Files>
