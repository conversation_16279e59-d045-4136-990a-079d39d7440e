# 🗄️ إعدادات MySQL محسنة لمنصة البث العربية

[mysqld]
# الإعدادات الأساسية
default-authentication-plugin = mysql_native_password
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init-connect = 'SET NAMES utf8mb4'

# إعدادات الأداء
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# إعدادات الاتصالات
max_connections = 200
max_connect_errors = 1000
connect_timeout = 60
wait_timeout = 600
interactive_timeout = 600

# إعدادات الاستعلامات
query_cache_type = 1
query_cache_size = 32M
query_cache_limit = 2M

# إعدادات الجداول المؤقتة
tmp_table_size = 32M
max_heap_table_size = 32M

# إعدادات الفرز والتجميع
sort_buffer_size = 2M
read_buffer_size = 128K
read_rnd_buffer_size = 256K
join_buffer_size = 128K

# إعدادات MyISAM
key_buffer_size = 32M
table_open_cache = 400

# إعدادات السجلات
log_error = /var/log/mysql/error.log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# إعدادات الأمان
local_infile = 0
skip_show_database

# إعدادات البيانات الثنائية
log_bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# إعدادات InnoDB
innodb_file_per_table = 1
innodb_open_files = 400
innodb_io_capacity = 400
innodb_read_io_threads = 4
innodb_write_io_threads = 4

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
