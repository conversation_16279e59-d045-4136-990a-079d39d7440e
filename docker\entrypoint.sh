#!/bin/bash
# 🚀 نقطة دخول Docker لمنصة البث العربية

set -e

echo "🚀 بدء تشغيل منصة البث العربية..."

# انتظار قاعدة البيانات
echo "⏳ انتظار قاعدة البيانات..."
while ! mysqladmin ping -h"$DB_HOST" --silent; do
    sleep 1
done
echo "✅ قاعدة البيانات جاهزة"

# إنشاء ملف الإعدادات إذا لم يكن موجوداً
if [ ! -f "/var/www/html/includes/config.php" ]; then
    echo "📝 إنشاء ملف الإعدادات..."
    cp /var/www/html/includes/config.example.php /var/www/html/includes/config.php
    
    # تحديث إعدادات قاعدة البيانات
    sed -i "s/localhost/$DB_HOST/g" /var/www/html/includes/config.php
    sed -i "s/streaming_platform/$DB_NAME/g" /var/www/html/includes/config.php
    sed -i "s/root/$DB_USER/g" /var/www/html/includes/config.php
    sed -i "s/''/'$DB_PASS'/g" /var/www/html/includes/config.php
fi

# إنشاء المجلدات المطلوبة
echo "📁 إنشاء المجلدات..."
mkdir -p /var/www/html/logs
mkdir -p /var/www/html/cache
mkdir -p /var/www/html/public/uploads/posters
mkdir -p /var/www/html/public/uploads/thumbnails
mkdir -p /var/www/html/public/uploads/videos

# تعيين الصلاحيات
echo "🔐 تعيين الصلاحيات..."
chown -R www-data:www-data /var/www/html
chmod -R 755 /var/www/html
chmod -R 777 /var/www/html/logs
chmod -R 777 /var/www/html/cache
chmod -R 777 /var/www/html/public/uploads

# تشغيل Composer إذا لزم الأمر
if [ -f "/var/www/html/composer.json" ] && [ ! -d "/var/www/html/vendor" ]; then
    echo "📦 تثبيت تبعيات Composer..."
    cd /var/www/html && composer install --no-dev --optimize-autoloader
fi

# تنظيف التخزين المؤقت
echo "🧹 تنظيف التخزين المؤقت..."
rm -rf /var/www/html/cache/*

# إنشاء ملف .htaccess إذا لم يكن موجوداً
if [ ! -f "/var/www/html/public/.htaccess" ]; then
    echo "⚙️ إنشاء ملف .htaccess..."
    cp /var/www/html/public/.htaccess.example /var/www/html/public/.htaccess 2>/dev/null || true
fi

echo "✅ تم إعداد منصة البث بنجاح"
echo "🌐 الموقع متاح على: http://localhost"
echo "🎛️ لوحة التحكم: http://localhost/admin"
echo "🧪 صفحة الاختبار: http://localhost/test.php"

# تشغيل Apache
exec "$@"
