# 🌐 إعدادات Apache VirtualHost لمنصة البث العربية

<VirtualHost *:80>
    ServerName localhost
    DocumentRoot /var/www/html/public
    
    # إعدادات المجلد
    <Directory /var/www/html/public>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
        
        # إعدادات PHP
        php_admin_value upload_max_filesize 100M
        php_admin_value post_max_size 100M
        php_admin_value memory_limit 256M
        php_admin_value max_execution_time 300
    </Directory>
    
    # حماية المجلدات الحساسة
    <Directory /var/www/html/includes>
        Require all denied
    </Directory>
    
    <Directory /var/www/html/vendor>
        Require all denied
    </Directory>
    
    <Directory /var/www/html/logs>
        Require all denied
    </Directory>
    
    <Directory /var/www/html/cache>
        Require all denied
    </Directory>
    
    # إعدادات التخزين المؤقت للأصول
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
        Header append Cache-Control "public"
    </LocationMatch>
    
    # إعدادات الفيديو
    <LocationMatch "\.(mp4|webm|ogg|avi|mov)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 week"
        Header append Cache-Control "public"
        Header append Accept-Ranges "bytes"
    </LocationMatch>
    
    # إعدادات الضغط
    <IfModule mod_deflate.c>
        AddOutputFilterByType DEFLATE text/plain
        AddOutputFilterByType DEFLATE text/html
        AddOutputFilterByType DEFLATE text/xml
        AddOutputFilterByType DEFLATE text/css
        AddOutputFilterByType DEFLATE application/xml
        AddOutputFilterByType DEFLATE application/xhtml+xml
        AddOutputFilterByType DEFLATE application/rss+xml
        AddOutputFilterByType DEFLATE application/javascript
        AddOutputFilterByType DEFLATE application/x-javascript
        AddOutputFilterByType DEFLATE application/json
    </IfModule>
    
    # إعدادات الأمان
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
        Header always set Referrer-Policy "strict-origin-when-cross-origin"
        Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    </IfModule>
    
    # إعدادات السجلات
    ErrorLog /var/log/apache2/streaming-platform-error.log
    CustomLog /var/log/apache2/streaming-platform-access.log combined
    
    # إعدادات إضافية
    ServerSignature Off
    TraceEnable Off
</VirtualHost>

# إعدادات HTTPS (إذا كان SSL متاحاً)
<IfModule mod_ssl.c>
<VirtualHost *:443>
    ServerName localhost
    DocumentRoot /var/www/html/public
    
    # إعدادات SSL
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/ssl-cert-snakeoil.pem
    SSLCertificateKeyFile /etc/ssl/private/ssl-cert-snakeoil.key
    
    # نفس إعدادات HTTP
    <Directory /var/www/html/public>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    # إعدادات أمان إضافية لـ HTTPS
    <IfModule mod_headers.c>
        Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    ErrorLog /var/log/apache2/streaming-platform-ssl-error.log
    CustomLog /var/log/apache2/streaming-platform-ssl-access.log combined
</VirtualHost>
</IfModule>
