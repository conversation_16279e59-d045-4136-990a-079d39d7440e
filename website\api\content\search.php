<?php
/**
 * 🔍 API البحث في المحتوى
 * يتعامل مع طلبات البحث والتصفية
 */

// تعريف الثابت للسماح بالوصول
define('STREAMING_PLATFORM', true);

// تحميل الملفات المطلوبة
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';

// تعيين رأس JSON
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// التعامل مع طلبات OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// التحقق من طريقة الطلب
if (!in_array($_SERVER['REQUEST_METHOD'], ['GET', 'POST'])) {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'طريقة الطلب غير مدعومة'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // الحصول على المعاملات
    $params = $_SERVER['REQUEST_METHOD'] === 'POST' 
        ? json_decode(file_get_contents('php://input'), true) 
        : $_GET;
    
    // معاملات البحث
    $query = sanitizeInput($params['q'] ?? '');
    $type = sanitizeInput($params['type'] ?? '');
    $genre = sanitizeInput($params['genre'] ?? '');
    $year = (int)($params['year'] ?? 0);
    $rating = (float)($params['rating'] ?? 0);
    $sortBy = sanitizeInput($params['sort_by'] ?? 'relevance');
    $sortOrder = sanitizeInput($params['sort_order'] ?? 'desc');
    $page = max(1, (int)($params['page'] ?? 1));
    $limit = min(50, max(1, (int)($params['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    // التحقق من صحة معاملات الترتيب
    $allowedSortFields = ['relevance', 'title', 'release_date', 'our_rating', 'view_count', 'created_at'];
    $allowedSortOrders = ['asc', 'desc'];
    
    if (!in_array($sortBy, $allowedSortFields)) {
        $sortBy = 'relevance';
    }
    
    if (!in_array($sortOrder, $allowedSortOrders)) {
        $sortOrder = 'desc';
    }
    
    // بناء الاستعلام
    global $db;
    
    $sql = "SELECT 
                c.*,
                CASE 
                    WHEN c.title LIKE ? THEN 3
                    WHEN c.description LIKE ? THEN 2
                    WHEN c.tags LIKE ? THEN 1
                    ELSE 0
                END as relevance_score
            FROM " . DB_PREFIX . "content c
            WHERE c.status = 'published'";
    
    $searchParams = [];
    
    // إضافة شروط البحث
    if (!empty($query)) {
        $searchTerm = "%{$query}%";
        $searchParams = [$searchTerm, $searchTerm, $searchTerm];
        $sql .= " AND (c.title LIKE ? OR c.description LIKE ? OR c.tags LIKE ?)";
        $searchParams = array_merge($searchParams, [$searchTerm, $searchTerm, $searchTerm]);
    } else {
        $searchParams = ['', '', ''];
    }
    
    // تصفية حسب النوع
    if (!empty($type)) {
        $sql .= " AND c.type = ?";
        $searchParams[] = $type;
    }
    
    // تصفية حسب النوع الفرعي
    if (!empty($genre)) {
        $sql .= " AND c.genre LIKE ?";
        $searchParams[] = "%{$genre}%";
    }
    
    // تصفية حسب السنة
    if ($year > 0) {
        $sql .= " AND YEAR(c.release_date) = ?";
        $searchParams[] = $year;
    }
    
    // تصفية حسب التقييم
    if ($rating > 0) {
        $sql .= " AND c.our_rating >= ?";
        $searchParams[] = $rating;
    }
    
    // إضافة الترتيب
    if ($sortBy === 'relevance' && !empty($query)) {
        $sql .= " ORDER BY relevance_score DESC, c.our_rating DESC";
    } else {
        $sortField = $sortBy === 'relevance' ? 'c.created_at' : "c.{$sortBy}";
        $sql .= " ORDER BY {$sortField} {$sortOrder}";
    }
    
    // عد النتائج الإجمالية
    $countSql = str_replace(
        "SELECT c.*, CASE WHEN c.title LIKE ? THEN 3 WHEN c.description LIKE ? THEN 2 WHEN c.tags LIKE ? THEN 1 ELSE 0 END as relevance_score FROM",
        "SELECT COUNT(*) as total FROM",
        $sql
    );
    $countSql = preg_replace('/ORDER BY.*$/', '', $countSql);
    
    $totalResult = $db->selectOne($countSql, $searchParams);
    $total = $totalResult['total'] ?? 0;
    
    // إضافة الحد والإزاحة
    $sql .= " LIMIT ? OFFSET ?";
    $searchParams[] = $limit;
    $searchParams[] = $offset;
    
    // تنفيذ الاستعلام
    $results = $db->select($sql, $searchParams);
    
    // معالجة النتائج
    $processedResults = [];
    foreach ($results as $content) {
        $processedResults[] = [
            'id' => (int)$content['id'],
            'title' => $content['title'],
            'description' => $content['description'],
            'type' => $content['type'],
            'genre' => $content['genre'],
            'release_date' => $content['release_date'],
            'duration' => (int)$content['duration'],
            'our_rating' => (float)$content['our_rating'],
            'imdb_rating' => (float)$content['imdb_rating'],
            'view_count' => (int)$content['view_count'],
            'poster' => $content['poster'],
            'banner' => $content['banner'],
            'trailer_url' => $content['trailer_url'],
            'subscription_required' => $content['subscription_required'],
            'is_featured' => (bool)$content['is_featured'],
            'is_trending' => (bool)$content['is_trending'],
            'language' => $content['language'],
            'country' => $content['country'],
            'age_rating' => $content['age_rating'],
            'tags' => $content['tags'] ? explode(',', $content['tags']) : [],
            'created_at' => $content['created_at'],
            'updated_at' => $content['updated_at']
        ];
    }
    
    // حساب معلومات الصفحات
    $totalPages = ceil($total / $limit);
    $hasNextPage = $page < $totalPages;
    $hasPrevPage = $page > 1;
    
    // تسجيل البحث إذا كان هناك استعلام
    if (!empty($query)) {
        trackEvent('content_search', [
            'query' => $query,
            'type' => $type,
            'genre' => $genre,
            'year' => $year,
            'rating' => $rating,
            'results_count' => count($processedResults),
            'total_results' => $total,
            'page' => $page
        ]);
        
        // حفظ في سجل البحث للمستخدم المسجل
        if (isLoggedIn()) {
            logUserActivity('search', [
                'query' => $query,
                'type' => $type,
                'results_count' => count($processedResults)
            ]);
        }
    }
    
    // إرجاع النتائج
    echo json_encode([
        'success' => true,
        'data' => $processedResults,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_results' => $total,
            'per_page' => $limit,
            'has_next_page' => $hasNextPage,
            'has_prev_page' => $hasPrevPage
        ],
        'filters' => [
            'query' => $query,
            'type' => $type,
            'genre' => $genre,
            'year' => $year,
            'rating' => $rating,
            'sort_by' => $sortBy,
            'sort_order' => $sortOrder
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // تسجيل الخطأ
    logError('Search API Error: ' . $e->getMessage(), [
        'file' => __FILE__,
        'line' => $e->getLine(),
        'params' => $params ?? null,
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);
    
    // إرجاع رسالة خطأ
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ أثناء البحث'
    ], JSON_UNESCAPED_UNICODE);
}
?>
