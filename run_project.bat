@echo off
echo ========================================
echo 🚀 منصة البث الشاملة - تشغيل المشروع
echo ========================================
echo.

:: التحقق من وجود Flutter
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Flutter من: https://flutter.dev/docs/get-started/install
    pause
    exit /b 1
)

:: التحقق من وجود XAMPP أو خادم ويب
echo 🔍 التحقق من الخادم المحلي...
curl -s http://localhost/streaming_platform >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  الخادم المحلي غير متاح
    echo يرجى تشغيل XAMPP أو خادم ويب آخر
    echo.
    echo 📝 خطوات تشغيل الخادم:
    echo 1. تشغيل XAMPP Control Panel
    echo 2. تشغيل Apache و MySQL
    echo 3. نسخ مجلد website إلى htdocs
    echo 4. إنشاء قاعدة البيانات من ملف schema.sql
    echo.
    set /p choice="هل تريد المتابعة بدون الخادم؟ (y/n): "
    if /i "%choice%" neq "y" (
        pause
        exit /b 1
    )
)

echo.
echo 📱 تشغيل التطبيق الجوال...
echo ========================================

:: الانتقال لمجلد التطبيق
cd mobile_app

:: تثبيت التبعيات
echo 📦 تثبيت التبعيات...
flutter pub get

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت التبعيات
    pause
    exit /b 1
)

:: تنظيف المشروع
echo 🧹 تنظيف المشروع...
flutter clean
flutter pub get

:: التحقق من الأجهزة المتاحة
echo 📱 البحث عن الأجهزة المتاحة...
flutter devices

echo.
echo 🚀 تشغيل التطبيق...
echo ========================================

:: تشغيل التطبيق
flutter run

echo.
echo ✅ انتهى تشغيل التطبيق
pause
