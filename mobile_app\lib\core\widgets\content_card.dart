import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:go_router/go_router.dart';

import '../constants/app_constants.dart';
import '../models/content_model.dart';

/// 🎬 بطاقة المحتوى
/// ويدجت قابل لإعادة الاستخدام لعرض الأفلام والمسلسلات
class ContentCard extends StatelessWidget {
  final ContentModel content;
  final bool showProgress;
  final bool showDetails;
  final VoidCallback? onTap;
  final VoidCallback? onFavoriteToggle;
  final VoidCallback? onWatchlistToggle;
  final ContentCardSize size;

  const ContentCard({
    super.key,
    required this.content,
    this.showProgress = false,
    this.showDetails = true,
    this.onTap,
    this.onFavoriteToggle,
    this.onWatchlistToggle,
    this.size = ContentCardSize.normal,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ?? () => context.push('/content/${content.id}'),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.radiusMD),
          boxShadow: AppConstants.cardShadow,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Poster
            Expanded(
              child: _buildPoster(context),
            ),
            
            // Content Info
            if (showDetails) ...[
              const SizedBox(height: AppConstants.spacingSM),
              _buildContentInfo(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPoster(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.radiusMD),
        color: AppConstants.backgroundSecondary,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.radiusMD),
        child: Stack(
          children: [
            // Main Image
            Positioned.fill(
              child: CachedNetworkImage(
                imageUrl: content.poster ?? '',
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppConstants.backgroundSecondary,
                  child: const Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppConstants.primaryColor,
                      ),
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: AppConstants.backgroundSecondary,
                  child: const Icon(
                    Icons.image_not_supported,
                    color: AppConstants.textMuted,
                    size: 40,
                  ),
                ),
              ),
            ),
            
            // Overlay
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                    stops: const [0.6, 1.0],
                  ),
                ),
              ),
            ),
            
            // Badges
            Positioned(
              top: AppConstants.spacingSM,
              right: AppConstants.spacingSM,
              child: _buildBadges(),
            ),
            
            // Quality Badge
            if (content.videoQuality != null)
              Positioned(
                top: AppConstants.spacingSM,
                left: AppConstants.spacingSM,
                child: _buildQualityBadge(),
              ),
            
            // Play Button
            Center(
              child: _buildPlayButton(context),
            ),
            
            // Quick Actions
            Positioned(
              bottom: AppConstants.spacingSM,
              right: AppConstants.spacingSM,
              child: _buildQuickActions(context),
            ),
            
            // Progress Bar
            if (showProgress && content.watchProgress != null)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: _buildProgressBar(),
              ),
            
            // Duration
            if (content.duration != null)
              Positioned(
                bottom: AppConstants.spacingSM,
                left: AppConstants.spacingSM,
                child: _buildDurationBadge(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBadges() {
    final badges = <Widget>[];
    
    if (content.isNew) {
      badges.add(_buildBadge('جديد', AppConstants.successColor));
    }
    
    if (content.isFeatured) {
      badges.add(_buildBadge('مميز', AppConstants.warningColor));
    }
    
    if (content.isTrending) {
      badges.add(_buildBadge('رائج', AppConstants.errorColor));
    }
    
    if (content.subscriptionRequired != SubscriptionType.free) {
      badges.add(_buildBadge(
        content.subscriptionRequired.name.toUpperCase(),
        AppConstants.primaryColor,
        icon: Icons.crown,
      ));
    }
    
    if (badges.isEmpty) return const SizedBox.shrink();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: badges.map((badge) => Padding(
        padding: const EdgeInsets.only(bottom: AppConstants.spacingXS),
        child: badge,
      )).toList(),
    );
  }

  Widget _buildBadge(String text, Color color, {IconData? icon}) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.spacingSM,
        vertical: AppConstants.spacingXS,
      ),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(AppConstants.radiusSM),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: 12,
              color: Colors.white,
            ),
            const SizedBox(width: 2),
          ],
          Text(
            text,
            style: const TextStyle(
              color: Colors.white,
              fontSize: AppConstants.fontSizeXS,
              fontWeight: AppConstants.fontWeightBold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQualityBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.spacingSM,
        vertical: AppConstants.spacingXS,
      ),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(AppConstants.radiusSM),
      ),
      child: Text(
        content.videoQuality!,
        style: const TextStyle(
          color: Colors.white,
          fontSize: AppConstants.fontSizeXS,
          fontWeight: AppConstants.fontWeightBold,
        ),
      ),
    );
  }

  Widget _buildPlayButton(BuildContext context) {
    final bool canAccess = content.subscriptionRequired == SubscriptionType.free;
    
    return GestureDetector(
      onTap: () {
        if (canAccess) {
          context.push('/player/${content.id}');
        } else {
          context.push('/subscription');
        }
      },
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Icon(
          canAccess ? Icons.play_arrow : Icons.lock,
          size: 30,
          color: AppConstants.primaryColor,
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      children: [
        // Favorite Button
        _buildQuickActionButton(
          icon: content.isFavorite ? Icons.favorite : Icons.favorite_border,
          isActive: content.isFavorite,
          onTap: onFavoriteToggle,
        ),
        
        const SizedBox(height: AppConstants.spacingXS),
        
        // Watchlist Button
        _buildQuickActionButton(
          icon: content.inWatchlist ? Icons.bookmark : Icons.bookmark_border,
          isActive: content.inWatchlist,
          onTap: onWatchlistToggle,
        ),
        
        const SizedBox(height: AppConstants.spacingXS),
        
        // More Options
        _buildQuickActionButton(
          icon: Icons.more_vert,
          onTap: () => _showMoreOptions(context),
        ),
      ],
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    bool isActive = false,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: isActive 
              ? AppConstants.primaryColor 
              : Colors.black.withOpacity(0.6),
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          size: 16,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    final progress = (content.watchProgress ?? 0) / 100;
    
    return Container(
      height: 4,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.3),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppConstants.radiusMD),
          bottomRight: Radius.circular(AppConstants.radiusMD),
        ),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: progress,
        child: Container(
          decoration: const BoxDecoration(
            color: AppConstants.primaryColor,
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(AppConstants.radiusMD),
              bottomRight: Radius.circular(AppConstants.radiusMD),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDurationBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.spacingSM,
        vertical: AppConstants.spacingXS,
      ),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(AppConstants.radiusSM),
      ),
      child: Text(
        content.formattedDuration,
        style: const TextStyle(
          color: Colors.white,
          fontSize: AppConstants.fontSizeXS,
          fontWeight: AppConstants.fontWeightMedium,
        ),
      ),
    );
  }

  Widget _buildContentInfo(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.spacingXS),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            content.title,
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMD,
              fontWeight: AppConstants.fontWeightMedium,
              color: AppConstants.textPrimary,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: AppConstants.spacingXS),
          
          // Meta Info
          Row(
            children: [
              // Type
              Text(
                content.type == ContentType.movie ? 'فيلم' : 'مسلسل',
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeSM,
                  color: AppConstants.textSecondary,
                ),
              ),
              
              // Year
              if (content.releaseYear.isNotEmpty) ...[
                const Text(
                  ' • ',
                  style: TextStyle(
                    color: AppConstants.textMuted,
                  ),
                ),
                Text(
                  content.releaseYear,
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeSM,
                    color: AppConstants.textSecondary,
                  ),
                ),
              ],
            ],
          ),
          
          // Rating and Views
          if (content.ourRating != null || content.viewCount > 0) ...[
            const SizedBox(height: AppConstants.spacingXS),
            Row(
              children: [
                // Rating
                if (content.ourRating != null) ...[
                  const Icon(
                    Icons.star,
                    size: 14,
                    color: AppConstants.warningColor,
                  ),
                  const SizedBox(width: 2),
                  Text(
                    content.formattedRating,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSM,
                      color: AppConstants.textSecondary,
                    ),
                  ),
                ],
                
                // Views
                if (content.viewCount > 0) ...[
                  if (content.ourRating != null) ...[
                    const Text(
                      ' • ',
                      style: TextStyle(
                        color: AppConstants.textMuted,
                      ),
                    ),
                  ],
                  const Icon(
                    Icons.visibility,
                    size: 14,
                    color: AppConstants.textMuted,
                  ),
                  const SizedBox(width: 2),
                  Text(
                    content.formattedViewCount,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSM,
                      color: AppConstants.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ],
          
          // Progress Text
          if (showProgress && content.watchProgress != null) ...[
            const SizedBox(height: AppConstants.spacingXS),
            Text(
              '${content.progressText} مكتمل',
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSM,
                color: AppConstants.primaryColor,
                fontWeight: AppConstants.fontWeightMedium,
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppConstants.backgroundSecondary,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppConstants.radiusLG),
        ),
      ),
      builder: (context) => ContentOptionsBottomSheet(content: content),
    );
  }
}

/// 📏 أحجام بطاقة المحتوى
enum ContentCardSize {
  small,
  normal,
  large,
}

/// 🔧 ورقة خيارات المحتوى السفلية
class ContentOptionsBottomSheet extends StatelessWidget {
  final ContentModel content;

  const ContentOptionsBottomSheet({
    super.key,
    required this.content,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacingLG),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppConstants.textMuted,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          const SizedBox(height: AppConstants.spacingLG),
          
          // Content Info
          Row(
            children: [
              // Poster
              ClipRRect(
                borderRadius: BorderRadius.circular(AppConstants.radiusSM),
                child: CachedNetworkImage(
                  imageUrl: content.poster ?? '',
                  width: 60,
                  height: 90,
                  fit: BoxFit.cover,
                  errorWidget: (context, url, error) => Container(
                    width: 60,
                    height: 90,
                    color: AppConstants.backgroundTertiary,
                    child: const Icon(
                      Icons.image_not_supported,
                      color: AppConstants.textMuted,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(width: AppConstants.spacingMD),
              
              // Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      content.title,
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeLG,
                        fontWeight: AppConstants.fontWeightBold,
                        color: AppConstants.textPrimary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: AppConstants.spacingXS),
                    
                    Text(
                      '${content.type == ContentType.movie ? 'فيلم' : 'مسلسل'} • ${content.releaseYear}',
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeMD,
                        color: AppConstants.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.spacingXL),
          
          // Options
          Column(
            children: [
              _buildOption(
                icon: Icons.info_outline,
                title: 'تفاصيل أكثر',
                onTap: () {
                  Navigator.pop(context);
                  context.push('/content/${content.id}');
                },
              ),
              
              if (content.trailerUrl != null)
                _buildOption(
                  icon: Icons.play_circle_outline,
                  title: 'المقطع الدعائي',
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Open trailer
                  },
                ),
              
              _buildOption(
                icon: Icons.share_outlined,
                title: 'مشاركة',
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Share content
                },
              ),
              
              _buildOption(
                icon: Icons.download_outlined,
                title: 'تحميل',
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Download content
                },
              ),
              
              _buildOption(
                icon: Icons.report_outlined,
                title: 'إبلاغ عن مشكلة',
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Report content
                },
                isDestructive: true,
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.spacingLG),
        ],
      ),
    );
  }

  Widget _buildOption({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? AppConstants.errorColor : AppConstants.textPrimary,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? AppConstants.errorColor : AppConstants.textPrimary,
          fontWeight: AppConstants.fontWeightMedium,
        ),
      ),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }
}
