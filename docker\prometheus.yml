# 📊 إعدادات Prometheus لمراقبة منصة البث العربية

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # مراقبة Prometheus نفسه
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # مراقبة التطبيق الرئيسي
  - job_name: 'streaming-platform'
    static_configs:
      - targets: ['app:80']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # مراقبة قاعدة البيانات MySQL
  - job_name: 'mysql'
    static_configs:
      - targets: ['database:3306']
    scrape_interval: 30s

  # مراقبة Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # مراقبة Nginx
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    scrape_interval: 30s

  # مراقبة النظام
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093
