<?php
/**
 * 🎬 صفحة تفاصيل المحتوى
 * عرض تفاصيل الفيلم أو المسلسل
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}

// التحقق من وجود المحتوى
if (!isset($content) || !$content) {
    include 'views/404.php';
    return;
}

// الحصول على المحتوى ذو الصلة
$relatedContent = getRelatedContent($content['id'], $content['genre'], 6);

// الحصول على التقييمات
$ratings = getContentRatings($content['id'], 5);
$userRating = IS_LOGGED_IN ? getUserRating($content['id'], $currentUser['id']) : null;

// الحصول على التعليقات
$comments = getContentComments($content['id'], 10);

// الحصول على المواسم والحلقات (للمسلسلات)
$seasons = [];
if ($content['type'] === 'series') {
    $seasons = getContentSeasons($content['id']);
}

// التحقق من إمكانية الوصول
$canAccess = canAccessContent($content);
$isFavorite = IS_LOGGED_IN ? isInFavorites($content['id'], $currentUser['id']) : false;
$isInWatchlist = IS_LOGGED_IN ? isInWatchlist($content['id'], $currentUser['id']) : false;

// تحديث عدد المشاهدات
incrementViewCount($content['id']);
?>

<div class="content-detail">
    <!-- Hero Section -->
    <section class="content-hero" style="background-image: url('<?php echo $content['banner'] ?? $content['poster']; ?>')">
        <div class="content-hero-overlay"></div>
        <div class="container">
            <div class="content-hero-content">
                <div class="content-poster">
                    <img src="<?php echo $content['poster']; ?>" 
                         alt="<?php echo htmlspecialchars($content['title']); ?>"
                         class="poster-image">
                    
                    <?php if (!$canAccess): ?>
                    <div class="poster-overlay">
                        <i class="fas fa-lock"></i>
                        <span><?php echo ucfirst($content['subscription_required']); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="content-info">
                    <div class="content-badges">
                        <?php if ($content['is_new']): ?>
                        <span class="badge badge-new">جديد</span>
                        <?php endif; ?>
                        
                        <?php if ($content['is_featured']): ?>
                        <span class="badge badge-featured">مميز</span>
                        <?php endif; ?>
                        
                        <?php if ($content['is_trending']): ?>
                        <span class="badge badge-trending">رائج</span>
                        <?php endif; ?>
                        
                        <span class="badge badge-type">
                            <?php echo $content['type'] === 'movie' ? 'فيلم' : 'مسلسل'; ?>
                        </span>
                        
                        <?php if ($content['age_rating']): ?>
                        <span class="badge badge-age"><?php echo $content['age_rating']; ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <h1 class="content-title"><?php echo htmlspecialchars($content['title']); ?></h1>
                    
                    <div class="content-meta">
                        <div class="meta-item">
                            <i class="fas fa-calendar"></i>
                            <span><?php echo date('Y', strtotime($content['release_date'])); ?></span>
                        </div>
                        
                        <?php if ($content['duration']): ?>
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            <span><?php echo $content['duration']; ?> دقيقة</span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($content['our_rating']): ?>
                        <div class="meta-item">
                            <i class="fas fa-star"></i>
                            <span><?php echo $content['our_rating']; ?>/10</span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($content['imdb_rating']): ?>
                        <div class="meta-item">
                            <i class="fab fa-imdb"></i>
                            <span><?php echo $content['imdb_rating']; ?>/10</span>
                        </div>
                        <?php endif; ?>
                        
                        <div class="meta-item">
                            <i class="fas fa-eye"></i>
                            <span><?php echo number_format($content['view_count']); ?> مشاهدة</span>
                        </div>
                    </div>
                    
                    <p class="content-description"><?php echo htmlspecialchars($content['description']); ?></p>
                    
                    <div class="content-genres">
                        <?php 
                        $genres = explode(',', $content['genre']);
                        foreach ($genres as $genre): 
                        ?>
                        <a href="/genre/<?php echo trim($genre); ?>" class="genre-tag">
                            <?php echo trim($genre); ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="content-actions">
                        <?php if ($canAccess): ?>
                        <a href="/player/<?php echo $content['id']; ?>" class="btn btn-primary btn-lg">
                            <i class="fas fa-play"></i>
                            مشاهدة الآن
                        </a>
                        <?php else: ?>
                        <a href="/subscription" class="btn btn-primary btn-lg">
                            <i class="fas fa-crown"></i>
                            اشترك للمشاهدة
                        </a>
                        <?php endif; ?>
                        
                        <?php if ($content['trailer_url']): ?>
                        <button class="btn btn-outline btn-lg" id="trailer-btn" data-url="<?php echo $content['trailer_url']; ?>">
                            <i class="fas fa-video"></i>
                            المقطع الدعائي
                        </button>
                        <?php endif; ?>
                        
                        <?php if (IS_LOGGED_IN): ?>
                        <button class="btn btn-icon <?php echo $isFavorite ? 'active' : ''; ?>" 
                                id="favorite-btn" 
                                data-content-id="<?php echo $content['id']; ?>"
                                title="إضافة للمفضلة">
                            <i class="<?php echo $isFavorite ? 'fas' : 'far'; ?> fa-heart"></i>
                        </button>
                        
                        <button class="btn btn-icon <?php echo $isInWatchlist ? 'active' : ''; ?>" 
                                id="watchlist-btn" 
                                data-content-id="<?php echo $content['id']; ?>"
                                title="إضافة لقائمة المشاهدة">
                            <i class="fas fa-bookmark"></i>
                        </button>
                        
                        <button class="btn btn-icon" id="share-btn" title="مشاركة">
                            <i class="fas fa-share-alt"></i>
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Content Details -->
    <section class="content-details">
        <div class="container">
            <div class="details-grid">
                <!-- Main Content -->
                <div class="details-main">
                    <!-- Synopsis -->
                    <?php if ($content['synopsis']): ?>
                    <div class="detail-section">
                        <h3 class="section-title">القصة</h3>
                        <p class="synopsis"><?php echo nl2br(htmlspecialchars($content['synopsis'])); ?></p>
                    </div>
                    <?php endif; ?>

                    <!-- Cast and Crew -->
                    <div class="detail-section">
                        <h3 class="section-title">فريق العمل</h3>
                        <div class="crew-grid">
                            <?php if ($content['director']): ?>
                            <div class="crew-item">
                                <strong>المخرج:</strong>
                                <span><?php echo htmlspecialchars($content['director']); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($content['producer']): ?>
                            <div class="crew-item">
                                <strong>المنتج:</strong>
                                <span><?php echo htmlspecialchars($content['producer']); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($content['writer']): ?>
                            <div class="crew-item">
                                <strong>الكاتب:</strong>
                                <span><?php echo htmlspecialchars($content['writer']); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($content['cast']): ?>
                            <div class="crew-item">
                                <strong>الممثلون:</strong>
                                <span><?php echo htmlspecialchars($content['cast']); ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Seasons and Episodes (for series) -->
                    <?php if ($content['type'] === 'series' && !empty($seasons)): ?>
                    <div class="detail-section">
                        <h3 class="section-title">المواسم والحلقات</h3>
                        <div class="seasons-container">
                            <?php foreach ($seasons as $season): ?>
                            <div class="season-card">
                                <div class="season-header">
                                    <h4 class="season-title">الموسم <?php echo $season['season_number']; ?></h4>
                                    <span class="episode-count"><?php echo $season['episode_count']; ?> حلقة</span>
                                </div>
                                
                                <?php if ($season['description']): ?>
                                <p class="season-description"><?php echo htmlspecialchars($season['description']); ?></p>
                                <?php endif; ?>
                                
                                <div class="episodes-grid">
                                    <?php 
                                    $episodes = getSeasonEpisodes($season['id']);
                                    foreach ($episodes as $episode): 
                                    ?>
                                    <div class="episode-card">
                                        <div class="episode-thumbnail">
                                            <img src="<?php echo $episode['thumbnail'] ?? '/assets/images/default-episode.jpg'; ?>" 
                                                 alt="الحلقة <?php echo $episode['episode_number']; ?>">
                                            <?php if ($canAccess): ?>
                                            <div class="episode-overlay">
                                                <button class="play-episode-btn" data-episode-id="<?php echo $episode['id']; ?>">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="episode-info">
                                            <h5 class="episode-title">
                                                الحلقة <?php echo $episode['episode_number']; ?>: 
                                                <?php echo htmlspecialchars($episode['title']); ?>
                                            </h5>
                                            <p class="episode-description">
                                                <?php echo htmlspecialchars(substr($episode['description'], 0, 100)) . '...'; ?>
                                            </p>
                                            <div class="episode-meta">
                                                <?php if ($episode['duration']): ?>
                                                <span><i class="fas fa-clock"></i> <?php echo $episode['duration']; ?> دقيقة</span>
                                                <?php endif; ?>
                                                <span><i class="fas fa-eye"></i> <?php echo number_format($episode['view_count']); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- User Rating -->
                    <?php if (IS_LOGGED_IN): ?>
                    <div class="detail-section">
                        <h3 class="section-title">تقييمك</h3>
                        <div class="user-rating">
                            <div class="rating-stars" id="user-rating-stars">
                                <?php for ($i = 1; $i <= 10; $i++): ?>
                                <button class="star-btn <?php echo $userRating && $i <= $userRating['rating'] ? 'active' : ''; ?>" 
                                        data-rating="<?php echo $i; ?>">
                                    <i class="fas fa-star"></i>
                                </button>
                                <?php endfor; ?>
                            </div>
                            <span class="rating-text">
                                <?php echo $userRating ? $userRating['rating'] . '/10' : 'اختر تقييمك'; ?>
                            </span>
                        </div>
                        
                        <div class="user-review">
                            <textarea id="user-review-text" 
                                      class="form-control" 
                                      placeholder="اكتب مراجعتك هنا..."
                                      rows="4"><?php echo $userRating ? htmlspecialchars($userRating['review']) : ''; ?></textarea>
                            <button class="btn btn-primary" id="submit-review-btn">
                                <?php echo $userRating ? 'تحديث المراجعة' : 'إضافة مراجعة'; ?>
                            </button>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Reviews and Ratings -->
                    <div class="detail-section">
                        <h3 class="section-title">التقييمات والمراجعات</h3>
                        
                        <div class="ratings-summary">
                            <div class="average-rating">
                                <div class="rating-number"><?php echo $content['our_rating'] ?? 'N/A'; ?></div>
                                <div class="rating-stars">
                                    <?php 
                                    $rating = $content['our_rating'] ?? 0;
                                    for ($i = 1; $i <= 10; $i++): 
                                    ?>
                                    <i class="fas fa-star <?php echo $i <= $rating ? 'active' : ''; ?>"></i>
                                    <?php endfor; ?>
                                </div>
                                <div class="rating-count">
                                    <?php echo count($ratings); ?> تقييم
                                </div>
                            </div>
                        </div>
                        
                        <div class="reviews-list">
                            <?php foreach ($ratings as $rating): ?>
                            <div class="review-item">
                                <div class="review-header">
                                    <div class="reviewer-info">
                                        <img src="<?php echo $rating['avatar'] ?? '/assets/images/default-avatar.png'; ?>" 
                                             alt="<?php echo htmlspecialchars($rating['first_name']); ?>"
                                             class="reviewer-avatar">
                                        <div class="reviewer-details">
                                            <h5 class="reviewer-name">
                                                <?php echo htmlspecialchars($rating['first_name'] . ' ' . $rating['last_name']); ?>
                                            </h5>
                                            <div class="review-rating">
                                                <?php for ($i = 1; $i <= 10; $i++): ?>
                                                <i class="fas fa-star <?php echo $i <= $rating['rating'] ? 'active' : ''; ?>"></i>
                                                <?php endfor; ?>
                                                <span class="rating-value"><?php echo $rating['rating']; ?>/10</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="review-date">
                                        <?php echo timeAgo($rating['created_at']); ?>
                                    </div>
                                </div>
                                
                                <?php if ($rating['review']): ?>
                                <div class="review-content">
                                    <p><?php echo nl2br(htmlspecialchars($rating['review'])); ?></p>
                                </div>
                                <?php endif; ?>
                                
                                <div class="review-actions">
                                    <button class="review-action-btn" data-action="helpful" data-review-id="<?php echo $rating['id']; ?>">
                                        <i class="fas fa-thumbs-up"></i>
                                        مفيد (<?php echo $rating['helpful_count']; ?>)
                                    </button>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <?php if (count($ratings) === 0): ?>
                        <div class="no-reviews">
                            <i class="fas fa-star"></i>
                            <p>لا توجد مراجعات بعد. كن أول من يكتب مراجعة!</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="details-sidebar">
                    <!-- Technical Info -->
                    <div class="sidebar-section">
                        <h4 class="sidebar-title">معلومات تقنية</h4>
                        <div class="tech-info">
                            <?php if ($content['language']): ?>
                            <div class="tech-item">
                                <strong>اللغة:</strong>
                                <span><?php echo htmlspecialchars($content['language']); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($content['country']): ?>
                            <div class="tech-item">
                                <strong>البلد:</strong>
                                <span><?php echo htmlspecialchars($content['country']); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($content['video_quality']): ?>
                            <div class="tech-item">
                                <strong>الجودة:</strong>
                                <span><?php echo $content['video_quality']; ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($content['file_size']): ?>
                            <div class="tech-item">
                                <strong>حجم الملف:</strong>
                                <span><?php echo formatFileSize($content['file_size']); ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Related Content -->
                    <?php if (!empty($relatedContent)): ?>
                    <div class="sidebar-section">
                        <h4 class="sidebar-title">محتوى مشابه</h4>
                        <div class="related-content">
                            <?php foreach ($relatedContent as $related): ?>
                            <a href="/content/<?php echo $related['id']; ?>" class="related-item">
                                <img src="<?php echo $related['poster']; ?>" 
                                     alt="<?php echo htmlspecialchars($related['title']); ?>"
                                     class="related-poster">
                                <div class="related-info">
                                    <h5 class="related-title"><?php echo htmlspecialchars($related['title']); ?></h5>
                                    <div class="related-meta">
                                        <span class="related-year"><?php echo date('Y', strtotime($related['release_date'])); ?></span>
                                        <span class="related-rating">
                                            <i class="fas fa-star"></i>
                                            <?php echo $related['our_rating']; ?>
                                        </span>
                                    </div>
                                </div>
                            </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Trailer Modal -->
<div id="trailer-modal" class="modal-overlay" style="display: none;">
    <div class="modal-container">
        <div class="modal-header">
            <h3 class="modal-title">المقطع الدعائي</h3>
            <button class="modal-close" id="close-trailer">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="video-container">
                <iframe id="trailer-iframe" 
                        width="100%" 
                        height="400" 
                        frameborder="0" 
                        allowfullscreen></iframe>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initContentDetail();
});

function initContentDetail() {
    // تهيئة أزرار الإجراءات
    initActionButtons();
    
    // تهيئة نظام التقييم
    initRatingSystem();
    
    // تهيئة المقطع الدعائي
    initTrailer();
    
    // تهيئة مشاركة المحتوى
    initSharing();
    
    // تهيئة تشغيل الحلقات
    initEpisodePlayer();
}

function initActionButtons() {
    // زر المفضلة
    const favoriteBtn = document.getElementById('favorite-btn');
    if (favoriteBtn) {
        favoriteBtn.addEventListener('click', function() {
            toggleFavorite(this.dataset.contentId, this);
        });
    }
    
    // زر قائمة المشاهدة
    const watchlistBtn = document.getElementById('watchlist-btn');
    if (watchlistBtn) {
        watchlistBtn.addEventListener('click', function() {
            toggleWatchlist(this.dataset.contentId, this);
        });
    }
}

async function toggleFavorite(contentId, button) {
    try {
        const response = await APP.api.post('/api/favorites/toggle', { content_id: contentId });
        
        if (response.success) {
            const icon = button.querySelector('i');
            if (response.is_favorite) {
                icon.className = 'fas fa-heart';
                button.classList.add('active');
                APP.ui.showNotification('تم إضافة المحتوى للمفضلة', 'success');
            } else {
                icon.className = 'far fa-heart';
                button.classList.remove('active');
                APP.ui.showNotification('تم إزالة المحتوى من المفضلة', 'info');
            }
        }
    } catch (error) {
        APP.ui.showNotification('خطأ في العملية', 'error');
    }
}

async function toggleWatchlist(contentId, button) {
    try {
        const response = await APP.api.post('/api/watchlist/toggle', { content_id: contentId });
        
        if (response.success) {
            if (response.in_watchlist) {
                button.classList.add('active');
                APP.ui.showNotification('تم إضافة المحتوى لقائمة المشاهدة', 'success');
            } else {
                button.classList.remove('active');
                APP.ui.showNotification('تم إزالة المحتوى من قائمة المشاهدة', 'info');
            }
        }
    } catch (error) {
        APP.ui.showNotification('خطأ في العملية', 'error');
    }
}

function initRatingSystem() {
    const starButtons = document.querySelectorAll('.star-btn');
    const submitBtn = document.getElementById('submit-review-btn');
    let selectedRating = 0;
    
    starButtons.forEach((star, index) => {
        star.addEventListener('click', function() {
            selectedRating = parseInt(this.dataset.rating);
            updateStarDisplay(selectedRating);
        });
        
        star.addEventListener('mouseenter', function() {
            const hoverRating = parseInt(this.dataset.rating);
            updateStarDisplay(hoverRating);
        });
    });
    
    document.getElementById('user-rating-stars').addEventListener('mouseleave', function() {
        updateStarDisplay(selectedRating);
    });
    
    if (submitBtn) {
        submitBtn.addEventListener('click', submitReview);
    }
}

function updateStarDisplay(rating) {
    const stars = document.querySelectorAll('.star-btn');
    const ratingText = document.querySelector('.rating-text');
    
    stars.forEach((star, index) => {
        if (index < rating) {
            star.classList.add('active');
        } else {
            star.classList.remove('active');
        }
    });
    
    ratingText.textContent = rating > 0 ? `${rating}/10` : 'اختر تقييمك';
}

async function submitReview() {
    const rating = document.querySelector('.star-btn.active:last-of-type')?.dataset.rating;
    const review = document.getElementById('user-review-text').value;
    const contentId = <?php echo $content['id']; ?>;
    
    if (!rating) {
        APP.ui.showNotification('يرجى اختيار تقييم', 'warning');
        return;
    }
    
    try {
        const response = await APP.api.post('/api/content/rate', {
            content_id: contentId,
            rating: parseInt(rating),
            review: review
        });
        
        if (response.success) {
            APP.ui.showNotification('تم حفظ تقييمك بنجاح', 'success');
            // إعادة تحميل الصفحة لإظهار التقييم الجديد
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
    } catch (error) {
        APP.ui.showNotification('خطأ في حفظ التقييم', 'error');
    }
}

function initTrailer() {
    const trailerBtn = document.getElementById('trailer-btn');
    const trailerModal = document.getElementById('trailer-modal');
    const closeTrailer = document.getElementById('close-trailer');
    const trailerIframe = document.getElementById('trailer-iframe');
    
    if (trailerBtn) {
        trailerBtn.addEventListener('click', function() {
            const trailerUrl = this.dataset.url;
            trailerIframe.src = trailerUrl;
            trailerModal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        });
    }
    
    if (closeTrailer) {
        closeTrailer.addEventListener('click', function() {
            trailerModal.style.display = 'none';
            trailerIframe.src = '';
            document.body.style.overflow = '';
        });
    }
    
    if (trailerModal) {
        trailerModal.addEventListener('click', function(e) {
            if (e.target === trailerModal) {
                closeTrailer.click();
            }
        });
    }
}

function initSharing() {
    const shareBtn = document.getElementById('share-btn');
    
    if (shareBtn) {
        shareBtn.addEventListener('click', function() {
            if (navigator.share) {
                navigator.share({
                    title: '<?php echo htmlspecialchars($content['title']); ?>',
                    text: '<?php echo htmlspecialchars($content['description']); ?>',
                    url: window.location.href
                });
            } else {
                // نسخ الرابط للحافظة
                navigator.clipboard.writeText(window.location.href).then(() => {
                    APP.ui.showNotification('تم نسخ الرابط', 'success');
                });
            }
        });
    }
}

function initEpisodePlayer() {
    const playButtons = document.querySelectorAll('.play-episode-btn');
    
    playButtons.forEach(button => {
        button.addEventListener('click', function() {
            const episodeId = this.dataset.episodeId;
            window.location.href = `/player/<?php echo $content['id']; ?>?episode=${episodeId}`;
        });
    });
}

// تتبع الأحداث
trackEvent('content_view', {
    content_id: <?php echo $content['id']; ?>,
    content_type: '<?php echo $content['type']; ?>',
    content_title: '<?php echo htmlspecialchars($content['title']); ?>'
});
</script>

<?php
/**
 * دوال مساعدة لصفحة تفاصيل المحتوى
 */

function getRelatedContent($contentId, $genre, $limit = 6) {
    global $db;
    return $db->select(
        "SELECT * FROM " . DB_PREFIX . "content 
         WHERE status = 'published' AND id != ? AND genre LIKE ? 
         ORDER BY our_rating DESC, view_count DESC 
         LIMIT ?",
        [$contentId, "%{$genre}%", $limit]
    );
}

function getContentRatings($contentId, $limit = 10) {
    global $db;
    return $db->select(
        "SELECT cr.*, u.first_name, u.last_name, u.avatar 
         FROM " . DB_PREFIX . "content_ratings cr
         JOIN " . DB_PREFIX . "users u ON cr.user_id = u.id
         WHERE cr.content_id = ? 
         ORDER BY cr.created_at DESC 
         LIMIT ?",
        [$contentId, $limit]
    );
}

function getUserRating($contentId, $userId) {
    global $db;
    return $db->selectOne(
        "SELECT * FROM " . DB_PREFIX . "content_ratings 
         WHERE content_id = ? AND user_id = ?",
        [$contentId, $userId]
    );
}

function getContentComments($contentId, $limit = 10) {
    global $db;
    return $db->select(
        "SELECT c.*, u.first_name, u.last_name, u.avatar 
         FROM " . DB_PREFIX . "comments c
         JOIN " . DB_PREFIX . "users u ON c.user_id = u.id
         WHERE c.content_id = ? AND c.status = 'approved'
         ORDER BY c.created_at DESC 
         LIMIT ?",
        [$contentId, $limit]
    );
}

function getContentSeasons($contentId) {
    global $db;
    return $db->select(
        "SELECT * FROM " . DB_PREFIX . "seasons 
         WHERE content_id = ? 
         ORDER BY season_number ASC",
        [$contentId]
    );
}

function getSeasonEpisodes($seasonId) {
    global $db;
    return $db->select(
        "SELECT * FROM " . DB_PREFIX . "episodes 
         WHERE season_id = ? 
         ORDER BY episode_number ASC",
        [$seasonId]
    );
}

function isInFavorites($contentId, $userId) {
    global $db;
    $result = $db->selectOne(
        "SELECT id FROM " . DB_PREFIX . "favorites 
         WHERE content_id = ? AND user_id = ?",
        [$contentId, $userId]
    );
    return $result !== false;
}

function isInWatchlist($contentId, $userId) {
    global $db;
    $result = $db->selectOne(
        "SELECT id FROM " . DB_PREFIX . "watchlist 
         WHERE content_id = ? AND user_id = ?",
        [$contentId, $userId]
    );
    return $result !== false;
}
?>
