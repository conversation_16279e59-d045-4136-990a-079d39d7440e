[2025-07-17 19:45:30] ERROR: Database connection failed: SQLSTATE[HY000] [1049] Unknown database 'streaming_platform'
[2025-07-17 19:45:46] ERROR: Database connection failed: SQLSTATE[HY000] [1049] Unknown database 'streaming_platform'
[2025-07-17 19:47:53] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 19:47:53] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 19:48:06] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 19:48:06] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 19:48:18] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 19:48:18] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 19:48:51] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 19:48:51] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 19:49:59] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 19:49:59] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 19:51:09] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 19:51:09] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 20:45:58] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 20:45:58] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 21:44:34] ERROR: Database connection failed: SQLSTATE[HY000] [1049] Unknown database 'streaming_platform'
[2025-07-17 21:44:36] ERROR: Database connection failed: SQLSTATE[HY000] [1049] Unknown database 'streaming_platform'
[2025-07-17 21:48:29] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 21:48:29] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 21:52:34] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 21:52:34] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 21:52:40] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 21:52:40] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 21:55:12] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 21:55:12] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 21:57:19] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 21:57:19] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:02:41] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:02:41] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:02:42] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:02:42] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:02:43] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:02:43] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:02:44] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:02:44] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:02:44] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:02:44] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:02:45] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:02:45] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:02:46] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:02:46] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:02:47] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:02:47] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:03:36] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:03:36] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:03:37] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:03:37] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:03:37] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:03:37] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:04:25] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:04:25] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:04:25] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:04:25] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:04:26] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:04:26] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:04:26] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:04:26] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:04:26] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:04:26] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:08:00] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:08:00] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:08:03] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:08:03] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
[2025-07-17 22:08:28] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_active = 1
[2025-07-17 22:08:28] ERROR: Prepare failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'streaming_platform.activity_logs' doesn't exist SQL: 
                INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, request_data, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            
