-- 🗄️ قاعدة بيانات منصة البث الشاملة
-- إنشاء قاعدة البيانات والجداول المطلوبة

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS streaming_platform 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE streaming_platform;

-- ==========================================
-- 👥 جدول المستخدمين
-- ==========================================
CREATE TABLE sp_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    avatar VARCHAR(255),
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    country VARCHAR(50),
    language VARCHAR(5) DEFAULT 'ar',
    timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
    subscription_type ENUM('free', 'basic', 'premium', 'vip') DEFAULT 'free',
    subscription_expires_at DATETIME NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(100),
    password_reset_token VARCHAR(100),
    password_reset_expires DATETIME NULL,
    last_login_at DATETIME NULL,
    login_attempts INT DEFAULT 0,
    locked_until DATETIME NULL,
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
    role ENUM('user', 'moderator', 'admin', 'super_admin') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_subscription (subscription_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- ==========================================
-- 🎬 جدول المحتوى (أفلام ومسلسلات)
-- ==========================================
CREATE TABLE sp_content (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    original_title VARCHAR(255),
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    synopsis TEXT,
    type ENUM('movie', 'series', 'documentary', 'live') NOT NULL,
    genre VARCHAR(255),
    director VARCHAR(255),
    cast TEXT,
    producer VARCHAR(255),
    writer VARCHAR(255),
    country VARCHAR(100),
    language VARCHAR(50),
    release_date DATE,
    duration INT, -- بالدقائق للأفلام
    total_episodes INT DEFAULT 0, -- للمسلسلات
    total_seasons INT DEFAULT 0, -- للمسلسلات
    imdb_rating DECIMAL(3,1),
    our_rating DECIMAL(3,1),
    age_rating VARCHAR(10),
    poster VARCHAR(255),
    banner VARCHAR(255),
    trailer_url VARCHAR(500),
    video_url VARCHAR(500),
    video_quality VARCHAR(10) DEFAULT '720p',
    video_size BIGINT, -- بالبايت
    subtitle_files JSON, -- مسارات ملفات الترجمة
    tags JSON, -- الكلمات المفتاحية
    is_featured BOOLEAN DEFAULT FALSE,
    is_trending BOOLEAN DEFAULT FALSE,
    is_new BOOLEAN DEFAULT FALSE,
    view_count INT DEFAULT 0,
    like_count INT DEFAULT 0,
    download_count INT DEFAULT 0,
    subscription_required ENUM('free', 'basic', 'premium', 'vip') DEFAULT 'free',
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    published_at DATETIME NULL,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES sp_users(id) ON DELETE SET NULL,
    INDEX idx_type (type),
    INDEX idx_genre (genre),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured),
    INDEX idx_trending (is_trending),
    INDEX idx_subscription (subscription_required),
    INDEX idx_published_at (published_at),
    FULLTEXT idx_search (title, description, genre, cast)
);

-- ==========================================
-- 📺 جدول الحلقات (للمسلسلات)
-- ==========================================
CREATE TABLE sp_episodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content_id INT NOT NULL,
    season_number INT NOT NULL,
    episode_number INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    duration INT, -- بالدقائق
    video_url VARCHAR(500),
    video_quality VARCHAR(10) DEFAULT '720p',
    video_size BIGINT,
    thumbnail VARCHAR(255),
    subtitle_files JSON,
    view_count INT DEFAULT 0,
    like_count INT DEFAULT 0,
    air_date DATE,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (content_id) REFERENCES sp_content(id) ON DELETE CASCADE,
    UNIQUE KEY unique_episode (content_id, season_number, episode_number),
    INDEX idx_content_season (content_id, season_number),
    INDEX idx_status (status)
);

-- ==========================================
-- ⭐ جدول التقييمات والمراجعات
-- ==========================================
CREATE TABLE sp_reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 10),
    review TEXT,
    is_spoiler BOOLEAN DEFAULT FALSE,
    helpful_count INT DEFAULT 0,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES sp_users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES sp_content(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_content (user_id, content_id),
    INDEX idx_content_rating (content_id, rating),
    INDEX idx_status (status)
);

-- ==========================================
-- 💖 جدول المفضلة
-- ==========================================
CREATE TABLE sp_favorites (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES sp_users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES sp_content(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_content (user_id, content_id),
    INDEX idx_user_id (user_id),
    INDEX idx_content_id (content_id)
);

-- ==========================================
-- 📋 جدول قائمة المشاهدة
-- ==========================================
CREATE TABLE sp_watchlist (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES sp_users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES sp_content(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_content (user_id, content_id),
    INDEX idx_user_id (user_id),
    INDEX idx_content_id (content_id)
);

-- ==========================================
-- 📊 جدول سجل المشاهدة
-- ==========================================
CREATE TABLE sp_watch_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_id INT NOT NULL,
    episode_id INT NULL,
    watch_time INT DEFAULT 0, -- الوقت المشاهد بالثواني
    total_time INT DEFAULT 0, -- المدة الإجمالية بالثواني
    progress DECIMAL(5,2) DEFAULT 0, -- نسبة المشاهدة
    completed BOOLEAN DEFAULT FALSE,
    device_type VARCHAR(50),
    ip_address VARCHAR(45),
    user_agent TEXT,
    watched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES sp_users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES sp_content(id) ON DELETE CASCADE,
    FOREIGN KEY (episode_id) REFERENCES sp_episodes(id) ON DELETE CASCADE,
    INDEX idx_user_content (user_id, content_id),
    INDEX idx_user_episode (user_id, episode_id),
    INDEX idx_watched_at (watched_at)
);

-- ==========================================
-- 📥 جدول التحميلات
-- ==========================================
CREATE TABLE sp_downloads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_id INT NOT NULL,
    episode_id INT NULL,
    file_path VARCHAR(500),
    file_size BIGINT,
    quality VARCHAR(10),
    status ENUM('pending', 'downloading', 'completed', 'failed', 'expired') DEFAULT 'pending',
    progress DECIMAL(5,2) DEFAULT 0,
    expires_at DATETIME,
    downloaded_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES sp_users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES sp_content(id) ON DELETE CASCADE,
    FOREIGN KEY (episode_id) REFERENCES sp_episodes(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at)
);

-- ==========================================
-- 💳 جدول الاشتراكات والدفع
-- ==========================================
CREATE TABLE sp_subscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    plan_type ENUM('basic', 'premium', 'vip') NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    payment_method VARCHAR(50),
    payment_id VARCHAR(255),
    status ENUM('active', 'cancelled', 'expired', 'pending') DEFAULT 'pending',
    starts_at DATETIME NOT NULL,
    expires_at DATETIME NOT NULL,
    auto_renew BOOLEAN DEFAULT TRUE,
    cancelled_at DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES sp_users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at)
);

-- ==========================================
-- 🔔 جدول الإشعارات
-- ==========================================
CREATE TABLE sp_notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('new_content', 'subscription', 'reminder', 'promotion', 'system') NOT NULL,
    data JSON,
    is_read BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES sp_users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

-- ==========================================
-- 🔐 جدول الجلسات
-- ==========================================
CREATE TABLE sp_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    device_type VARCHAR(50),
    data TEXT,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    
    FOREIGN KEY (user_id) REFERENCES sp_users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_last_activity (last_activity)
);

-- ==========================================
-- 📱 جدول أجهزة المستخدمين
-- ==========================================
CREATE TABLE sp_user_devices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    device_name VARCHAR(100),
    device_type VARCHAR(50),
    device_id VARCHAR(255) UNIQUE,
    fcm_token VARCHAR(255),
    platform VARCHAR(20),
    app_version VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES sp_users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_device_id (device_id),
    INDEX idx_is_active (is_active)
);

-- ==========================================
-- 📈 جدول الإحصائيات
-- ==========================================
CREATE TABLE sp_analytics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_type VARCHAR(50) NOT NULL,
    user_id INT,
    content_id INT,
    episode_id INT,
    data JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES sp_users(id) ON DELETE SET NULL,
    FOREIGN KEY (content_id) REFERENCES sp_content(id) ON DELETE SET NULL,
    FOREIGN KEY (episode_id) REFERENCES sp_episodes(id) ON DELETE SET NULL,
    INDEX idx_event_type (event_type),
    INDEX idx_user_id (user_id),
    INDEX idx_content_id (content_id),
    INDEX idx_created_at (created_at)
);

-- ==========================================
-- ⚙️ جدول إعدادات النظام
-- ==========================================
CREATE TABLE sp_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
);

-- ==========================================
-- 📝 جدول السجلات
-- ==========================================
CREATE TABLE sp_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    level ENUM('error', 'warning', 'info', 'debug') NOT NULL,
    message TEXT NOT NULL,
    context JSON,
    user_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    file VARCHAR(255),
    line INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES sp_users(id) ON DELETE SET NULL,
    INDEX idx_level (level),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
);

-- ==========================================
-- 🔍 إنشاء فهارس إضافية للبحث والأداء
-- ==========================================

-- فهرس مركب للمحتوى المنشور
CREATE INDEX idx_content_published ON sp_content (status, published_at, type);

-- فهرس مركب لسجل المشاهدة
CREATE INDEX idx_watch_history_user_date ON sp_watch_history (user_id, watched_at);

-- فهرس مركب للتقييمات
CREATE INDEX idx_reviews_content_approved ON sp_reviews (content_id, status, rating);

-- فهرس مركب للإشعارات غير المقروءة
CREATE INDEX idx_notifications_unread ON sp_notifications (user_id, is_read, created_at);

-- ==========================================
-- 🎯 إدراج البيانات الأساسية
-- ==========================================

-- إعدادات النظام الافتراضية
INSERT INTO sp_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site_name', 'منصة البث الشاملة', 'string', 'اسم الموقع', TRUE),
('site_description', 'شاهد أفضل الأفلام والمسلسلات بجودة عالية', 'string', 'وصف الموقع', TRUE),
('maintenance_mode', 'false', 'boolean', 'وضع الصيانة', FALSE),
('registration_enabled', 'true', 'boolean', 'تفعيل التسجيل', FALSE),
('email_verification_required', 'true', 'boolean', 'مطلوب تأكيد البريد الإلكتروني', FALSE),
('max_devices_per_user', '5', 'number', 'الحد الأقصى للأجهزة لكل مستخدم', FALSE),
('video_qualities', '["240p", "360p", "480p", "720p", "1080p", "4K"]', 'json', 'جودات الفيديو المتاحة', TRUE),
('supported_languages', '["ar", "en", "fr", "tr"]', 'json', 'اللغات المدعومة', TRUE);

-- إنشاء مستخدم إداري افتراضي
INSERT INTO sp_users (first_name, last_name, email, password_hash, role, status, email_verified, subscription_type) VALUES
('مدير', 'النظام', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin', 'active', TRUE, 'vip');

-- ==========================================
-- 🔧 إجراءات مخزنة مفيدة
-- ==========================================

DELIMITER //

-- إجراء لحساب تقييم المحتوى
CREATE PROCEDURE UpdateContentRating(IN content_id INT)
BEGIN
    DECLARE avg_rating DECIMAL(3,1);
    
    SELECT AVG(rating) INTO avg_rating 
    FROM sp_reviews 
    WHERE content_id = content_id AND status = 'approved';
    
    UPDATE sp_content 
    SET our_rating = COALESCE(avg_rating, 0) 
    WHERE id = content_id;
END //

-- إجراء لتنظيف البيانات القديمة
CREATE PROCEDURE CleanupOldData()
BEGIN
    -- حذف الجلسات المنتهية الصلاحية
    DELETE FROM sp_sessions WHERE expires_at < NOW();
    
    -- حذف التحميلات المنتهية الصلاحية
    DELETE FROM sp_downloads WHERE status = 'expired' AND expires_at < NOW();
    
    -- حذف السجلات القديمة (أكثر من 90 يوم)
    DELETE FROM sp_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- حذف الإشعارات القديمة المقروءة (أكثر من 30 يوم)
    DELETE FROM sp_notifications 
    WHERE is_read = TRUE AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
END //

DELIMITER ;

-- ==========================================
-- 📅 إنشاء مهام مجدولة (Events)
-- ==========================================

-- تفعيل مجدول الأحداث
SET GLOBAL event_scheduler = ON;

-- مهمة تنظيف البيانات القديمة (يومياً في الساعة 2 صباحاً)
CREATE EVENT IF NOT EXISTS cleanup_old_data
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURRENT_DATE + INTERVAL 1 DAY, '02:00:00')
DO
  CALL CleanupOldData();

-- مهمة تحديث إحصائيات المحتوى (كل ساعة)
CREATE EVENT IF NOT EXISTS update_content_stats
ON SCHEDULE EVERY 1 HOUR
DO
  UPDATE sp_content c
  SET view_count = (
    SELECT COUNT(*) FROM sp_watch_history w 
    WHERE w.content_id = c.id
  );

-- ==========================================
-- ✅ اكتمال إنشاء قاعدة البيانات
-- ==========================================

-- رسالة نجاح
SELECT 'تم إنشاء قاعدة البيانات بنجاح! 🎉' AS message;
