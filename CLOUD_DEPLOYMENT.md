# ☁️ دليل النشر السحابي - منصة البث العربية

## 🎯 **نظرة عامة**

هذا الدليل يوضح كيفية نشر منصة البث العربية على مختلف منصات الحوسبة السحابية (AWS, GCP, Azure) مع أفضل الممارسات والتحسينات.

### **مزايا النشر السحابي**
- **قابلية التوسع اللامحدودة**: توسيع الموارد حسب الطلب
- **الموثوقية العالية**: توفر 99.99% مع التكرار
- **الأمان المتقدم**: حماية على مستوى المؤسسات
- **التكلفة المحسنة**: دفع حسب الاستخدام
- **الإدارة المبسطة**: أدوات إدارة متقدمة

---

## 🌐 **Amazon Web Services (AWS)**

### **البنية التحتية على AWS**

```
┌─────────────────────────────────────────────────────────────┐
│                        AWS Cloud                            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    VPC (Virtual Private Cloud)          │ │
│  │  ┌─────────────────┐ ┌─────────────────┐               │ │
│  │  │  Public Subnet  │ │ Private Subnet  │               │ │
│  │  │                 │ │                 │               │ │
│  │  │ ┌─────────────┐ │ │ ┌─────────────┐ │               │ │
│  │  │ │     ALB     │ │ │ │     EKS     │ │               │ │
│  │  │ │(Load Balancer)│ │ │  (Kubernetes) │ │               │ │
│  │  │ └─────────────┘ │ │ └─────────────┘ │               │ │
│  │  │                 │ │                 │               │ │
│  │  │ ┌─────────────┐ │ │ ┌─────────────┐ │               │ │
│  │  │ │ CloudFront  │ │ │ │     RDS     │ │               │ │
│  │  │ │    (CDN)    │ │ │ │  (Database) │ │               │ │
│  │  │ └─────────────┘ │ │ └─────────────┘ │               │ │
│  │  └─────────────────┘ └─────────────────┘               │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### **Terraform Configuration for AWS**

```hcl
# aws/main.tf
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# VPC Configuration
resource "aws_vpc" "main" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "streaming-platform-vpc"
    Environment = var.environment
  }
}

# Internet Gateway
resource "aws_internet_gateway" "main" {
  vpc_id = aws_vpc.main.id

  tags = {
    Name = "streaming-platform-igw"
  }
}

# Public Subnets
resource "aws_subnet" "public" {
  count = 2

  vpc_id                  = aws_vpc.main.id
  cidr_block              = "10.0.${count.index + 1}.0/24"
  availability_zone       = data.aws_availability_zones.available.names[count.index]
  map_public_ip_on_launch = true

  tags = {
    Name = "streaming-platform-public-${count.index + 1}"
    Type = "public"
  }
}

# Private Subnets
resource "aws_subnet" "private" {
  count = 2

  vpc_id            = aws_vpc.main.id
  cidr_block        = "10.0.${count.index + 10}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name = "streaming-platform-private-${count.index + 1}"
    Type = "private"
  }
}

# EKS Cluster
resource "aws_eks_cluster" "main" {
  name     = "streaming-platform-cluster"
  role_arn = aws_iam_role.eks_cluster.arn
  version  = "1.28"

  vpc_config {
    subnet_ids              = concat(aws_subnet.public[*].id, aws_subnet.private[*].id)
    endpoint_private_access = true
    endpoint_public_access  = true
  }

  depends_on = [
    aws_iam_role_policy_attachment.eks_cluster_policy,
  ]

  tags = {
    Name = "streaming-platform-eks"
  }
}

# EKS Node Group
resource "aws_eks_node_group" "main" {
  cluster_name    = aws_eks_cluster.main.name
  node_group_name = "streaming-platform-nodes"
  node_role_arn   = aws_iam_role.eks_node_group.arn
  subnet_ids      = aws_subnet.private[*].id

  scaling_config {
    desired_size = 3
    max_size     = 10
    min_size     = 1
  }

  instance_types = ["t3.medium"]

  depends_on = [
    aws_iam_role_policy_attachment.eks_worker_node_policy,
    aws_iam_role_policy_attachment.eks_cni_policy,
    aws_iam_role_policy_attachment.eks_container_registry_policy,
  ]
}

# RDS Database
resource "aws_db_instance" "main" {
  identifier = "streaming-platform-db"

  engine         = "mysql"
  engine_version = "8.0"
  instance_class = "db.t3.medium"

  allocated_storage     = 100
  max_allocated_storage = 1000
  storage_type          = "gp2"
  storage_encrypted     = true

  db_name  = "streaming_platform"
  username = "admin"
  password = var.db_password

  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.main.name

  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"

  skip_final_snapshot = false
  final_snapshot_identifier = "streaming-platform-final-snapshot"

  tags = {
    Name = "streaming-platform-db"
  }
}

# ElastiCache Redis
resource "aws_elasticache_subnet_group" "main" {
  name       = "streaming-platform-cache-subnet"
  subnet_ids = aws_subnet.private[*].id
}

resource "aws_elasticache_cluster" "main" {
  cluster_id           = "streaming-platform-cache"
  engine               = "redis"
  node_type            = "cache.t3.micro"
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  subnet_group_name    = aws_elasticache_subnet_group.main.name
  security_group_ids   = [aws_security_group.redis.id]
}

# Application Load Balancer
resource "aws_lb" "main" {
  name               = "streaming-platform-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets            = aws_subnet.public[*].id

  enable_deletion_protection = false

  tags = {
    Name = "streaming-platform-alb"
  }
}

# CloudFront Distribution
resource "aws_cloudfront_distribution" "main" {
  origin {
    domain_name = aws_lb.main.dns_name
    origin_id   = "ALB-streaming-platform"

    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1.2"]
    }
  }

  enabled             = true
  is_ipv6_enabled     = true
  default_root_object = "index.html"

  aliases = ["streaming-platform.com", "www.streaming-platform.com"]

  default_cache_behavior {
    allowed_methods        = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods         = ["GET", "HEAD"]
    target_origin_id       = "ALB-streaming-platform"
    compress               = true
    viewer_protocol_policy = "redirect-to-https"

    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }

    min_ttl     = 0
    default_ttl = 3600
    max_ttl     = 86400
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn = aws_acm_certificate.main.arn
    ssl_support_method  = "sni-only"
  }

  tags = {
    Name = "streaming-platform-cdn"
  }
}
```

### **AWS Deployment Script**

```bash
#!/bin/bash
# aws-deploy.sh

set -e

AWS_REGION=${AWS_REGION:-us-east-1}
CLUSTER_NAME="streaming-platform-cluster"
ENVIRONMENT=${ENVIRONMENT:-production}

echo "🚀 Deploying to AWS in region: $AWS_REGION"

# Initialize Terraform
cd aws/
terraform init

# Plan deployment
terraform plan -var="environment=$ENVIRONMENT" -var="aws_region=$AWS_REGION"

# Apply infrastructure
echo "📦 Creating AWS infrastructure..."
terraform apply -auto-approve -var="environment=$ENVIRONMENT" -var="aws_region=$AWS_REGION"

# Configure kubectl
echo "⚙️ Configuring kubectl..."
aws eks update-kubeconfig --region $AWS_REGION --name $CLUSTER_NAME

# Deploy applications to EKS
echo "🚀 Deploying applications to EKS..."
kubectl apply -f ../k8s/

# Wait for deployment
echo "⏳ Waiting for deployment to complete..."
kubectl wait --for=condition=available --timeout=300s deployment/web-app -n streaming-platform

# Get LoadBalancer URL
ALB_URL=$(kubectl get ingress streaming-platform-ingress -n streaming-platform -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')

echo "✅ Deployment completed successfully!"
echo "🌐 Application URL: https://$ALB_URL"
echo "📊 CloudWatch Logs: https://console.aws.amazon.com/cloudwatch/home?region=$AWS_REGION#logsV2:log-groups"
```

---

## 🌐 **Google Cloud Platform (GCP)**

### **GCP Infrastructure**

```yaml
# gcp/gke-cluster.yaml
apiVersion: container.v1
kind: Cluster
metadata:
  name: streaming-platform-cluster
spec:
  location: us-central1
  initialNodeCount: 3
  nodeConfig:
    machineType: e2-medium
    diskSizeGb: 100
    oauthScopes:
    - https://www.googleapis.com/auth/cloud-platform
  addonsConfig:
    httpLoadBalancing:
      disabled: false
    horizontalPodAutoscaling:
      disabled: false
  networkPolicy:
    enabled: true
  ipAllocationPolicy:
    useIpAliases: true
  workloadIdentityConfig:
    workloadPool: PROJECT_ID.svc.id.goog
```

### **Terraform Configuration for GCP**

```hcl
# gcp/main.tf
terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

# VPC Network
resource "google_compute_network" "main" {
  name                    = "streaming-platform-vpc"
  auto_create_subnetworks = false
}

# Subnet
resource "google_compute_subnetwork" "main" {
  name          = "streaming-platform-subnet"
  ip_cidr_range = "10.0.0.0/16"
  region        = var.region
  network       = google_compute_network.main.id

  secondary_ip_range {
    range_name    = "pods"
    ip_cidr_range = "********/16"
  }

  secondary_ip_range {
    range_name    = "services"
    ip_cidr_range = "********/16"
  }
}

# GKE Cluster
resource "google_container_cluster" "main" {
  name     = "streaming-platform-cluster"
  location = var.region

  remove_default_node_pool = true
  initial_node_count       = 1

  network    = google_compute_network.main.name
  subnetwork = google_compute_subnetwork.main.name

  ip_allocation_policy {
    cluster_secondary_range_name  = "pods"
    services_secondary_range_name = "services"
  }

  workload_identity_config {
    workload_pool = "${var.project_id}.svc.id.goog"
  }

  addons_config {
    http_load_balancing {
      disabled = false
    }
    horizontal_pod_autoscaling {
      disabled = false
    }
  }
}

# Node Pool
resource "google_container_node_pool" "main" {
  name       = "streaming-platform-nodes"
  location   = var.region
  cluster    = google_container_cluster.main.name
  node_count = 3

  node_config {
    preemptible  = false
    machine_type = "e2-medium"
    disk_size_gb = 100

    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]

    workload_metadata_config {
      mode = "GKE_METADATA"
    }
  }

  autoscaling {
    min_node_count = 1
    max_node_count = 10
  }

  management {
    auto_repair  = true
    auto_upgrade = true
  }
}

# Cloud SQL Instance
resource "google_sql_database_instance" "main" {
  name             = "streaming-platform-db"
  database_version = "MYSQL_8_0"
  region           = var.region

  settings {
    tier = "db-n1-standard-2"

    backup_configuration {
      enabled                        = true
      start_time                     = "03:00"
      point_in_time_recovery_enabled = true
      backup_retention_settings {
        retained_backups = 7
      }
    }

    ip_configuration {
      ipv4_enabled    = false
      private_network = google_compute_network.main.id
    }

    database_flags {
      name  = "innodb_buffer_pool_size"
      value = "268435456"
    }
  }

  depends_on = [google_service_networking_connection.private_vpc_connection]
}

# Redis Instance
resource "google_redis_instance" "main" {
  name           = "streaming-platform-cache"
  tier           = "STANDARD_HA"
  memory_size_gb = 1
  region         = var.region

  authorized_network = google_compute_network.main.id
  redis_version      = "REDIS_6_X"
}

# Load Balancer
resource "google_compute_global_address" "main" {
  name = "streaming-platform-ip"
}

# CDN
resource "google_compute_backend_service" "main" {
  name        = "streaming-platform-backend"
  protocol    = "HTTP"
  timeout_sec = 10

  backend {
    group = google_container_cluster.main.node_pool[0].instance_group_urls[0]
  }

  health_checks = [google_compute_health_check.main.id]
}

resource "google_compute_url_map" "main" {
  name            = "streaming-platform-url-map"
  default_service = google_compute_backend_service.main.id
}

resource "google_compute_target_https_proxy" "main" {
  name             = "streaming-platform-https-proxy"
  url_map          = google_compute_url_map.main.id
  ssl_certificates = [google_compute_ssl_certificate.main.id]
}

resource "google_compute_global_forwarding_rule" "main" {
  name       = "streaming-platform-forwarding-rule"
  target     = google_compute_target_https_proxy.main.id
  port_range = "443"
  ip_address = google_compute_global_address.main.address
}
```

### **GCP Deployment Script**

```bash
#!/bin/bash
# gcp-deploy.sh

set -e

PROJECT_ID=${PROJECT_ID:-streaming-platform-project}
REGION=${REGION:-us-central1}
CLUSTER_NAME="streaming-platform-cluster"

echo "🚀 Deploying to GCP Project: $PROJECT_ID"

# Set project
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "🔧 Enabling required APIs..."
gcloud services enable container.googleapis.com
gcloud services enable sqladmin.googleapis.com
gcloud services enable redis.googleapis.com
gcloud services enable compute.googleapis.com

# Initialize Terraform
cd gcp/
terraform init

# Plan deployment
terraform plan -var="project_id=$PROJECT_ID" -var="region=$REGION"

# Apply infrastructure
echo "📦 Creating GCP infrastructure..."
terraform apply -auto-approve -var="project_id=$PROJECT_ID" -var="region=$REGION"

# Get cluster credentials
echo "⚙️ Getting cluster credentials..."
gcloud container clusters get-credentials $CLUSTER_NAME --region $REGION

# Deploy applications
echo "🚀 Deploying applications to GKE..."
kubectl apply -f ../k8s/

# Wait for deployment
echo "⏳ Waiting for deployment to complete..."
kubectl wait --for=condition=available --timeout=300s deployment/web-app -n streaming-platform

# Get LoadBalancer IP
LB_IP=$(kubectl get ingress streaming-platform-ingress -n streaming-platform -o jsonpath='{.status.loadBalancer.ingress[0].ip}')

echo "✅ Deployment completed successfully!"
echo "🌐 Application IP: $LB_IP"
echo "📊 GCP Console: https://console.cloud.google.com/kubernetes/workload?project=$PROJECT_ID"
```

---

## 🔷 **Microsoft Azure**

### **Azure Resource Manager Template**

```json
{
  "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
  "contentVersion": "*******",
  "parameters": {
    "clusterName": {
      "type": "string",
      "defaultValue": "streaming-platform-cluster"
    },
    "location": {
      "type": "string",
      "defaultValue": "East US"
    },
    "nodeCount": {
      "type": "int",
      "defaultValue": 3
    }
  },
  "resources": [
    {
      "type": "Microsoft.ContainerService/managedClusters",
      "apiVersion": "2023-01-01",
      "name": "[parameters('clusterName')]",
      "location": "[parameters('location')]",
      "properties": {
        "dnsPrefix": "[parameters('clusterName')]",
        "agentPoolProfiles": [
          {
            "name": "agentpool",
            "count": "[parameters('nodeCount')]",
            "vmSize": "Standard_D2s_v3",
            "osType": "Linux",
            "mode": "System"
          }
        ],
        "servicePrincipalProfile": {
          "clientId": "[parameters('servicePrincipalClientId')]",
          "secret": "[parameters('servicePrincipalClientSecret')]"
        }
      }
    },
    {
      "type": "Microsoft.DBforMySQL/flexibleServers",
      "apiVersion": "2021-05-01",
      "name": "streaming-platform-db",
      "location": "[parameters('location')]",
      "properties": {
        "administratorLogin": "admin",
        "administratorLoginPassword": "[parameters('dbPassword')]",
        "version": "8.0.21",
        "storageProfile": {
          "storageMB": 102400,
          "backupRetentionDays": 7
        },
        "createMode": "Default"
      }
    },
    {
      "type": "Microsoft.Cache/Redis",
      "apiVersion": "2020-06-01",
      "name": "streaming-platform-cache",
      "location": "[parameters('location')]",
      "properties": {
        "sku": {
          "name": "Standard",
          "family": "C",
          "capacity": 1
        },
        "enableNonSslPort": false,
        "minimumTlsVersion": "1.2"
      }
    }
  ]
}
```

### **Azure Deployment Script**

```bash
#!/bin/bash
# azure-deploy.sh

set -e

RESOURCE_GROUP=${RESOURCE_GROUP:-streaming-platform-rg}
LOCATION=${LOCATION:-eastus}
CLUSTER_NAME="streaming-platform-cluster"

echo "🚀 Deploying to Azure in region: $LOCATION"

# Create resource group
echo "📦 Creating resource group..."
az group create --name $RESOURCE_GROUP --location $LOCATION

# Deploy ARM template
echo "🏗️ Deploying infrastructure..."
az deployment group create \
  --resource-group $RESOURCE_GROUP \
  --template-file azure/template.json \
  --parameters clusterName=$CLUSTER_NAME location=$LOCATION

# Get AKS credentials
echo "⚙️ Getting AKS credentials..."
az aks get-credentials --resource-group $RESOURCE_GROUP --name $CLUSTER_NAME

# Deploy applications
echo "🚀 Deploying applications to AKS..."
kubectl apply -f k8s/

# Wait for deployment
echo "⏳ Waiting for deployment to complete..."
kubectl wait --for=condition=available --timeout=300s deployment/web-app -n streaming-platform

# Get LoadBalancer IP
LB_IP=$(kubectl get ingress streaming-platform-ingress -n streaming-platform -o jsonpath='{.status.loadBalancer.ingress[0].ip}')

echo "✅ Deployment completed successfully!"
echo "🌐 Application IP: $LB_IP"
echo "📊 Azure Portal: https://portal.azure.com"
```

---

## 🔄 **Multi-Cloud Deployment**

### **Multi-Cloud Strategy**

```yaml
# multi-cloud/config.yaml
clouds:
  primary:
    provider: aws
    region: us-east-1
    traffic_percentage: 60
  
  secondary:
    provider: gcp
    region: us-central1
    traffic_percentage: 30
  
  tertiary:
    provider: azure
    region: eastus
    traffic_percentage: 10

failover:
  enabled: true
  health_check_interval: 30s
  failure_threshold: 3

load_balancing:
  method: weighted_round_robin
  sticky_sessions: true
```

### **Multi-Cloud Deployment Script**

```bash
#!/bin/bash
# multi-cloud-deploy.sh

set -e

echo "🌐 Starting multi-cloud deployment..."

# Deploy to AWS (Primary)
echo "🚀 Deploying to AWS (Primary)..."
./aws-deploy.sh

# Deploy to GCP (Secondary)
echo "🚀 Deploying to GCP (Secondary)..."
./gcp-deploy.sh

# Deploy to Azure (Tertiary)
echo "🚀 Deploying to Azure (Tertiary)..."
./azure-deploy.sh

# Configure global load balancer
echo "⚖️ Configuring global load balancer..."
./configure-global-lb.sh

echo "✅ Multi-cloud deployment completed successfully!"
echo "🌍 Global endpoints configured"
echo "📊 Traffic distribution: AWS(60%), GCP(30%), Azure(10%)"
```

---

## 📊 **Monitoring and Cost Optimization**

### **Cost Monitoring Script**

```bash
#!/bin/bash
# cost-monitor.sh

echo "💰 Cloud Cost Analysis"
echo "====================="

# AWS Costs
echo "☁️ AWS Costs (Last 30 days):"
aws ce get-cost-and-usage \
  --time-period Start=2024-01-01,End=2024-01-31 \
  --granularity MONTHLY \
  --metrics BlendedCost \
  --group-by Type=DIMENSION,Key=SERVICE

# GCP Costs
echo "🌐 GCP Costs (Last 30 days):"
gcloud billing budgets list --billing-account=$GCP_BILLING_ACCOUNT

# Azure Costs
echo "🔷 Azure Costs (Last 30 days):"
az consumption usage list --start-date 2024-01-01 --end-date 2024-01-31

echo "📈 Cost optimization recommendations:"
echo "1. Use spot instances for non-critical workloads"
echo "2. Implement auto-scaling to reduce idle resources"
echo "3. Use reserved instances for predictable workloads"
echo "4. Optimize storage classes based on access patterns"
```

### **Resource Optimization**

```yaml
# optimization/resource-limits.yaml
apiVersion: v1
kind: LimitRange
metadata:
  name: resource-limits
  namespace: streaming-platform
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "512Mi"
    defaultRequest:
      cpu: "250m"
      memory: "256Mi"
    type: Container
  - max:
      cpu: "2"
      memory: "2Gi"
    min:
      cpu: "100m"
      memory: "128Mi"
    type: Container
```

---

## 🔒 **Security Best Practices**

### **Security Checklist**

```bash
#!/bin/bash
# security-check.sh

echo "🔒 Cloud Security Assessment"
echo "============================"

# Check for public resources
echo "🔍 Checking for public resources..."

# AWS Security
aws ec2 describe-security-groups --query 'SecurityGroups[?IpPermissions[?IpRanges[?CidrIp==`0.0.0.0/0`]]]'

# GCP Security
gcloud compute firewall-rules list --filter="direction:INGRESS AND allowed[].ports:('0-65535' OR '22' OR '3389')"

# Azure Security
az network nsg list --query "[].{Name:name,ResourceGroup:resourceGroup}"

echo "✅ Security assessment completed"
echo "📋 Review findings and apply security patches"
```

---

## 🚀 **Automated Deployment Pipeline**

### **GitHub Actions Multi-Cloud**

```yaml
# .github/workflows/multi-cloud-deploy.yml
name: Multi-Cloud Deployment

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  deploy-aws:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    - name: Deploy to AWS
      run: ./scripts/aws-deploy.sh

  deploy-gcp:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Setup GCP
      uses: google-github-actions/setup-gcloud@v1
      with:
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ secrets.GCP_PROJECT_ID }}
    - name: Deploy to GCP
      run: ./scripts/gcp-deploy.sh

  deploy-azure:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
    - name: Deploy to Azure
      run: ./scripts/azure-deploy.sh

  configure-global-lb:
    needs: [deploy-aws, deploy-gcp, deploy-azure]
    runs-on: ubuntu-latest
    steps:
    - name: Configure Global Load Balancer
      run: ./scripts/configure-global-lb.sh
```

---

**☁️ دليل نشر سحابي شامل ومتطور لمنصة البث العربية**

آخر تحديث: 15 يناير 2024
