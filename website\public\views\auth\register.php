<?php
/**
 * 📝 صفحة التسجيل
 */
?>

<div class="auth-container min-vh-100 d-flex align-items-center py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="auth-card card bg-secondary border-0 shadow-lg">
                    <div class="card-body p-5">
                        <!-- Logo -->
                        <div class="text-center mb-4">
                            <img src="<?= asset('images/logo.png') ?>" 
                                 alt="<?= DynamicSettings::get('site_name', 'منصة البث الشاملة') ?>" 
                                 height="60" class="mb-3">
                            <h2 class="text-white fw-bold">إنشاء حساب جديد</h2>
                            <p class="text-muted">انضم إلى آلاف المستخدمين</p>
                        </div>

                        <!-- Registration Form -->
                        <form id="registerForm" novalidate>
                            <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                            
                            <div class="row">
                                <!-- First Name -->
                                <div class="col-md-6 mb-3">
                                    <label for="first_name" class="form-label">
                                        <i class="fas fa-user me-2"></i>الاسم الأول
                                    </label>
                                    <input type="text" 
                                           class="form-control form-control-lg" 
                                           id="first_name" 
                                           name="first_name" 
                                           placeholder="الاسم الأول"
                                           required
                                           autocomplete="given-name">
                                </div>

                                <!-- Last Name -->
                                <div class="col-md-6 mb-3">
                                    <label for="last_name" class="form-label">
                                        <i class="fas fa-user me-2"></i>الاسم الأخير
                                    </label>
                                    <input type="text" 
                                           class="form-control form-control-lg" 
                                           id="last_name" 
                                           name="last_name" 
                                           placeholder="الاسم الأخير"
                                           required
                                           autocomplete="family-name">
                                </div>
                            </div>

                            <!-- Username -->
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-at me-2"></i>اسم المستخدم
                                </label>
                                <input type="text" 
                                       class="form-control form-control-lg" 
                                       id="username" 
                                       name="username" 
                                       placeholder="اختر اسم مستخدم فريد"
                                       required
                                       autocomplete="username">
                                <div class="form-text text-muted">
                                    يجب أن يكون فريداً ولا يحتوي على مسافات
                                </div>
                            </div>

                            <!-- Email -->
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-2"></i>البريد الإلكتروني
                                </label>
                                <input type="email" 
                                       class="form-control form-control-lg" 
                                       id="email" 
                                       name="email" 
                                       placeholder="أدخل بريدك الإلكتروني"
                                       required
                                       autocomplete="email">
                            </div>

                            <!-- Password -->
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>كلمة المرور
                                </label>
                                <div class="input-group">
                                    <input type="password" 
                                           class="form-control form-control-lg" 
                                           id="password" 
                                           name="password" 
                                           placeholder="أدخل كلمة مرور قوية"
                                           required
                                           autocomplete="new-password">
                                    <button class="btn btn-outline-secondary" 
                                            type="button" 
                                            id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                
                                <!-- Password Strength Indicator -->
                                <div class="password-strength mt-2">
                                    <div class="progress" style="height: 4px;">
                                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small class="form-text text-muted">
                                        يجب أن تحتوي على 8 أحرف على الأقل مع حرف كبير وصغير ورقم ورمز خاص
                                    </small>
                                </div>
                            </div>

                            <!-- Confirm Password -->
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>تأكيد كلمة المرور
                                </label>
                                <div class="input-group">
                                    <input type="password" 
                                           class="form-control form-control-lg" 
                                           id="confirm_password" 
                                           name="confirm_password" 
                                           placeholder="أعد إدخال كلمة المرور"
                                           required
                                           autocomplete="new-password">
                                    <button class="btn btn-outline-secondary" 
                                            type="button" 
                                            id="toggleConfirmPassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Terms and Privacy -->
                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="terms" 
                                           name="terms" 
                                           required>
                                    <label class="form-check-label text-muted" for="terms">
                                        أوافق على 
                                        <a href="<?= url('/terms') ?>" 
                                           class="text-primary text-decoration-none" 
                                           target="_blank">شروط الاستخدام</a>
                                        و
                                        <a href="<?= url('/privacy') ?>" 
                                           class="text-primary text-decoration-none" 
                                           target="_blank">سياسة الخصوصية</a>
                                    </label>
                                </div>
                            </div>

                            <!-- Register Button -->
                            <button type="submit" 
                                    class="btn btn-primary btn-lg w-100 mb-3">
                                <i class="fas fa-user-plus me-2"></i>
                                إنشاء الحساب
                            </button>
                        </form>

                        <!-- Social Registration -->
                        <?php if ($googleLoginEnabled || $facebookLoginEnabled): ?>
                            <div class="social-login">
                                <div class="divider text-center mb-3">
                                    <span class="text-muted bg-secondary px-3">أو</span>
                                </div>

                                <div class="d-grid gap-2">
                                    <?php if ($googleLoginEnabled): ?>
                                        <a href="<?= url('/auth/google') ?>" 
                                           class="btn btn-outline-light btn-lg">
                                            <i class="fab fa-google me-2 text-danger"></i>
                                            التسجيل بجوجل
                                        </a>
                                    <?php endif; ?>

                                    <?php if ($facebookLoginEnabled): ?>
                                        <a href="<?= url('/auth/facebook') ?>" 
                                           class="btn btn-outline-light btn-lg">
                                            <i class="fab fa-facebook-f me-2 text-primary"></i>
                                            التسجيل بفيسبوك
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Login Link -->
                        <div class="text-center mt-4">
                            <p class="text-muted mb-0">
                                لديك حساب بالفعل؟ 
                                <a href="<?= url('/login') ?>" 
                                   class="text-primary text-decoration-none fw-bold">
                                    تسجيل الدخول
                                </a>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Benefits -->
                <div class="auth-benefits mt-4">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="benefit-item text-center">
                                <i class="fas fa-gift fa-2x text-primary mb-2"></i>
                                <h6 class="text-white">فترة تجريبية مجانية</h6>
                                <p class="text-muted small mb-0">7 أيام مجاناً للمستخدمين الجدد</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="benefit-item text-center">
                                <i class="fas fa-hd-video fa-2x text-primary mb-2"></i>
                                <h6 class="text-white">جودة عالية</h6>
                                <p class="text-muted small mb-0">مشاهدة بجودة 4K و HD</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="benefit-item text-center">
                                <i class="fas fa-devices fa-2x text-primary mb-2"></i>
                                <h6 class="text-white">متعدد الأجهزة</h6>
                                <p class="text-muted small mb-0">شاهد على جميع أجهزتك</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional CSS (same as login page) -->
<style>
.auth-container {
    background: linear-gradient(135deg, var(--dark-color) 0%, var(--secondary-color) 100%);
    position: relative;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('<?= asset('images/auth-bg.jpg') ?>');
    background-size: cover;
    background-position: center;
    opacity: 0.1;
    z-index: -1;
}

.auth-card {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-card .card-body {
    background: rgba(34, 31, 31, 0.9);
    border-radius: 0.5rem;
}

.password-strength .progress-bar {
    transition: all 0.3s ease;
}

.password-strength .progress-bar.weak {
    background-color: #dc3545;
    width: 25%;
}

.password-strength .progress-bar.fair {
    background-color: #ffc107;
    width: 50%;
}

.password-strength .progress-bar.good {
    background-color: #17a2b8;
    width: 75%;
}

.password-strength .progress-bar.strong {
    background-color: #28a745;
    width: 100%;
}

.benefit-item {
    transition: transform 0.3s ease;
}

.benefit-item:hover {
    transform: translateY(-5px);
}
</style>

<!-- Additional JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    function setupPasswordToggle(toggleId, inputId) {
        const toggle = document.getElementById(toggleId);
        const input = document.getElementById(inputId);
        
        if (toggle && input) {
            toggle.addEventListener('click', function() {
                const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
                input.setAttribute('type', type);
                
                const icon = this.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });
        }
    }
    
    setupPasswordToggle('togglePassword', 'password');
    setupPasswordToggle('toggleConfirmPassword', 'confirm_password');
    
    // Password strength checker
    const passwordInput = document.getElementById('password');
    const strengthBar = document.querySelector('.password-strength .progress-bar');
    
    if (passwordInput && strengthBar) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            let strength = 0;
            
            // Check length
            if (password.length >= 8) strength++;
            
            // Check for lowercase
            if (/[a-z]/.test(password)) strength++;
            
            // Check for uppercase
            if (/[A-Z]/.test(password)) strength++;
            
            // Check for numbers
            if (/\d/.test(password)) strength++;
            
            // Check for special characters
            if (/[@$!%*?&]/.test(password)) strength++;
            
            // Update progress bar
            strengthBar.className = 'progress-bar';
            
            if (strength === 0) {
                strengthBar.style.width = '0%';
            } else if (strength <= 2) {
                strengthBar.classList.add('weak');
            } else if (strength === 3) {
                strengthBar.classList.add('fair');
            } else if (strength === 4) {
                strengthBar.classList.add('good');
            } else {
                strengthBar.classList.add('strong');
            }
        });
    }
    
    // Username validation
    const usernameInput = document.getElementById('username');
    if (usernameInput) {
        usernameInput.addEventListener('input', function() {
            // Remove spaces and special characters
            this.value = this.value.replace(/[^a-zA-Z0-9_]/g, '');
        });
    }
    
    // Form submission handling
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Add loading state
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;
            
            // Let the main JavaScript handle the actual submission
            StreamingPlatform.handleRegister(e).finally(() => {
                // Remove loading state
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    }
    
    // Auto-focus on first name field
    const firstNameInput = document.getElementById('first_name');
    if (firstNameInput) {
        firstNameInput.focus();
    }
});
</script>
