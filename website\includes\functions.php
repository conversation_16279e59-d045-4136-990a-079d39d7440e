<?php
/**
 * 🛠️ الدوال المساعدة العامة لمنصة البث
 * تحتوي على دوال مفيدة للاستخدام في جميع أنحاء التطبيق
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}

/**
 * ==========================================
 * 🔐 دوال الأمان
 * ==========================================
 */

/**
 * تنظيف وتأمين البيانات المدخلة
 */
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من قوة كلمة المرور
 */
function isStrongPassword($password) {
    // على الأقل 8 أحرف، حرف كبير، حرف صغير، رقم، رمز خاص
    return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/', $password);
}

/**
 * تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
}

/**
 * التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * توليد رمز عشوائي آمن
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * ==========================================
 * 🌐 دوال URL والتوجيه
 * ==========================================
 */

/**
 * إنشاء URL كامل
 */
function url($path = '') {
    return rtrim(SITE_URL, '/') . '/' . ltrim($path, '/');
}

/**
 * إنشاء URL للأصول (CSS, JS, Images)
 */
function asset($path) {
    return rtrim(ASSETS_URL, '/') . '/' . ltrim($path, '/');
}

/**
 * إنشاء URL للوسائط
 */
function media($path) {
    return rtrim(MEDIA_URL, '/') . '/' . ltrim($path, '/');
}

/**
 * إعادة التوجيه
 */
function redirect($url, $statusCode = 302) {
    header("Location: $url", true, $statusCode);
    exit();
}

/**
 * إعادة التوجيه مع رسالة
 */
function redirectWithMessage($url, $message, $type = 'success') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
    redirect($url);
}

/**
 * ==========================================
 * 📝 دوال الرسائل والإشعارات
 * ==========================================
 */

/**
 * عرض رسالة فلاش
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        
        return [
            'message' => $message,
            'type' => $type
        ];
    }
    return null;
}

/**
 * تسجيل رسالة في السجل
 */
function logMessage($message, $level = 'INFO', $context = []) {
    if (!LOGGING_ENABLED) return;
    
    $logFile = LOG_PATH . 'app_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' ' . json_encode($context) : '';
    $logMessage = "[{$timestamp}] {$level}: {$message}{$contextStr}" . PHP_EOL;
    
    if (!is_dir(LOG_PATH)) {
        mkdir(LOG_PATH, 0755, true);
    }
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * ==========================================
 * 🕒 دوال التاريخ والوقت
 * ==========================================
 */

/**
 * تنسيق التاريخ باللغة العربية
 */
function formatDate($date, $format = 'Y-m-d H:i:s') {
    if (empty($date)) return '';
    
    $timestamp = is_numeric($date) ? $date : strtotime($date);
    return date($format, $timestamp);
}

/**
 * حساب الوقت المنقضي (منذ كم)
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة';
    if ($time < 86400) return floor($time/3600) . ' ساعة';
    if ($time < 2592000) return floor($time/86400) . ' يوم';
    if ($time < 31536000) return floor($time/2592000) . ' شهر';
    
    return floor($time/31536000) . ' سنة';
}

/**
 * تحويل الثواني إلى تنسيق وقت قابل للقراءة
 */
function secondsToTime($seconds) {
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $seconds = $seconds % 60;
    
    if ($hours > 0) {
        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    } else {
        return sprintf('%02d:%02d', $minutes, $seconds);
    }
}

/**
 * ==========================================
 * 📁 دوال الملفات والرفع
 * ==========================================
 */

/**
 * التحقق من نوع الملف المسموح
 */
function isAllowedFileType($filename, $allowedTypes) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, $allowedTypes);
}

/**
 * الحصول على حجم الملف بتنسيق قابل للقراءة
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, 2) . ' ' . $units[$i];
}

/**
 * إنشاء اسم ملف فريد
 */
function generateUniqueFilename($originalName) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $name = pathinfo($originalName, PATHINFO_FILENAME);
    $name = preg_replace('/[^a-zA-Z0-9_-]/', '', $name);
    
    return $name . '_' . time() . '_' . uniqid() . '.' . $extension;
}

/**
 * ==========================================
 * 🎨 دوال العرض والتنسيق
 * ==========================================
 */

/**
 * اقتطاع النص مع إضافة نقاط
 */
function truncateText($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    
    return mb_substr($text, 0, $length) . $suffix;
}

/**
 * تحويل النص إلى slug
 */
function createSlug($text) {
    // تحويل الأحرف العربية إلى إنجليزية
    $arabic = ['ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص', 'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'];
    $english = ['a', 'b', 't', 'th', 'j', 'h', 'kh', 'd', 'th', 'r', 'z', 's', 'sh', 's', 'd', 't', 'th', 'a', 'gh', 'f', 'q', 'k', 'l', 'm', 'n', 'h', 'w', 'y'];
    
    $text = str_replace($arabic, $english, $text);
    $text = strtolower($text);
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    $text = trim($text, '-');
    
    return $text;
}

/**
 * ==========================================
 * 🔢 دوال الأرقام والحسابات
 * ==========================================
 */

/**
 * تنسيق الأرقام باللغة العربية
 */
function formatNumber($number, $decimals = 0) {
    return number_format($number, $decimals, '.', ',');
}

/**
 * تحويل الأرقام إلى أرقام عربية
 */
function toArabicNumbers($string) {
    $english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    
    return str_replace($english, $arabic, $string);
}

/**
 * ==========================================
 * 🎯 دوال مساعدة أخرى
 * ==========================================
 */

/**
 * التحقق من وجود المستخدم في الجلسة
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * الحصول على معلومات المستخدم الحالي
 */
function getCurrentUser() {
    if (!isLoggedIn()) return null;
    
    static $user = null;
    if ($user === null) {
        $user = table('users')->where('id', $_SESSION['user_id'])->first();
    }
    
    return $user;
}

/**
 * التحقق من صلاحية المستخدم
 */
function hasPermission($permission) {
    $user = getCurrentUser();
    if (!$user) return false;
    
    // المدير العام له جميع الصلاحيات
    if ($user['username'] === 'admin') return true;
    
    // التحقق من الصلاحيات من قاعدة البيانات
    // سيتم تطوير هذا لاحقاً
    return false;
}

/**
 * إنشاء CSRF Token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = generateSecureToken();
    }
    return $_SESSION['csrf_token'];
}

/**
 * التحقق من CSRF Token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

?>
