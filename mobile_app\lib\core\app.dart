import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:go_router/go_router.dart';

import 'constants/app_constants.dart';
import 'theme/app_theme.dart';
import 'router/app_router.dart';
import 'services/storage_service.dart';

/// 🚀 التطبيق الرئيسي لمنصة البث الشاملة
class StreamingPlatformApp extends ConsumerWidget {
  const StreamingPlatformApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // مراقبة حالة السمة واللغة
    final themeMode = ref.watch(themeModeProvider);
    final locale = ref.watch(localeProvider);
    
    return MaterialApp.router(
      // معلومات التطبيق
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      
      // التوجيه
      routerConfig: AppRouter.router,
      
      // السمات
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeMode,
      
      // اللغة والترجمة
      locale: locale,
      supportedLocales: AppConstants.supportedLocales,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      
      // إعدادات إضافية
      builder: (context, child) {
        return MediaQuery(
          // تعطيل تكبير الخط من إعدادات النظام
          data: MediaQuery.of(context).copyWith(
            textScaler: const TextScaler.linear(1.0),
          ),
          child: child!,
        );
      },
    );
  }
}

/// 🎨 موفر وضع السمة
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>((ref) {
  return ThemeModeNotifier();
});

class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  ThemeModeNotifier() : super(ThemeMode.dark) {
    _loadThemeMode();
  }

  void _loadThemeMode() {
    final savedTheme = StorageService.instance.getThemeMode();
    switch (savedTheme) {
      case 'light':
        state = ThemeMode.light;
        break;
      case 'dark':
        state = ThemeMode.dark;
        break;
      case 'system':
        state = ThemeMode.system;
        break;
      default:
        state = ThemeMode.dark;
    }
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    state = mode;
    String modeString;
    switch (mode) {
      case ThemeMode.light:
        modeString = 'light';
        break;
      case ThemeMode.dark:
        modeString = 'dark';
        break;
      case ThemeMode.system:
        modeString = 'system';
        break;
    }
    await StorageService.instance.saveThemeMode(modeString);
  }
}

/// 🌍 موفر اللغة
final localeProvider = StateNotifierProvider<LocaleNotifier, Locale>((ref) {
  return LocaleNotifier();
});

class LocaleNotifier extends StateNotifier<Locale> {
  LocaleNotifier() : super(AppConstants.defaultLocale) {
    _loadLocale();
  }

  void _loadLocale() {
    final savedLanguage = StorageService.instance.getLanguage();
    switch (savedLanguage) {
      case 'ar':
        state = const Locale('ar', 'SA');
        break;
      case 'en':
        state = const Locale('en', 'US');
        break;
      case 'fr':
        state = const Locale('fr', 'FR');
        break;
      case 'tr':
        state = const Locale('tr', 'TR');
        break;
      default:
        state = AppConstants.defaultLocale;
    }
  }

  Future<void> setLocale(Locale locale) async {
    state = locale;
    await StorageService.instance.saveLanguage(locale.languageCode);
  }
}

/// 👤 موفر حالة المصادقة
final authStateProvider = StateNotifierProvider<AuthStateNotifier, AuthState>((ref) {
  return AuthStateNotifier();
});

class AuthStateNotifier extends StateNotifier<AuthState> {
  AuthStateNotifier() : super(AuthState.initial()) {
    _checkAuthStatus();
  }

  void _checkAuthStatus() {
    final isLoggedIn = StorageService.instance.isLoggedIn();
    final userData = StorageService.instance.getUserData();
    
    if (isLoggedIn && userData != null) {
      state = AuthState.authenticated(userData);
    } else {
      state = AuthState.unauthenticated();
    }
  }

  Future<void> login(Map<String, dynamic> userData, String token) async {
    await StorageService.instance.saveUserData(userData);
    await StorageService.instance.saveUserToken(token);
    state = AuthState.authenticated(userData);
  }

  Future<void> logout() async {
    await StorageService.instance.logout();
    state = AuthState.unauthenticated();
  }

  void updateUserData(Map<String, dynamic> userData) {
    StorageService.instance.saveUserData(userData);
    state = AuthState.authenticated(userData);
  }
}

/// 🔐 حالة المصادقة
class AuthState {
  final bool isAuthenticated;
  final Map<String, dynamic>? userData;
  final bool isLoading;

  const AuthState({
    required this.isAuthenticated,
    this.userData,
    this.isLoading = false,
  });

  factory AuthState.initial() {
    return const AuthState(
      isAuthenticated: false,
      isLoading: true,
    );
  }

  factory AuthState.authenticated(Map<String, dynamic> userData) {
    return AuthState(
      isAuthenticated: true,
      userData: userData,
      isLoading: false,
    );
  }

  factory AuthState.unauthenticated() {
    return const AuthState(
      isAuthenticated: false,
      userData: null,
      isLoading: false,
    );
  }

  factory AuthState.loading() {
    return const AuthState(
      isAuthenticated: false,
      userData: null,
      isLoading: true,
    );
  }

  AuthState copyWith({
    bool? isAuthenticated,
    Map<String, dynamic>? userData,
    bool? isLoading,
  }) {
    return AuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      userData: userData ?? this.userData,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

/// 🌐 موفر حالة الاتصال
final connectivityProvider = StateNotifierProvider<ConnectivityNotifier, bool>((ref) {
  return ConnectivityNotifier();
});

class ConnectivityNotifier extends StateNotifier<bool> {
  ConnectivityNotifier() : super(true) {
    _initConnectivity();
  }

  void _initConnectivity() {
    // سيتم تطوير هذا لاحقاً مع connectivity_plus
    // للتحقق من حالة الاتصال بالإنترنت
  }

  void setConnectivity(bool isConnected) {
    state = isConnected;
  }
}

/// ⚙️ موفر الإعدادات العامة
final appSettingsProvider = StateNotifierProvider<AppSettingsNotifier, AppSettings>((ref) {
  return AppSettingsNotifier();
});

class AppSettingsNotifier extends StateNotifier<AppSettings> {
  AppSettingsNotifier() : super(AppSettings.initial()) {
    _loadSettings();
  }

  void _loadSettings() {
    final storage = StorageService.instance;
    
    state = AppSettings(
      autoPlayEnabled: storage.isAutoPlayEnabled(),
      subtitlesEnabled: storage.isSubtitlesEnabled(),
      biometricEnabled: storage.isBiometricEnabled(),
      preferredVideoQuality: storage.getPreferredVideoQuality(),
      preferredPlaybackSpeed: storage.getPreferredPlaybackSpeed(),
    );
  }

  Future<void> setAutoPlay(bool enabled) async {
    await StorageService.instance.setAutoPlayEnabled(enabled);
    state = state.copyWith(autoPlayEnabled: enabled);
  }

  Future<void> setSubtitles(bool enabled) async {
    await StorageService.instance.setSubtitlesEnabled(enabled);
    state = state.copyWith(subtitlesEnabled: enabled);
  }

  Future<void> setBiometric(bool enabled) async {
    await StorageService.instance.setBiometricEnabled(enabled);
    state = state.copyWith(biometricEnabled: enabled);
  }

  Future<void> setVideoQuality(String quality) async {
    await StorageService.instance.savePreferredVideoQuality(quality);
    state = state.copyWith(preferredVideoQuality: quality);
  }

  Future<void> setPlaybackSpeed(double speed) async {
    await StorageService.instance.savePreferredPlaybackSpeed(speed);
    state = state.copyWith(preferredPlaybackSpeed: speed);
  }
}

/// ⚙️ إعدادات التطبيق
class AppSettings {
  final bool autoPlayEnabled;
  final bool subtitlesEnabled;
  final bool biometricEnabled;
  final String preferredVideoQuality;
  final double preferredPlaybackSpeed;

  const AppSettings({
    required this.autoPlayEnabled,
    required this.subtitlesEnabled,
    required this.biometricEnabled,
    required this.preferredVideoQuality,
    required this.preferredPlaybackSpeed,
  });

  factory AppSettings.initial() {
    return const AppSettings(
      autoPlayEnabled: AppConstants.autoPlayEnabled,
      subtitlesEnabled: AppConstants.subtitlesEnabled,
      biometricEnabled: false,
      preferredVideoQuality: AppConstants.defaultVideoQuality,
      preferredPlaybackSpeed: AppConstants.defaultPlaybackSpeed,
    );
  }

  AppSettings copyWith({
    bool? autoPlayEnabled,
    bool? subtitlesEnabled,
    bool? biometricEnabled,
    String? preferredVideoQuality,
    double? preferredPlaybackSpeed,
  }) {
    return AppSettings(
      autoPlayEnabled: autoPlayEnabled ?? this.autoPlayEnabled,
      subtitlesEnabled: subtitlesEnabled ?? this.subtitlesEnabled,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      preferredVideoQuality: preferredVideoQuality ?? this.preferredVideoQuality,
      preferredPlaybackSpeed: preferredPlaybackSpeed ?? this.preferredPlaybackSpeed,
    );
  }
}
