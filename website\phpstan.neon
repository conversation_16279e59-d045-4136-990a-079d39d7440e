# 🔍 إعدادات PHPStan للتحليل الثابت - منصة البث العربية

parameters:
    # مستوى التحليل (0-9)
    level: 8
    
    # المسارات للتحليل
    paths:
        - src
        - includes
        - public
    
    # المسارات المستبعدة
    excludePaths:
        - vendor
        - cache
        - logs
        - tests
        - coverage
        - node_modules
        - includes/config.php
    
    # ملفات Bootstrap
    bootstrapFiles:
        - vendor/autoload.php
        - includes/functions.php
    
    # إعدادات PHP
    phpVersion: 80100 # PHP 8.1
    
    # تجاهل الأخطاء
    ignoreErrors:
        # تجاهل أخطاء المتغيرات العامة
        - '#Variable \$_[A-Z]+ might not be defined#'
        
        # تجاهل أخطاء الدوال المدمجة
        - '#Function [a-z_]+ not found#'
        
        # تجاهل أخطاء الكلاسات الخارجية
        - '#Class [A-Za-z\\]+ not found#'
        
        # تجاهل أخطاء المصفوفات المختلطة
        - '#Cannot access offset .* on mixed#'
        
        # تجاهل أخطاء النتائج المختلطة
        - '#Method .* should return .* but returns mixed#'
    
    # إعدادات التحقق
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
    checkMissingCallableSignature: true
    checkMissingVarDocTypeHint: true
    checkArgumentsPassedByReference: true
    checkMaybeUndefinedVariables: true
    checkNullables: true
    checkThisOnly: false
    checkUnionTypes: true
    checkExplicitMixed: false
    checkFunctionNameCase: true
    checkInternalClassCaseSensitivity: true
    
    # إعدادات التقارير
    reportUnmatchedIgnoredErrors: false
    reportMaybes: true
    reportStaticMethodSignatures: true
    reportMagicMethods: true
    reportMagicProperties: true
    
    # إعدادات الذاكرة والأداء
    memoryLimitFile: .phpstan-memory-limit
    tmpDir: var/cache/phpstan
    
    # إعدادات الكتابة الديناميكية
    dynamicConstantNames:
        - APP_*
        - DB_*
        - CACHE_*
        - SESSION_*
        - MAIL_*
        - API_*
        - UPLOAD_*
        - LOG_*
    
    # إعدادات الثوابت العامة
    universalObjectCratesClasses:
        - stdClass
        - SimpleXMLElement
    
    # إعدادات الاستثناءات
    exceptions:
        implicitThrows: false
        checkedExceptionClasses: []
        check:
            missingCheckedExceptionInThrows: true
            tooWideThrowType: true
        
    # إعدادات التحليل المتقدم
    strictRules:
        allRules: false
        booleansInConditions: true
        uselessCast: true
        requireParentConstructorCall: true
        disallowedLooseComparison: true
        booleansInConditions: true
        numericOperandsInArithmeticOperators: true
        strictCalls: true
        switchConditionsMatchingType: true
        noVariableVariables: false
    
    # إعدادات الملفات المؤقتة
    fileExtensions:
        - php
        - phtml
        - inc
    
    # إعدادات المكتبات الخارجية
    scanFiles:
        - includes/constants.php
        - includes/helpers.php
    
    # إعدادات التحليل المخصص
    customRulesetUsed: true
    
    # إعدادات الكلاسات المجردة
    treatPhpDocTypesAsCertain: false
    
    # إعدادات التحقق من النوع
    checkAlwaysTrueCheckTypeFunctionCall: true
    checkAlwaysTrueInstanceof: true
    checkAlwaysTrueStrictComparison: true
    checkClassCaseSensitivity: true
    checkFunctionArgumentTypes: true
    checkFunctionNameCase: true
    checkInternalClassCaseSensitivity: true
    checkMissingTypehints: true
    checkTooWideReturnTypesInProtectedAndPublicMethods: true
    checkUninitializedProperties: true
    checkDynamicProperties: true
    
    # إعدادات التحليل الإضافية
    parallel:
        jobSize: 20
        maximumNumberOfProcesses: 32
        minimumNumberOfJobsPerProcess: 2
    
    # إعدادات التخزين المؤقت
    resultCachePath: var/cache/phpstan/resultCache.php
    resultCacheChecksProjectExtensionFilesDependencies: true
    
    # إعدادات التصدير
    exportUnresolvableParameterTypes: false

# إعدادات الخدمات
services:
    # خدمات مخصصة للمشروع
    -
        class: StreamingPlatform\PHPStan\CustomTypeSpecifyingExtension
        tags:
            - phpstan.typeSpecifier.methodTypeSpecifyingExtension
    
    # خدمات التحليل المتقدم
    -
        class: StreamingPlatform\PHPStan\DatabaseQueryExtension
        tags:
            - phpstan.broker.dynamicMethodReturnTypeExtension

# إعدادات القواعد المخصصة
rules:
    # قواعد مخصصة للمشروع
    - StreamingPlatform\PHPStan\Rules\NoEchoRule
    - StreamingPlatform\PHPStan\Rules\NoExitRule
    - StreamingPlatform\PHPStan\Rules\NoGlobalVariablesRule
    - StreamingPlatform\PHPStan\Rules\RequireStrictTypesRule

# إعدادات الإضافات
includes:
    # إضافات PHPStan الأساسية
    - vendor/phpstan/phpstan-strict-rules/rules.neon
    - vendor/phpstan/phpstan-deprecation-rules/rules.neon
    
    # إضافات خاصة بالمكتبات
    - vendor/phpstan/phpstan-doctrine/extension.neon
    - vendor/phpstan/phpstan-symfony/extension.neon
    
    # إضافات مخصصة للمشروع
    - phpstan-baseline.neon

# إعدادات المعاملات المخصصة
parametersSchema:
    customParameters: arrayOf(mixed())
    projectSpecificSettings: structure([
        enableCustomRules: bool()
        strictMode: bool()
        performanceOptimizations: bool()
    ])
