import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/constants/app_constants.dart';
import '../../core/app.dart';

/// ⚙️ شاشة الإعدادات
class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeModeProvider);
    final locale = ref.watch(localeProvider);
    final appSettings = ref.watch(appSettingsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
        elevation: 0,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // إعدادات العرض
          _buildSectionHeader('إعدادات العرض'),
          _buildSettingsCard([
            _buildThemeSelector(context, ref, themeMode),
            _buildLanguageSelector(context, ref, locale),
          ]),

          const SizedBox(height: 24),

          // إعدادات الفيديو
          _buildSectionHeader('إعدادات الفيديو'),
          _buildSettingsCard([
            _buildSwitchTile(
              icon: Icons.play_arrow,
              title: 'التشغيل التلقائي',
              subtitle: 'تشغيل الفيديو تلقائياً عند فتحه',
              value: appSettings.autoPlayEnabled,
              onChanged: (value) {
                ref.read(appSettingsProvider.notifier).setAutoPlay(value);
              },
            ),
            _buildSwitchTile(
              icon: Icons.subtitles,
              title: 'الترجمة',
              subtitle: 'عرض الترجمة افتراضياً',
              value: appSettings.subtitlesEnabled,
              onChanged: (value) {
                ref.read(appSettingsProvider.notifier).setSubtitles(value);
              },
            ),
            _buildVideoQualitySelector(context, ref, appSettings),
            _buildPlaybackSpeedSelector(context, ref, appSettings),
          ]),

          const SizedBox(height: 24),

          // إعدادات الأمان
          _buildSectionHeader('الأمان والخصوصية'),
          _buildSettingsCard([
            _buildSwitchTile(
              icon: Icons.fingerprint,
              title: 'البصمة/Face ID',
              subtitle: 'استخدام البصمة لتسجيل الدخول',
              value: appSettings.biometricEnabled,
              onChanged: (value) {
                ref.read(appSettingsProvider.notifier).setBiometric(value);
              },
            ),
            _buildListTile(
              icon: Icons.lock,
              title: 'تغيير كلمة المرور',
              subtitle: 'تحديث كلمة مرور حسابك',
              onTap: () {
                // تغيير كلمة المرور
              },
            ),
            _buildListTile(
              icon: Icons.privacy_tip,
              title: 'إعدادات الخصوصية',
              subtitle: 'التحكم في خصوصية بياناتك',
              onTap: () {
                // إعدادات الخصوصية
              },
            ),
          ]),

          const SizedBox(height: 24),

          // إعدادات الإشعارات
          _buildSectionHeader('الإشعارات'),
          _buildSettingsCard([
            _buildSwitchTile(
              icon: Icons.notifications,
              title: 'الإشعارات',
              subtitle: 'تلقي إشعارات التطبيق',
              value: true,
              onChanged: (value) {
                // تفعيل/تعطيل الإشعارات
              },
            ),
            _buildSwitchTile(
              icon: Icons.new_releases,
              title: 'المحتوى الجديد',
              subtitle: 'إشعارات عند إضافة محتوى جديد',
              value: true,
              onChanged: (value) {
                // إشعارات المحتوى الجديد
              },
            ),
            _buildSwitchTile(
              icon: Icons.favorite,
              title: 'المفضلة',
              subtitle: 'إشعارات عن المحتوى المفضل',
              value: false,
              onChanged: (value) {
                // إشعارات المفضلة
              },
            ),
          ]),

          const SizedBox(height: 24),

          // إعدادات التحميل
          _buildSectionHeader('التحميل والتخزين'),
          _buildSettingsCard([
            _buildListTile(
              icon: Icons.download,
              title: 'جودة التحميل',
              subtitle: 'HD (720p)',
              onTap: () {
                _showDownloadQualityDialog(context);
              },
            ),
            _buildSwitchTile(
              icon: Icons.wifi,
              title: 'التحميل بالواي فاي فقط',
              subtitle: 'تحميل المحتوى عند الاتصال بالواي فاي',
              value: true,
              onChanged: (value) {
                // إعداد التحميل بالواي فاي
              },
            ),
            _buildListTile(
              icon: Icons.storage,
              title: 'إدارة التخزين',
              subtitle: 'مسح الملفات المؤقتة والتحميلات',
              onTap: () {
                _showStorageDialog(context);
              },
            ),
          ]),

          const SizedBox(height: 24),

          // معلومات التطبيق
          _buildSectionHeader('معلومات التطبيق'),
          _buildSettingsCard([
            _buildListTile(
              icon: Icons.info,
              title: 'حول التطبيق',
              subtitle: 'الإصدار ${AppConstants.appVersion}',
              onTap: () {
                // حول التطبيق
              },
            ),
            _buildListTile(
              icon: Icons.help,
              title: 'المساعدة والدعم',
              subtitle: 'الأسئلة الشائعة والتواصل',
              onTap: () {
                // المساعدة
              },
            ),
            _buildListTile(
              icon: Icons.rate_review,
              title: 'تقييم التطبيق',
              subtitle: 'شاركنا رأيك في المتجر',
              onTap: () {
                // تقييم التطبيق
              },
            ),
          ]),

          const SizedBox(height: 100), // مساحة للتنقل السفلي
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppConstants.primaryColor,
        ),
      ),
    );
  }

  Widget _buildSettingsCard(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: AppConstants.secondaryColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppConstants.cardShadow,
      ),
      child: Column(
        children: children,
      ),
    );
  }

  Widget _buildListTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Widget? trailing,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppConstants.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(
          icon,
          color: AppConstants.primaryColor,
          size: 20,
        ),
      ),
      title: Text(title),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          color: AppConstants.textMutedColor,
          fontSize: 12,
        ),
      ),
      trailing: trailing ?? const Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppConstants.textMutedColor,
      ),
      onTap: onTap,
    );
  }

  Widget _buildSwitchTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppConstants.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(
          icon,
          color: AppConstants.primaryColor,
          size: 20,
        ),
      ),
      title: Text(title),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          color: AppConstants.textMutedColor,
          fontSize: 12,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildThemeSelector(BuildContext context, WidgetRef ref, ThemeMode themeMode) {
    return _buildListTile(
      icon: Icons.palette,
      title: 'السمة',
      subtitle: _getThemeLabel(themeMode),
      onTap: () {
        _showThemeDialog(context, ref, themeMode);
      },
    );
  }

  Widget _buildLanguageSelector(BuildContext context, WidgetRef ref, Locale locale) {
    return _buildListTile(
      icon: Icons.language,
      title: 'اللغة',
      subtitle: _getLanguageLabel(locale),
      onTap: () {
        _showLanguageDialog(context, ref, locale);
      },
    );
  }

  Widget _buildVideoQualitySelector(BuildContext context, WidgetRef ref, AppSettings settings) {
    return _buildListTile(
      icon: Icons.hd,
      title: 'جودة الفيديو الافتراضية',
      subtitle: settings.preferredVideoQuality,
      onTap: () {
        _showVideoQualityDialog(context, ref, settings.preferredVideoQuality);
      },
    );
  }

  Widget _buildPlaybackSpeedSelector(BuildContext context, WidgetRef ref, AppSettings settings) {
    return _buildListTile(
      icon: Icons.speed,
      title: 'سرعة التشغيل الافتراضية',
      subtitle: '${settings.preferredPlaybackSpeed}x',
      onTap: () {
        _showPlaybackSpeedDialog(context, ref, settings.preferredPlaybackSpeed);
      },
    );
  }

  String _getThemeLabel(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'فاتح';
      case ThemeMode.dark:
        return 'داكن';
      case ThemeMode.system:
        return 'تلقائي';
    }
  }

  String _getLanguageLabel(Locale locale) {
    switch (locale.languageCode) {
      case 'ar':
        return 'العربية';
      case 'en':
        return 'English';
      case 'fr':
        return 'Français';
      case 'tr':
        return 'Türkçe';
      default:
        return 'العربية';
    }
  }

  void _showThemeDialog(BuildContext context, WidgetRef ref, ThemeMode currentTheme) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر السمة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<ThemeMode>(
              title: const Text('فاتح'),
              value: ThemeMode.light,
              groupValue: currentTheme,
              onChanged: (value) {
                if (value != null) {
                  ref.read(themeModeProvider.notifier).setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('داكن'),
              value: ThemeMode.dark,
              groupValue: currentTheme,
              onChanged: (value) {
                if (value != null) {
                  ref.read(themeModeProvider.notifier).setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('تلقائي'),
              value: ThemeMode.system,
              groupValue: currentTheme,
              onChanged: (value) {
                if (value != null) {
                  ref.read(themeModeProvider.notifier).setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showLanguageDialog(BuildContext context, WidgetRef ref, Locale currentLocale) {
    final languages = [
      {'code': 'ar', 'name': 'العربية', 'locale': const Locale('ar', 'SA')},
      {'code': 'en', 'name': 'English', 'locale': const Locale('en', 'US')},
      {'code': 'fr', 'name': 'Français', 'locale': const Locale('fr', 'FR')},
      {'code': 'tr', 'name': 'Türkçe', 'locale': const Locale('tr', 'TR')},
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر اللغة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: languages.map((lang) {
            return RadioListTile<Locale>(
              title: Text(lang['name'] as String),
              value: lang['locale'] as Locale,
              groupValue: currentLocale,
              onChanged: (value) {
                if (value != null) {
                  ref.read(localeProvider.notifier).setLocale(value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showVideoQualityDialog(BuildContext context, WidgetRef ref, String currentQuality) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('جودة الفيديو'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AppConstants.videoQualities.map((quality) {
            return RadioListTile<String>(
              title: Text(quality),
              value: quality,
              groupValue: currentQuality,
              onChanged: (value) {
                if (value != null) {
                  ref.read(appSettingsProvider.notifier).setVideoQuality(value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showPlaybackSpeedDialog(BuildContext context, WidgetRef ref, double currentSpeed) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سرعة التشغيل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AppConstants.videoPlaybackSpeeds.map((speed) {
            return RadioListTile<double>(
              title: Text('${speed}x'),
              value: speed,
              groupValue: currentSpeed,
              onChanged: (value) {
                if (value != null) {
                  ref.read(appSettingsProvider.notifier).setPlaybackSpeed(value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showDownloadQualityDialog(BuildContext context) {
    // تنفيذ حوار جودة التحميل
  }

  void _showStorageDialog(BuildContext context) {
    // تنفيذ حوار إدارة التخزين
  }
}
