import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../app.dart';
import '../../features/splash/splash_screen.dart';
import '../../features/onboarding/onboarding_screen.dart';
import '../../features/auth/login_screen.dart';
import '../../features/auth/register_screen.dart';
import '../../features/home/<USER>';
import '../../features/main/main_screen.dart';
import '../../features/content/content_details_screen.dart';
import '../../features/video_player/video_player_screen.dart';
import '../../features/search/search_screen.dart';
import '../../features/profile/profile_screen.dart';
import '../../features/settings/settings_screen.dart';

/// 🧭 نظام التوجيه للتطبيق
class AppRouter {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final _shellNavigatorKey = GlobalKey<NavigatorState>();

  static final GoRouter router = GoRouter(
    navigatorKey: _rootNavigatorKey,
    initialLocation: '/splash',
    debugLogDiagnostics: true,
    
    // إعادة التوجيه حسب حالة المصادقة
    redirect: (context, state) {
      final container = ProviderScope.containerOf(context);
      final authState = container.read(authStateProvider);
      final isFirstLaunch = container.read(storageServiceProvider).isFirstLaunch();
      
      final location = state.uri.path;
      
      // إذا كان في صفحة البداية، لا تعيد التوجيه
      if (location == '/splash') {
        return null;
      }
      
      // إذا كان التشغيل الأول، اذهب للتعريف بالتطبيق
      if (isFirstLaunch && location != '/onboarding') {
        return '/onboarding';
      }
      
      // إذا لم يكن مسجل دخول وليس في صفحات المصادقة
      if (!authState.isAuthenticated && 
          !location.startsWith('/auth') && 
          location != '/onboarding') {
        return '/auth/login';
      }
      
      // إذا كان مسجل دخول وفي صفحات المصادقة
      if (authState.isAuthenticated && location.startsWith('/auth')) {
        return '/home';
      }
      
      return null;
    },
    
    routes: [
      // ==========================================
      // 🚀 صفحة البداية
      // ==========================================
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),
      
      // ==========================================
      // 👋 التعريف بالتطبيق
      // ==========================================
      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),
      
      // ==========================================
      // 🔐 صفحات المصادقة
      // ==========================================
      GoRoute(
        path: '/auth',
        redirect: (context, state) => '/auth/login',
      ),
      
      GoRoute(
        path: '/auth/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      
      GoRoute(
        path: '/auth/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      
      // ==========================================
      // 🏠 الصفحات الرئيسية مع شريط التنقل
      // ==========================================
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) {
          return MainScreen(child: child);
        },
        routes: [
          GoRoute(
            path: '/home',
            name: 'home',
            builder: (context, state) => const HomeScreen(),
          ),
          
          GoRoute(
            path: '/search',
            name: 'search',
            builder: (context, state) => const SearchScreen(),
          ),
          
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfileScreen(),
          ),
        ],
      ),
      
      // ==========================================
      // 🎬 صفحات المحتوى
      // ==========================================
      GoRoute(
        path: '/content/:id',
        name: 'content_details',
        builder: (context, state) {
          final contentId = state.pathParameters['id']!;
          return ContentDetailsScreen(contentId: contentId);
        },
      ),
      
      // ==========================================
      // 📺 مشغل الفيديو
      // ==========================================
      GoRoute(
        path: '/player/:contentId/:episodeId?',
        name: 'video_player',
        builder: (context, state) {
          final contentId = state.pathParameters['contentId']!;
          final episodeId = state.pathParameters['episodeId'];
          return VideoPlayerScreen(
            contentId: contentId,
            episodeId: episodeId,
          );
        },
      ),
      
      // ==========================================
      // ⚙️ الإعدادات
      // ==========================================
      GoRoute(
        path: '/settings',
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
        routes: [
          GoRoute(
            path: '/account',
            name: 'account_settings',
            builder: (context, state) => const AccountSettingsScreen(),
          ),
          
          GoRoute(
            path: '/video',
            name: 'video_settings',
            builder: (context, state) => const VideoSettingsScreen(),
          ),
          
          GoRoute(
            path: '/notifications',
            name: 'notification_settings',
            builder: (context, state) => const NotificationSettingsScreen(),
          ),
          
          GoRoute(
            path: '/privacy',
            name: 'privacy_settings',
            builder: (context, state) => const PrivacySettingsScreen(),
          ),
        ],
      ),
      
      // ==========================================
      // 📱 صفحات إضافية
      // ==========================================
      GoRoute(
        path: '/downloads',
        name: 'downloads',
        builder: (context, state) => const DownloadsScreen(),
      ),
      
      GoRoute(
        path: '/favorites',
        name: 'favorites',
        builder: (context, state) => const FavoritesScreen(),
      ),
      
      GoRoute(
        path: '/watchlist',
        name: 'watchlist',
        builder: (context, state) => const WatchlistScreen(),
      ),
      
      GoRoute(
        path: '/history',
        name: 'history',
        builder: (context, state) => const HistoryScreen(),
      ),
      
      GoRoute(
        path: '/subscription',
        name: 'subscription',
        builder: (context, state) => const SubscriptionScreen(),
      ),
      
      // ==========================================
      // 📄 صفحات المعلومات
      // ==========================================
      GoRoute(
        path: '/about',
        name: 'about',
        builder: (context, state) => const AboutScreen(),
      ),
      
      GoRoute(
        path: '/help',
        name: 'help',
        builder: (context, state) => const HelpScreen(),
      ),
      
      GoRoute(
        path: '/terms',
        name: 'terms',
        builder: (context, state) => const TermsScreen(),
      ),
      
      GoRoute(
        path: '/privacy-policy',
        name: 'privacy_policy',
        builder: (context, state) => const PrivacyPolicyScreen(),
      ),
    ],
    
    // معالجة الأخطاء
    errorBuilder: (context, state) => ErrorScreen(error: state.error),
  );
}

/// 🔄 موفر خدمة التخزين للتوجيه
final storageServiceProvider = Provider((ref) {
  return StorageService.instance;
});

/// ❌ صفحة الخطأ
class ErrorScreen extends StatelessWidget {
  final GoException? error;
  
  const ErrorScreen({super.key, this.error});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('خطأ'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'حدث خطأ غير متوقع',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            if (error != null)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  error.toString(),
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.grey),
                ),
              ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    );
  }
}

// ==========================================
// 📱 صفحات مؤقتة (سيتم تطويرها لاحقاً)
// ==========================================

class AccountSettingsScreen extends StatelessWidget {
  const AccountSettingsScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('إعدادات الحساب')));
  }
}

class VideoSettingsScreen extends StatelessWidget {
  const VideoSettingsScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('إعدادات الفيديو')));
  }
}

class NotificationSettingsScreen extends StatelessWidget {
  const NotificationSettingsScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('إعدادات الإشعارات')));
  }
}

class PrivacySettingsScreen extends StatelessWidget {
  const PrivacySettingsScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('إعدادات الخصوصية')));
  }
}

class DownloadsScreen extends StatelessWidget {
  const DownloadsScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('التحميلات')));
  }
}

class FavoritesScreen extends StatelessWidget {
  const FavoritesScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('المفضلة')));
  }
}

class WatchlistScreen extends StatelessWidget {
  const WatchlistScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('قائمة المشاهدة')));
  }
}

class HistoryScreen extends StatelessWidget {
  const HistoryScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('سجل المشاهدة')));
  }
}

class SubscriptionScreen extends StatelessWidget {
  const SubscriptionScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('الاشتراك')));
  }
}

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('حول التطبيق')));
  }
}

class HelpScreen extends StatelessWidget {
  const HelpScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('المساعدة')));
  }
}

class TermsScreen extends StatelessWidget {
  const TermsScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('شروط الاستخدام')));
  }
}

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('سياسة الخصوصية')));
  }
}
