<?php
/**
 * 🎛️ لوحة التحكم الإدارية الرئيسية
 * نظرة عامة على إحصائيات المنصة
 */

// تعريف ثابت المنصة
define('STREAMING_PLATFORM', true);

// تحميل ملف التهيئة
require_once dirname(__DIR__) . '/../includes/init.php';

// التحقق من صلاحيات الإدارة
if (!IS_LOGGED_IN || (getCurrentUser()['role'] ?? '') !== 'admin') {
    header('Location: /login');
    exit;
}

// الحصول على الإحصائيات
$stats = [
    'users' => ['value' => 0, 'change' => 0],
    'content' => ['value' => 0, 'change' => 0],
    'views' => ['value' => 0, 'change' => 0],
    'revenue' => ['value' => 0, 'change' => 0]
];

if (DB_CONNECTED) {
    try {
        global $db;
        
        // إحصائيات المستخدمين
        $userCount = $db->selectOne("SELECT COUNT(*) as count FROM users")['count'] ?? 0;
        $stats['users']['value'] = $userCount;
        
        // إحصائيات المحتوى
        $contentCount = $db->selectOne("SELECT COUNT(*) as count FROM content WHERE status = 'published'")['count'] ?? 0;
        $stats['content']['value'] = $contentCount;
        
        // إحصائيات المشاهدات
        $viewsCount = $db->selectOne("SELECT SUM(view_count) as total FROM content")['total'] ?? 0;
        $stats['views']['value'] = $viewsCount;
        
        // أحدث المستخدمين
        $recentUsers = $db->select("SELECT * FROM users ORDER BY created_at DESC LIMIT 5");
        
        // أحدث المحتوى
        $recentContent = $db->select("SELECT * FROM content ORDER BY created_at DESC LIMIT 5");
        
    } catch (Exception $e) {
        error_log("خطأ في تحميل إحصائيات الإدارة: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?php echo SITE_NAME; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <meta name="csrf-token" content="<?php echo csrf_token(); ?>">
</head>
<body>
    <div class="admin-layout">
        <!-- الشريط الجانبي -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <a href="/admin" class="sidebar-logo">
                    🎬 <?php echo SITE_NAME; ?>
                </a>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">الرئيسية</div>
                    <a href="/admin" class="nav-item active">
                        <i class="fas fa-tachometer-alt"></i>
                        لوحة التحكم
                    </a>
                    <a href="/admin/analytics" class="nav-item">
                        <i class="fas fa-chart-bar"></i>
                        التحليلات
                    </a>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">المحتوى</div>
                    <a href="/admin/content" class="nav-item">
                        <i class="fas fa-film"></i>
                        إدارة المحتوى
                    </a>
                    <a href="/admin/content/add" class="nav-item">
                        <i class="fas fa-plus"></i>
                        إضافة محتوى
                    </a>
                    <a href="/admin/categories" class="nav-item">
                        <i class="fas fa-tags"></i>
                        التصنيفات
                    </a>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">المستخدمون</div>
                    <a href="/admin/users" class="nav-item">
                        <i class="fas fa-users"></i>
                        إدارة المستخدمين
                    </a>
                    <a href="/admin/subscriptions" class="nav-item">
                        <i class="fas fa-credit-card"></i>
                        الاشتراكات
                    </a>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">النظام</div>
                    <a href="/admin/settings" class="nav-item">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </a>
                    <a href="/admin/logs" class="nav-item">
                        <i class="fas fa-file-alt"></i>
                        السجلات
                    </a>
                    <a href="/admin/backup" class="nav-item">
                        <i class="fas fa-download"></i>
                        النسخ الاحتياطي
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- المحتوى الرئيسي -->
        <main class="admin-main">
            <!-- الرأس -->
            <header class="admin-header">
                <div class="header-content">
                    <div class="header-left">
                        <button class="sidebar-toggle d-md-none">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="header-title">لوحة التحكم</h1>
                    </div>
                    
                    <div class="header-actions">
                        <div class="header-search">
                            <input type="text" placeholder="البحث...">
                            <i class="fas fa-search"></i>
                        </div>
                        
                        <div class="header-user">
                            <div class="user-avatar">
                                <?php echo substr(getCurrentUser()['first_name'] ?? 'A', 0, 1); ?>
                            </div>
                            <span><?php echo getCurrentUser()['first_name'] ?? 'المدير'; ?></span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- المحتوى -->
            <div class="admin-content">
                <!-- بطاقات الإحصائيات -->
                <div class="stats-grid">
                    <div class="stat-card" data-stat="users" style="--stat-color: #2563eb;">
                        <div class="stat-header">
                            <div class="stat-title">إجمالي المستخدمين</div>
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-value"><?php echo number_format($stats['users']['value']); ?></div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +12% من الشهر الماضي
                        </div>
                    </div>
                    
                    <div class="stat-card" data-stat="content" style="--stat-color: #10b981;">
                        <div class="stat-header">
                            <div class="stat-title">المحتوى المنشور</div>
                            <div class="stat-icon">
                                <i class="fas fa-film"></i>
                            </div>
                        </div>
                        <div class="stat-value"><?php echo number_format($stats['content']['value']); ?></div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +8% من الشهر الماضي
                        </div>
                    </div>
                    
                    <div class="stat-card" data-stat="views" style="--stat-color: #f59e0b;">
                        <div class="stat-header">
                            <div class="stat-title">إجمالي المشاهدات</div>
                            <div class="stat-icon">
                                <i class="fas fa-eye"></i>
                            </div>
                        </div>
                        <div class="stat-value"><?php echo number_format($stats['views']['value']); ?></div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +25% من الشهر الماضي
                        </div>
                    </div>
                    
                    <div class="stat-card" data-stat="revenue" style="--stat-color: #ef4444;">
                        <div class="stat-header">
                            <div class="stat-title">الإيرادات الشهرية</div>
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                        </div>
                        <div class="stat-value">$<?php echo number_format($stats['revenue']['value']); ?></div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +15% من الشهر الماضي
                        </div>
                    </div>
                </div>
                
                <!-- الرسوم البيانية والجداول -->
                <div class="row">
                    <div class="col-md-8">
                        <!-- رسم بياني للمشاهدات -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">المشاهدات خلال الأسبوع</h3>
                            </div>
                            <div class="card-body">
                                <canvas id="viewsChart" height="100"></canvas>
                            </div>
                        </div>
                        
                        <!-- أحدث المحتوى -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h3 class="card-title">أحدث المحتوى</h3>
                                <a href="/admin/content/add" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i>
                                    إضافة محتوى
                                </a>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recentContent)): ?>
                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>العنوان</th>
                                                <th>النوع</th>
                                                <th>الحالة</th>
                                                <th>تاريخ الإضافة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentContent as $item): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($item['title']); ?></td>
                                                <td>
                                                    <span class="badge badge-primary">
                                                        <?php echo $item['type'] === 'movie' ? 'فيلم' : 'مسلسل'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge badge-<?php echo $item['status'] === 'published' ? 'success' : 'warning'; ?>">
                                                        <?php echo $item['status'] === 'published' ? 'منشور' : 'مسودة'; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('Y-m-d', strtotime($item['created_at'])); ?></td>
                                                <td>
                                                    <a href="/admin/content/edit/<?php echo $item['id']; ?>" class="btn btn-sm btn-outline">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-danger btn-delete" 
                                                            data-delete-url="/admin/content/delete/<?php echo $item['id']; ?>"
                                                            data-item-name="<?php echo htmlspecialchars($item['title']); ?>">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php else: ?>
                                <div class="text-center">
                                    <p>لا يوجد محتوى حالياً</p>
                                    <a href="/admin/content/add" class="btn btn-primary">
                                        <i class="fas fa-plus"></i>
                                        إضافة أول محتوى
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <!-- رسم بياني للمستخدمين -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">توزيع المستخدمين</h3>
                            </div>
                            <div class="card-body">
                                <canvas id="usersChart"></canvas>
                            </div>
                        </div>
                        
                        <!-- أحدث المستخدمين -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h3 class="card-title">أحدث المستخدمين</h3>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recentUsers)): ?>
                                <div class="user-list">
                                    <?php foreach ($recentUsers as $user): ?>
                                    <div class="user-item">
                                        <div class="user-avatar">
                                            <?php echo substr($user['first_name'], 0, 1); ?>
                                        </div>
                                        <div class="user-info">
                                            <div class="user-name">
                                                <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                            </div>
                                            <div class="user-email">
                                                <?php echo htmlspecialchars($user['email']); ?>
                                            </div>
                                        </div>
                                        <div class="user-status">
                                            <span class="badge badge-<?php echo $user['status'] === 'active' ? 'success' : 'warning'; ?>">
                                                <?php echo $user['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                            </span>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php else: ?>
                                <p class="text-center">لا يوجد مستخدمون حالياً</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- JavaScript -->
    <script src="/assets/js/admin.js"></script>
    
    <script>
        // بيانات وهمية للرسوم البيانية
        document.addEventListener('DOMContentLoaded', function() {
            // رسم المشاهدات
            const viewsData = {
                labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                values: [1200, 1900, 3000, 5000, 2000, 3000, 4500]
            };
            
            // رسم المستخدمين
            const usersData = {
                labels: ['مجاني', 'أساسي', 'مميز', 'VIP'],
                values: [<?php echo $stats['users']['value'] * 0.6; ?>, 
                        <?php echo $stats['users']['value'] * 0.25; ?>, 
                        <?php echo $stats['users']['value'] * 0.1; ?>, 
                        <?php echo $stats['users']['value'] * 0.05; ?>]
            };
            
            // تحديث الرسوم البيانية
            if (window.AdminDashboard) {
                const dashboard = new AdminDashboard();
                dashboard.updateViewsChart(viewsData);
                dashboard.updateUsersChart(usersData);
            }
        });
    </script>
</body>
</html>
