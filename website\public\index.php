<?php
/**
 * 🏠 الصفحة الرئيسية لمنصة البث الشاملة
 * نقطة الدخول الرئيسية للموقع
 */

// تحميل ملف التهيئة
require_once dirname(__FILE__) . '/../includes/init.php';

// إنشاء موجه جديد
$router = new Router();

// ==========================================
// 🏠 الصفحة الرئيسية
// ==========================================
$router->get('/', function() {
    try {
        // الحصول على المحتوى المميز
        $featuredContent = table('content')
            ->select(['id', 'title', 'slug', 'description', 'poster', 'banner', 'type', 'rating', 'release_date'])
            ->where('is_featured', 1)
            ->where('is_active', 1)
            ->orderBy('created_at', 'DESC')
            ->limit(10)
            ->get();
        
        // الحصول على المحتوى الأكثر مشاهدة
        $trendingContent = table('content')
            ->select(['id', 'title', 'slug', 'description', 'poster', 'type', 'rating', 'views_count'])
            ->where('is_trending', 1)
            ->where('is_active', 1)
            ->orderBy('views_count', 'DESC')
            ->limit(12)
            ->get();
        
        // الحصول على أحدث المحتوى
        $latestContent = table('content')
            ->select(['id', 'title', 'slug', 'description', 'poster', 'type', 'rating', 'release_date'])
            ->where('is_active', 1)
            ->orderBy('created_at', 'DESC')
            ->limit(12)
            ->get();
        
        // الحصول على التصنيفات
        $categories = table('categories')
            ->select(['id', 'name', 'slug', 'image'])
            ->where('is_active', 1)
            ->where('parent_id', null)
            ->orderBy('sort_order', 'ASC')
            ->get();
        
        // إحصائيات سريعة
        $stats = [
            'total_content' => table('content')->where('is_active', 1)->count(),
            'total_users' => table('users')->where('is_active', 1)->count(),
            'total_views' => table('content')->where('is_active', 1)->get()[0]['views_count'] ?? 0
        ];
        
        // عرض الصفحة
        view('home', [
            'featuredContent' => $featuredContent,
            'trendingContent' => $trendingContent,
            'latestContent' => $latestContent,
            'categories' => $categories,
            'stats' => $stats,
            'pageTitle' => DynamicSettings::get('site_name', 'منصة البث الشاملة'),
            'pageDescription' => DynamicSettings::get('site_description', 'شاهد أحدث الأفلام والمسلسلات')
        ]);
        
    } catch (Exception $e) {
        logMessage('Error loading homepage: ' . $e->getMessage(), 'ERROR');
        view('error', ['message' => 'حدث خطأ في تحميل الصفحة']);
    }
});

// ==========================================
// 📱 صفحة تسجيل الدخول
// ==========================================
$router->get('/login', function() {
    // إذا كان المستخدم مسجل دخول، إعادة توجيه للرئيسية
    if (isLoggedIn()) {
        redirect('/');
    }
    
    view('auth/login', [
        'pageTitle' => 'تسجيل الدخول',
        'googleLoginEnabled' => DynamicSettings::get('social_login_google', true),
        'facebookLoginEnabled' => DynamicSettings::get('social_login_facebook', true)
    ]);
});

$router->post('/login', function() {
    try {
        // التحقق من CSRF
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            jsonError('Invalid CSRF token', 403);
        }
        
        $email = sanitize($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $remember = isset($_POST['remember']);
        
        // التحقق من البيانات
        if (empty($email) || empty($password)) {
            jsonError('البريد الإلكتروني وكلمة المرور مطلوبان');
        }
        
        if (!isValidEmail($email)) {
            jsonError('البريد الإلكتروني غير صحيح');
        }
        
        // التحقق من قفل الحساب
        if (Security::isAccountLocked($email)) {
            jsonError('تم قفل الحساب مؤقتاً بسبب محاولات تسجيل دخول فاشلة متعددة');
        }
        
        // البحث عن المستخدم
        $user = table('users')->where('email', $email)->where('is_active', 1)->first();
        
        if (!$user || !verifyPassword($password, $user['password_hash'])) {
            // تسجيل محاولة فاشلة
            Security::logFailedLogin($email);
            jsonError('البريد الإلكتروني أو كلمة المرور غير صحيحة');
        }
        
        // التحقق من تفعيل الحساب
        if (!$user['is_verified'] && DynamicSettings::get('email_verification_required', true)) {
            jsonError('يجب تفعيل حسابك أولاً. تحقق من بريدك الإلكتروني');
        }
        
        // إعادة تعيين محاولات تسجيل الدخول
        Security::resetLoginAttempts($email);
        
        // إنشاء الجلسة
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_type'] = $user['subscription_type'];
        
        // تحديث آخر تسجيل دخول
        $db = Database::getInstance();
        $stmt = $db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$user['id']]);
        
        // تسجيل النشاط
        $app = App::getInstance();
        $app->logActivity('user_login', 'User logged in successfully');
        
        jsonSuccess(['redirect' => '/dashboard'], 'تم تسجيل الدخول بنجاح');
        
    } catch (Exception $e) {
        logMessage('Login error: ' . $e->getMessage(), 'ERROR');
        jsonError('حدث خطأ في تسجيل الدخول');
    }
});

// ==========================================
// 📝 صفحة التسجيل
// ==========================================
$router->get('/register', function() {
    if (isLoggedIn()) {
        redirect('/');
    }
    
    // التحقق من تفعيل التسجيل
    if (!DynamicSettings::get('registration_enabled', true)) {
        view('error', ['message' => 'التسجيل غير متاح حالياً']);
        return;
    }
    
    view('auth/register', [
        'pageTitle' => 'إنشاء حساب جديد',
        'googleLoginEnabled' => DynamicSettings::get('social_login_google', true),
        'facebookLoginEnabled' => DynamicSettings::get('social_login_facebook', true)
    ]);
});

$router->post('/register', function() {
    try {
        // التحقق من تفعيل التسجيل
        if (!DynamicSettings::get('registration_enabled', true)) {
            jsonError('التسجيل غير متاح حالياً');
        }
        
        // التحقق من CSRF
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            jsonError('Invalid CSRF token', 403);
        }
        
        $username = sanitize($_POST['username'] ?? '');
        $email = sanitize($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        $firstName = sanitize($_POST['first_name'] ?? '');
        $lastName = sanitize($_POST['last_name'] ?? '');
        
        // التحقق من البيانات
        $errors = [];
        
        if (empty($username)) $errors['username'] = 'اسم المستخدم مطلوب';
        if (empty($email)) $errors['email'] = 'البريد الإلكتروني مطلوب';
        if (empty($password)) $errors['password'] = 'كلمة المرور مطلوبة';
        if (empty($firstName)) $errors['first_name'] = 'الاسم الأول مطلوب';
        
        if (!isValidEmail($email)) {
            $errors['email'] = 'البريد الإلكتروني غير صحيح';
        }
        
        if (!isStrongPassword($password)) {
            $errors['password'] = 'كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل مع حرف كبير وصغير ورقم ورمز خاص';
        }
        
        if ($password !== $confirmPassword) {
            $errors['confirm_password'] = 'كلمات المرور غير متطابقة';
        }
        
        // التحقق من عدم وجود المستخدم
        $existingUser = table('users')
            ->where('email', $email)
            ->first();
        
        if ($existingUser) {
            $errors['email'] = 'البريد الإلكتروني مستخدم بالفعل';
        }
        
        $existingUsername = table('users')
            ->where('username', $username)
            ->first();
        
        if ($existingUsername) {
            $errors['username'] = 'اسم المستخدم مستخدم بالفعل';
        }
        
        if (!empty($errors)) {
            jsonError('يرجى تصحيح الأخطاء التالية', 400, $errors);
        }
        
        // إنشاء المستخدم
        $db = Database::getInstance();
        $db->beginTransaction();
        
        try {
            $verificationToken = generateSecureToken();
            $passwordHash = hashPassword($password);
            
            $stmt = $db->prepare("
                INSERT INTO users (username, email, password_hash, first_name, last_name, verification_token, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $username,
                $email,
                $passwordHash,
                $firstName,
                $lastName,
                $verificationToken
            ]);
            
            $userId = $db->lastInsertId();
            
            // إضافة دور المستخدم العادي
            $userRole = table('roles')->where('name', 'user')->first();
            if ($userRole) {
                $stmt = $db->prepare("INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)");
                $stmt->execute([$userId, $userRole['id']]);
            }
            
            $db->commit();
            
            // إرسال بريد التفعيل (سيتم تطويره لاحقاً)
            // sendVerificationEmail($email, $verificationToken);
            
            // تسجيل النشاط
            $app = App::getInstance();
            $app->logActivity('user_register', 'New user registered', ['user_id' => $userId]);
            
            jsonSuccess([
                'redirect' => '/login',
                'message' => 'تم إنشاء الحساب بنجاح. يرجى تفعيل حسابك من خلال البريد الإلكتروني'
            ]);
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        
    } catch (Exception $e) {
        logMessage('Registration error: ' . $e->getMessage(), 'ERROR');
        jsonError('حدث خطأ في إنشاء الحساب');
    }
});

// ==========================================
// 🚪 تسجيل الخروج
// ==========================================
$router->get('/logout', function() {
    if (isLoggedIn()) {
        $app = App::getInstance();
        $app->logActivity('user_logout', 'User logged out');
        
        // إنهاء الجلسة
        session_destroy();
    }
    
    redirect('/');
});

// ==========================================
// 🎬 صفحة المحتوى
// ==========================================
$router->get('/content/{slug}', function() {
    // سيتم تطوير هذا لاحقاً
    view('content/details');
});

// ==========================================
// 🔍 صفحة البحث
// ==========================================
$router->get('/search', function() {
    // سيتم تطوير هذا لاحقاً
    view('search');
});

// تشغيل الموجه
$router->run();

?>
