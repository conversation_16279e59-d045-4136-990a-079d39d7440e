# ❓ الأسئلة الشائعة - منصة البث العربية

## 🎯 **الأسئلة العامة**

### ❓ **ما هي منصة البث العربية؟**
منصة البث العربية هي منصة مفتوحة المصدر لبث ومشاركة الفيديوهات، مصممة خصيصاً للمحتوى العربي مع دعم كامل للغة العربية وميزات متقدمة.

### ❓ **هل المنصة مجانية؟**
نعم، المنصة مفتوحة المصدر ومجانية بالكامل تحت ترخيص MIT. يمكنك استخدامها وتعديلها بحرية.

### ❓ **ما هي المتطلبات التقنية؟**
- **الخادم**: PHP 8.1+, MySQL 8.0+, Redis 6.0+
- **التطوير**: Node.js 18+, Composer, Git
- **الذاكرة**: 2GB RAM كحد أدنى (4GB موصى)
- **التخزين**: 10GB مساحة فارغة كحد أدنى

### ❓ **هل تدعم المنصة اللغة العربية بالكامل؟**
نعم، المنصة مصممة خصيصاً للمحتوى العربي مع:
- دعم كامل للنصوص العربية (RTL)
- واجهة مستخدم باللغة العربية
- معالجة متقدمة للنصوص العربية
- دعم الخطوط العربية الجميلة

---

## 🔧 **التثبيت والإعداد**

### ❓ **كيف أثبت المنصة؟**
```bash
# الطريقة السريعة
git clone https://github.com/your-repo/arabic-streaming-platform.git
cd arabic-streaming-platform
make quick-start
```

### ❓ **ماذا لو واجهت خطأ في التثبيت؟**
1. **تحقق من المتطلبات**: `make check-requirements`
2. **راجع السجلات**: `tail -f website/logs/app.log`
3. **استخدم أداة التشخيص**: `make troubleshoot`
4. **اطلب المساعدة**: أنشئ Issue في GitHub

### ❓ **كيف أحدث المنصة؟**
```bash
# تحديث تلقائي
make update

# أو يدوياً
git pull origin main
composer update
npm update
php artisan migrate
```

### ❓ **هل يمكنني استخدام Docker؟**
نعم! المنصة تدعم Docker بالكامل:
```bash
docker-compose up -d
```

---

## 👤 **إدارة المستخدمين**

### ❓ **كيف أنشئ حساب مدير؟**
```bash
php artisan make:admin
# اتبع التعليمات لإنشاء حساب المدير
```

### ❓ **كيف أعيد تعيين كلمة مرور المدير؟**
```bash
php artisan admin:reset-password <EMAIL>
```

### ❓ **كيف أدير صلاحيات المستخدمين؟**
1. سجل دخول كمدير
2. اذهب إلى "إدارة المستخدمين"
3. اختر المستخدم وعدل الصلاحيات
4. احفظ التغييرات

### ❓ **هل يمكن للمستخدمين التسجيل تلقائياً؟**
نعم، يمكنك تفعيل/إلغاء التسجيل التلقائي من لوحة الإدارة > الإعدادات > إعدادات المستخدمين.

---

## 📺 **إدارة المحتوى**

### ❓ **ما هي صيغ الفيديو المدعومة؟**
المنصة تدعم جميع الصيغ الشائعة:
- **MP4** (موصى)
- **WebM**
- **AVI**
- **MOV**
- **MKV**

### ❓ **ما هو الحد الأقصى لحجم الفيديو؟**
الحد الافتراضي هو 100MB، يمكنك تغييره في:
- ملف `.env`: `MAX_UPLOAD_SIZE=500`
- إعدادات PHP: `upload_max_filesize`

### ❓ **كيف أرفع فيديو؟**
1. سجل دخول كمدير أو مستخدم مخول
2. اذهب إلى "رفع فيديو"
3. اختر الملف واملأ البيانات
4. اضغط "رفع ونشر"

### ❓ **هل تدعم المنصة الترجمة؟**
نعم، يمكنك رفع ملفات الترجمة بصيغ:
- **SRT**
- **VTT**
- **ASS**

### ❓ **كيف أنظم الفيديوهات في فئات؟**
1. اذهب إلى "إدارة الفئات"
2. أنشئ فئات جديدة
3. عند رفع الفيديو، اختر الفئة المناسبة

---

## 🎨 **التخصيص والمظهر**

### ❓ **كيف أغير شعار المنصة؟**
1. ارفع الشعار الجديد إلى `website/public/assets/images/`
2. حدث ملف `website/includes/config.php`
3. أو من لوحة الإدارة > الإعدادات > المظهر

### ❓ **كيف أخصص الألوان؟**
عدل ملف `website/public/assets/css/main.css`:
```css
:root {
  --primary-color: #your-color;
  --secondary-color: #your-color;
}
```

### ❓ **هل يمكنني إضافة صفحات مخصصة؟**
نعم، أنشئ ملفات PHP جديدة في `website/public/views/` واربطها في القائمة.

### ❓ **كيف أغير الخط المستخدم؟**
1. أضف الخط إلى `website/public/assets/css/fonts/`
2. حدث ملف CSS:
```css
@font-face {
  font-family: 'YourFont';
  src: url('fonts/yourfont.woff2');
}
```

---

## 🔒 **الأمان والحماية**

### ❓ **كيف أحمي المنصة من الهجمات؟**
المنصة تتضمن حماية متقدمة:
- **CSRF Protection**
- **XSS Prevention**
- **SQL Injection Protection**
- **Rate Limiting**
- **Secure Headers**

### ❓ **كيف أفعل HTTPS؟**
1. احصل على شهادة SSL (Let's Encrypt مجاني)
2. حدث إعدادات الخادم
3. عدل `APP_URL` في `.env`

### ❓ **كيف أعمل نسخة احتياطية؟**
```bash
# نسخة احتياطية تلقائية
make backup

# نسخة احتياطية يدوية
./backup.sh
```

### ❓ **كيف أستعيد النسخة الاحتياطية؟**
```bash
make restore backup-file.tar.gz
```

---

## 📱 **التطبيق الجوال**

### ❓ **هل يوجد تطبيق جوال؟**
نعم، المنصة تتضمن:
- **تطبيق Flutter** متعدد المنصات
- **تطبيق PWA** للمتصفحات
- **تطبيقات أصلية** لـ iOS و Android

### ❓ **كيف أبني التطبيق الجوال؟**
```bash
cd mobile_app
flutter pub get
flutter build apk  # للأندرويد
flutter build ios  # للآيفون
```

### ❓ **هل التطبيق متاح في المتاجر؟**
يمكنك نشر التطبيق في متاجر التطبيقات بعد تخصيصه لمشروعك.

---

## ☁️ **النشر والاستضافة**

### ❓ **أين يمكنني استضافة المنصة؟**
المنصة تعمل على:
- **الخوادم المحلية**
- **VPS/Dedicated Servers**
- **السحابة**: AWS, Google Cloud, Azure
- **الاستضافة المشتركة** (مع قيود)

### ❓ **ما هي متطلبات الخادم؟**
- **CPU**: 2 cores كحد أدنى
- **RAM**: 4GB كحد أدنى
- **Storage**: 50GB SSD
- **Bandwidth**: 100Mbps

### ❓ **كيف أنشر على AWS؟**
```bash
make aws-deploy
```

### ❓ **هل تدعم المنصة Kubernetes؟**
نعم، المنصة تتضمن ملفات Kubernetes كاملة:
```bash
make k8s-deploy
```

---

## 🤖 **الذكاء الاصطناعي**

### ❓ **ما هي ميزات الذكاء الاصطناعي؟**
- **نظام التوصيات الذكي**
- **معالجة اللغة العربية**
- **تحليل المحتوى المرئي**
- **تحليل المشاعر**
- **توليد المحتوى**

### ❓ **كيف أفعل الذكاء الاصطناعي؟**
```bash
make ai-setup
make ai-train
```

### ❓ **هل أحتاج مفاتيح API خارجية؟**
بعض الميزات تحتاج مفاتيح اختيارية:
- **OpenAI**: للذكاء الاصطناعي التوليدي
- **Google AI**: لمعالجة اللغة المتقدمة

---

## 📊 **الأداء والمراقبة**

### ❓ **كيف أراقب أداء المنصة؟**
```bash
make monitoring-start
# افتح http://localhost:3000 للوحة Grafana
```

### ❓ **المنصة بطيئة، كيف أحسن الأداء؟**
1. **فعل التخزين المؤقت**: Redis
2. **استخدم CDN**: CloudFlare
3. **حسن قاعدة البيانات**: فهارس
4. **استخدم SSD**: للتخزين

### ❓ **كيف أحلل السجلات؟**
```bash
make logs-analysis
```

---

## 🆘 **استكشاف الأخطاء**

### ❓ **المنصة لا تعمل، ماذا أفعل؟**
1. **تحقق من السجلات**: `tail -f website/logs/app.log`
2. **فحص صحة النظام**: `make health-check`
3. **تشخيص شامل**: `make troubleshoot`

### ❓ **خطأ في قاعدة البيانات؟**
```bash
# تحقق من الاتصال
php artisan tinker
>>> DB::connection()->getPdo();

# إعادة تشغيل MySQL
sudo systemctl restart mysql
```

### ❓ **خطأ في الصلاحيات؟**
```bash
chmod -R 755 website/
chmod -R 777 website/storage/
chmod -R 777 website/cache/
```

### ❓ **الفيديوهات لا تعمل؟**
1. تحقق من صيغة الفيديو
2. تأكد من وجود FFmpeg
3. راجع إعدادات الخادم
4. تحقق من مساحة التخزين

---

## 💬 **الدعم والمساعدة**

### ❓ **كيف أحصل على المساعدة؟**
1. **راجع الوثائق** أولاً
2. **ابحث في GitHub Issues**
3. **انضم لمجتمع Discord**
4. **أنشئ Issue جديد**

### ❓ **كيف أساهم في المشروع؟**
1. **Fork المستودع**
2. **أنشئ فرع للميزة**
3. **اكتب الكود والاختبارات**
4. **أرسل Pull Request**

### ❓ **كيف أبلغ عن خطأ؟**
1. تأكد أنه خطأ حقيقي
2. ابحث في Issues الموجودة
3. أنشئ Issue جديد مع:
   - وصف مفصل للخطأ
   - خطوات إعادة الإنتاج
   - ملفات السجلات
   - معلومات البيئة

---

## 🎓 **التعلم والتطوير**

### ❓ **كيف أتعلم تطوير المنصة؟**
1. **ادرس الكود المصدري**
2. **اقرأ الوثائق التقنية**
3. **جرب الأمثلة**
4. **شارك في المجتمع**

### ❓ **ما هي التقنيات المستخدمة؟**
- **Backend**: PHP, Laravel, MySQL, Redis
- **Frontend**: HTML, CSS, JavaScript, Vue.js
- **Mobile**: Flutter, Swift, Kotlin
- **DevOps**: Docker, Kubernetes, CI/CD

### ❓ **هل يمكنني استخدام المنصة تجارياً؟**
نعم، الترخيص MIT يسمح بالاستخدام التجاري بحرية.

---

**❓ لم تجد إجابة لسؤالك؟**

**تواصل معنا:**
- **GitHub Issues**: للأسئلة التقنية
- **Discord**: للدردشة المباشرة
- **Email**: <EMAIL>

**📝 آخر تحديث: 15 يناير 2024**
