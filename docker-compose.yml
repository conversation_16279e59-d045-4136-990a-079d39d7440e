# 🐳 Docker Compose لمنصة البث العربية الشاملة
# نشر كامل مع جميع الخدمات المطلوبة

version: '3.8'

services:
  # خدمة التطبيق الرئيسي
  app:
    build: .
    container_name: streaming-platform-app
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./website:/var/www/html
      - ./logs:/var/www/html/logs
      - ./uploads:/var/www/html/public/uploads
      - ./cache:/var/www/html/cache
    environment:
      - DB_HOST=database
      - DB_NAME=streaming_platform
      - DB_USER=streaming_user
      - DB_PASS=streaming_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - database
      - redis
    networks:
      - streaming-network

  # قاعدة البيانات MySQL
  database:
    image: mysql:8.0
    container_name: streaming-platform-db
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: streaming_platform
      MYSQL_USER: streaming_user
      MYSQL_PASSWORD: streaming_password
    volumes:
      - db_data:/var/lib/mysql
      - ./setup.sql:/docker-entrypoint-initdb.d/setup.sql
      - ./docker/mysql.cnf:/etc/mysql/conf.d/custom.cnf
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - streaming-network

  # Redis للتخزين المؤقت
  redis:
    image: redis:7-alpine
    container_name: streaming-platform-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - streaming-network

  # Nginx كـ Reverse Proxy (اختياري)
  nginx:
    image: nginx:alpine
    container_name: streaming-platform-nginx
    restart: unless-stopped
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - app
    networks:
      - streaming-network

  # Elasticsearch للبحث المتقدم (اختياري)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: streaming-platform-search
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - es_data:/usr/share/elasticsearch/data
    networks:
      - streaming-network

  # phpMyAdmin لإدارة قاعدة البيانات
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: streaming-platform-pma
    restart: unless-stopped
    ports:
      - "8081:80"
    environment:
      PMA_HOST: database
      PMA_USER: streaming_user
      PMA_PASSWORD: streaming_password
      MYSQL_ROOT_PASSWORD: root_password
    depends_on:
      - database
    networks:
      - streaming-network

  # Redis Commander لإدارة Redis
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: streaming-platform-redis-ui
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      REDIS_HOSTS: local:redis:6379
    depends_on:
      - redis
    networks:
      - streaming-network

  # Mailhog لاختبار البريد الإلكتروني
  mailhog:
    image: mailhog/mailhog
    container_name: streaming-platform-mail
    restart: unless-stopped
    ports:
      - "1025:1025"
      - "8025:8025"
    networks:
      - streaming-network

  # مراقب النظام (Monitoring)
  monitoring:
    image: prom/prometheus
    container_name: streaming-platform-monitor
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - streaming-network

  # Grafana للتصور
  grafana:
    image: grafana/grafana
    container_name: streaming-platform-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      - monitoring
    networks:
      - streaming-network

  # خدمة النسخ الاحتياطي
  backup:
    image: alpine:latest
    container_name: streaming-platform-backup
    restart: "no"
    volumes:
      - db_data:/backup/database
      - ./uploads:/backup/uploads
      - ./logs:/backup/logs
      - ./backups:/backups
    command: |
      sh -c "
        apk add --no-cache mysql-client tar gzip &&
        while true; do
          echo 'بدء النسخ الاحتياطي...'
          tar -czf /backups/backup-$(date +%Y%m%d_%H%M%S).tar.gz /backup/
          echo 'تم إنشاء النسخة الاحتياطية'
          sleep 86400
        done
      "
    networks:
      - streaming-network

# الشبكات
networks:
  streaming-network:
    driver: bridge

# وحدات التخزين
volumes:
  db_data:
    driver: local
  redis_data:
    driver: local
  es_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
