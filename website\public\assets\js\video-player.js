/**
 * 🎬 مشغل الفيديو المتقدم
 * مشغل فيديو احترافي مع جميع الميزات
 */

class StreamingVideoPlayer {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            autoplay: false,
            controls: true,
            fluid: true,
            responsive: true,
            playbackRates: [0.5, 1, 1.25, 1.5, 2],
            languages: {
                'ar': {
                    'Play': 'تشغيل',
                    'Pause': 'إيقاف مؤقت',
                    'Mute': 'كتم الصوت',
                    'Unmute': 'إلغاء كتم الصوت',
                    'Fullscreen': 'ملء الشاشة',
                    'Exit Fullscreen': 'خروج من ملء الشاشة',
                    'Settings': 'الإعدادات',
                    'Quality': 'الجودة',
                    'Speed': 'السرعة',
                    'Subtitles': 'الترجمة'
                }
            },
            ...options
        };
        
        this.player = null;
        this.currentTime = 0;
        this.duration = 0;
        this.isPlaying = false;
        this.volume = 1;
        this.playbackRate = 1;
        this.currentQuality = 'auto';
        this.subtitles = [];
        this.currentSubtitle = null;
        
        this.init();
    }
    
    init() {
        this.createPlayerHTML();
        this.setupEventListeners();
        this.loadVideoJS();
    }
    
    createPlayerHTML() {
        this.container.innerHTML = `
            <div class="streaming-player-wrapper">
                <video
                    id="${this.container.id}-video"
                    class="video-js vjs-default-skin"
                    controls
                    preload="auto"
                    data-setup='{}'>
                    <p class="vjs-no-js">
                        لمشاهدة هذا الفيديو يرجى تفعيل JavaScript، و
                        <a href="https://videojs.com/html5-video-support/" target="_blank">
                            ترقية متصفحك
                        </a>
                    </p>
                </video>
                
                <!-- عناصر تحكم مخصصة -->
                <div class="custom-controls">
                    <div class="control-bar">
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-filled"></div>
                                <div class="progress-handle"></div>
                            </div>
                            <div class="time-display">
                                <span class="current-time">00:00</span>
                                <span class="duration">00:00</span>
                            </div>
                        </div>
                        
                        <div class="control-buttons">
                            <button class="btn-play-pause">
                                <i class="fas fa-play"></i>
                            </button>
                            
                            <button class="btn-volume">
                                <i class="fas fa-volume-up"></i>
                            </button>
                            
                            <div class="volume-slider">
                                <input type="range" min="0" max="1" step="0.1" value="1">
                            </div>
                            
                            <div class="settings-menu">
                                <button class="btn-settings">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <div class="settings-dropdown">
                                    <div class="setting-item">
                                        <label>الجودة</label>
                                        <select class="quality-selector">
                                            <option value="auto">تلقائي</option>
                                            <option value="1080p">1080p</option>
                                            <option value="720p">720p</option>
                                            <option value="480p">480p</option>
                                            <option value="360p">360p</option>
                                        </select>
                                    </div>
                                    
                                    <div class="setting-item">
                                        <label>السرعة</label>
                                        <select class="speed-selector">
                                            <option value="0.5">0.5x</option>
                                            <option value="1" selected>1x</option>
                                            <option value="1.25">1.25x</option>
                                            <option value="1.5">1.5x</option>
                                            <option value="2">2x</option>
                                        </select>
                                    </div>
                                    
                                    <div class="setting-item">
                                        <label>الترجمة</label>
                                        <select class="subtitle-selector">
                                            <option value="">بدون ترجمة</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <button class="btn-fullscreen">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- شاشة التحميل -->
                <div class="loading-overlay">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>جاري التحميل...</span>
                    </div>
                </div>
                
                <!-- رسائل الخطأ -->
                <div class="error-overlay" style="display: none;">
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>خطأ في تشغيل الفيديو</h3>
                        <p>حدث خطأ أثناء تحميل الفيديو. يرجى المحاولة مرة أخرى.</p>
                        <button class="btn-retry">إعادة المحاولة</button>
                    </div>
                </div>
            </div>
        `;
    }
    
    loadVideoJS() {
        // تحميل Video.js إذا لم يكن محملاً
        if (typeof videojs === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://vjs.zencdn.net/8.6.1/video.min.js';
            script.onload = () => this.initializePlayer();
            document.head.appendChild(script);
            
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://vjs.zencdn.net/8.6.1/video-js.css';
            document.head.appendChild(link);
        } else {
            this.initializePlayer();
        }
    }
    
    initializePlayer() {
        const videoElement = document.getElementById(`${this.container.id}-video`);
        
        this.player = videojs(videoElement, {
            ...this.options,
            language: 'ar',
            plugins: {
                hotkeys: {
                    volumeStep: 0.1,
                    seekStep: 5,
                    enableModifiersForNumbers: false
                }
            }
        });
        
        this.setupPlayerEvents();
        this.setupCustomControls();
    }
    
    setupPlayerEvents() {
        this.player.ready(() => {
            console.log('🎬 مشغل الفيديو جاهز');
            this.hideLoading();
        });
        
        this.player.on('loadstart', () => {
            this.showLoading();
        });
        
        this.player.on('canplay', () => {
            this.hideLoading();
            this.duration = this.player.duration();
            this.updateTimeDisplay();
        });
        
        this.player.on('play', () => {
            this.isPlaying = true;
            this.updatePlayButton();
            this.trackPlayEvent();
        });
        
        this.player.on('pause', () => {
            this.isPlaying = false;
            this.updatePlayButton();
        });
        
        this.player.on('timeupdate', () => {
            this.currentTime = this.player.currentTime();
            this.updateProgress();
            this.updateTimeDisplay();
            this.saveWatchProgress();
        });
        
        this.player.on('volumechange', () => {
            this.volume = this.player.volume();
            this.updateVolumeDisplay();
        });
        
        this.player.on('error', (error) => {
            console.error('خطأ في مشغل الفيديو:', error);
            this.showError();
        });
        
        this.player.on('ended', () => {
            this.trackCompleteEvent();
            this.showNextEpisode();
        });
    }
    
    setupCustomControls() {
        // زر التشغيل/الإيقاف
        const playPauseBtn = this.container.querySelector('.btn-play-pause');
        playPauseBtn.addEventListener('click', () => {
            if (this.isPlaying) {
                this.player.pause();
            } else {
                this.player.play();
            }
        });
        
        // شريط التقدم
        const progressBar = this.container.querySelector('.progress-bar');
        progressBar.addEventListener('click', (e) => {
            const rect = progressBar.getBoundingClientRect();
            const percent = (e.clientX - rect.left) / rect.width;
            const newTime = percent * this.duration;
            this.player.currentTime(newTime);
        });
        
        // التحكم في الصوت
        const volumeBtn = this.container.querySelector('.btn-volume');
        const volumeSlider = this.container.querySelector('.volume-slider input');
        
        volumeBtn.addEventListener('click', () => {
            if (this.player.muted()) {
                this.player.muted(false);
            } else {
                this.player.muted(true);
            }
        });
        
        volumeSlider.addEventListener('input', (e) => {
            this.player.volume(parseFloat(e.target.value));
        });
        
        // الإعدادات
        this.setupSettingsMenu();
        
        // ملء الشاشة
        const fullscreenBtn = this.container.querySelector('.btn-fullscreen');
        fullscreenBtn.addEventListener('click', () => {
            if (this.player.isFullscreen()) {
                this.player.exitFullscreen();
            } else {
                this.player.requestFullscreen();
            }
        });
    }
    
    setupSettingsMenu() {
        const settingsBtn = this.container.querySelector('.btn-settings');
        const settingsDropdown = this.container.querySelector('.settings-dropdown');
        
        settingsBtn.addEventListener('click', () => {
            settingsDropdown.classList.toggle('show');
        });
        
        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!settingsBtn.contains(e.target) && !settingsDropdown.contains(e.target)) {
                settingsDropdown.classList.remove('show');
            }
        });
        
        // تغيير الجودة
        const qualitySelector = this.container.querySelector('.quality-selector');
        qualitySelector.addEventListener('change', (e) => {
            this.changeQuality(e.target.value);
        });
        
        // تغيير السرعة
        const speedSelector = this.container.querySelector('.speed-selector');
        speedSelector.addEventListener('change', (e) => {
            this.player.playbackRate(parseFloat(e.target.value));
        });
        
        // تغيير الترجمة
        const subtitleSelector = this.container.querySelector('.subtitle-selector');
        subtitleSelector.addEventListener('change', (e) => {
            this.changeSubtitle(e.target.value);
        });
    }
    
    setupEventListeners() {
        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (!this.player) return;
            
            switch (e.code) {
                case 'Space':
                    e.preventDefault();
                    if (this.isPlaying) {
                        this.player.pause();
                    } else {
                        this.player.play();
                    }
                    break;
                    
                case 'ArrowLeft':
                    e.preventDefault();
                    this.player.currentTime(this.player.currentTime() - 10);
                    break;
                    
                case 'ArrowRight':
                    e.preventDefault();
                    this.player.currentTime(this.player.currentTime() + 10);
                    break;
                    
                case 'ArrowUp':
                    e.preventDefault();
                    this.player.volume(Math.min(1, this.player.volume() + 0.1));
                    break;
                    
                case 'ArrowDown':
                    e.preventDefault();
                    this.player.volume(Math.max(0, this.player.volume() - 0.1));
                    break;
                    
                case 'KeyF':
                    e.preventDefault();
                    if (this.player.isFullscreen()) {
                        this.player.exitFullscreen();
                    } else {
                        this.player.requestFullscreen();
                    }
                    break;
                    
                case 'KeyM':
                    e.preventDefault();
                    this.player.muted(!this.player.muted());
                    break;
            }
        });
    }
    
    // دوال التحكم
    loadVideo(videoData) {
        if (!this.player) return;
        
        this.player.src({
            src: videoData.url,
            type: videoData.type || 'video/mp4'
        });
        
        if (videoData.poster) {
            this.player.poster(videoData.poster);
        }
        
        if (videoData.subtitles) {
            this.loadSubtitles(videoData.subtitles);
        }
        
        // استعادة موضع المشاهدة
        this.restoreWatchProgress(videoData.id);
    }
    
    loadSubtitles(subtitles) {
        const subtitleSelector = this.container.querySelector('.subtitle-selector');
        subtitleSelector.innerHTML = '<option value="">بدون ترجمة</option>';
        
        subtitles.forEach(subtitle => {
            const option = document.createElement('option');
            option.value = subtitle.src;
            option.textContent = subtitle.label;
            subtitleSelector.appendChild(option);
            
            this.player.addRemoteTextTrack({
                kind: 'subtitles',
                src: subtitle.src,
                srclang: subtitle.lang,
                label: subtitle.label
            });
        });
    }
    
    changeQuality(quality) {
        // تغيير جودة الفيديو
        console.log('تغيير الجودة إلى:', quality);
        this.currentQuality = quality;
    }
    
    changeSubtitle(subtitleSrc) {
        const tracks = this.player.textTracks();
        
        // إخفاء جميع الترجمات
        for (let i = 0; i < tracks.length; i++) {
            tracks[i].mode = 'hidden';
        }
        
        // إظهار الترجمة المحددة
        if (subtitleSrc) {
            for (let i = 0; i < tracks.length; i++) {
                if (tracks[i].src === subtitleSrc) {
                    tracks[i].mode = 'showing';
                    break;
                }
            }
        }
    }
    
    // دوال التحديث
    updatePlayButton() {
        const playBtn = this.container.querySelector('.btn-play-pause i');
        playBtn.className = this.isPlaying ? 'fas fa-pause' : 'fas fa-play';
    }
    
    updateProgress() {
        if (this.duration === 0) return;
        
        const percent = (this.currentTime / this.duration) * 100;
        const progressFilled = this.container.querySelector('.progress-filled');
        progressFilled.style.width = `${percent}%`;
    }
    
    updateTimeDisplay() {
        const currentTimeEl = this.container.querySelector('.current-time');
        const durationEl = this.container.querySelector('.duration');
        
        currentTimeEl.textContent = this.formatTime(this.currentTime);
        durationEl.textContent = this.formatTime(this.duration);
    }
    
    updateVolumeDisplay() {
        const volumeBtn = this.container.querySelector('.btn-volume i');
        const volumeSlider = this.container.querySelector('.volume-slider input');
        
        volumeSlider.value = this.volume;
        
        if (this.player.muted() || this.volume === 0) {
            volumeBtn.className = 'fas fa-volume-mute';
        } else if (this.volume < 0.5) {
            volumeBtn.className = 'fas fa-volume-down';
        } else {
            volumeBtn.className = 'fas fa-volume-up';
        }
    }
    
    // دوال مساعدة
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }
    
    showLoading() {
        const loadingOverlay = this.container.querySelector('.loading-overlay');
        loadingOverlay.style.display = 'flex';
    }
    
    hideLoading() {
        const loadingOverlay = this.container.querySelector('.loading-overlay');
        loadingOverlay.style.display = 'none';
    }
    
    showError() {
        const errorOverlay = this.container.querySelector('.error-overlay');
        errorOverlay.style.display = 'flex';
        
        const retryBtn = errorOverlay.querySelector('.btn-retry');
        retryBtn.addEventListener('click', () => {
            errorOverlay.style.display = 'none';
            this.player.load();
        });
    }
    
    // تتبع المشاهدة
    trackPlayEvent() {
        // تسجيل بداية المشاهدة
        if (typeof trackEvent === 'function') {
            trackEvent('video_play', {
                video_id: this.currentVideoId,
                timestamp: this.currentTime
            });
        }
    }
    
    trackCompleteEvent() {
        // تسجيل انتهاء المشاهدة
        if (typeof trackEvent === 'function') {
            trackEvent('video_complete', {
                video_id: this.currentVideoId,
                duration: this.duration
            });
        }
    }
    
    saveWatchProgress() {
        // حفظ موضع المشاهدة كل 10 ثوان
        if (this.currentTime % 10 < 1) {
            localStorage.setItem(`watch_progress_${this.currentVideoId}`, this.currentTime);
        }
    }
    
    restoreWatchProgress(videoId) {
        this.currentVideoId = videoId;
        const savedTime = localStorage.getItem(`watch_progress_${videoId}`);
        
        if (savedTime && parseFloat(savedTime) > 30) {
            const resumeTime = parseFloat(savedTime);
            
            if (confirm(`هل تريد متابعة المشاهدة من ${this.formatTime(resumeTime)}؟`)) {
                this.player.currentTime(resumeTime);
            }
        }
    }
    
    showNextEpisode() {
        // عرض الحلقة التالية إذا كانت متاحة
        if (this.options.nextEpisode) {
            const nextOverlay = document.createElement('div');
            nextOverlay.className = 'next-episode-overlay';
            nextOverlay.innerHTML = `
                <div class="next-episode-content">
                    <h3>الحلقة التالية</h3>
                    <p>${this.options.nextEpisode.title}</p>
                    <button class="btn-next-episode">مشاهدة الآن</button>
                    <button class="btn-cancel">إلغاء</button>
                </div>
            `;
            
            this.container.appendChild(nextOverlay);
            
            // العد التنازلي للتشغيل التلقائي
            let countdown = 10;
            const countdownInterval = setInterval(() => {
                const btn = nextOverlay.querySelector('.btn-next-episode');
                btn.textContent = `مشاهدة الآن (${countdown})`;
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(countdownInterval);
                    this.playNextEpisode();
                }
            }, 1000);
            
            // أزرار التحكم
            nextOverlay.querySelector('.btn-next-episode').addEventListener('click', () => {
                clearInterval(countdownInterval);
                this.playNextEpisode();
            });
            
            nextOverlay.querySelector('.btn-cancel').addEventListener('click', () => {
                clearInterval(countdownInterval);
                nextOverlay.remove();
            });
        }
    }
    
    playNextEpisode() {
        if (this.options.nextEpisode && this.options.nextEpisode.url) {
            window.location.href = this.options.nextEpisode.url;
        }
    }
    
    // تدمير المشغل
    destroy() {
        if (this.player) {
            this.player.dispose();
        }
    }
}

// تصدير الفئة للاستخدام العام
window.StreamingVideoPlayer = StreamingVideoPlayer;
