<?php
/**
 * 🔐 API تسجيل الدخول
 * يتعامل مع طلبات تسجيل الدخول عبر AJAX
 */

// تعريف الثابت للسماح بالوصول
define('STREAMING_PLATFORM', true);

// تحميل الملفات المطلوبة
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// تعيين رأس JSON
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// التعامل مع طلبات OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'طريقة الطلب غير مدعومة'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من وجود البيانات المطلوبة
    if (!isset($input['email']) || !isset($input['password'])) {
        throw new Exception('البريد الإلكتروني وكلمة المرور مطلوبان');
    }
    
    // تنظيف البيانات
    $email = sanitizeInput($input['email']);
    $password = $input['password'];
    $rememberMe = isset($input['remember_me']) ? (bool)$input['remember_me'] : false;
    
    // التحقق من صحة البريد الإلكتروني
    if (!isValidEmail($email)) {
        throw new Exception('البريد الإلكتروني غير صحيح');
    }
    
    // التحقق من طول كلمة المرور
    if (strlen($password) < 6) {
        throw new Exception('كلمة المرور قصيرة جداً');
    }
    
    // التحقق من CSRF Token إذا كان متوفراً
    if (isset($input['csrf_token'])) {
        if (!verifyCSRFToken($input['csrf_token'])) {
            throw new Exception('رمز الأمان غير صحيح');
        }
    }
    
    // محاولة تسجيل الدخول
    $auth = new Auth();
    $result = $auth->login($email, $password, $rememberMe);
    
    if ($result['success']) {
        // تسجيل نشاط تسجيل الدخول
        logUserActivity('login_api', [
            'email' => $email,
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'remember_me' => $rememberMe
        ]);
        
        // تتبع حدث تسجيل الدخول
        trackEvent('user_login', [
            'user_id' => $result['user']['id'],
            'login_method' => 'api',
            'remember_me' => $rememberMe
        ]);
        
        // إرجاع الاستجابة الناجحة
        echo json_encode([
            'success' => true,
            'message' => 'تم تسجيل الدخول بنجاح',
            'user' => [
                'id' => $result['user']['id'],
                'first_name' => $result['user']['first_name'],
                'last_name' => $result['user']['last_name'],
                'email' => $result['user']['email'],
                'role' => $result['user']['role'],
                'subscription_type' => $result['user']['subscription_type'],
                'avatar' => $result['user']['avatar'],
                'email_verified' => (bool)$result['user']['email_verified']
            ],
            'redirect_url' => '/dashboard'
        ], JSON_UNESCAPED_UNICODE);
        
    } else {
        // تسجيل محاولة تسجيل دخول فاشلة
        logUserActivity('login_failed_api', [
            'email' => $email,
            'error' => $result['error'],
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        
        // تتبع حدث فشل تسجيل الدخول
        trackEvent('login_failed', [
            'email' => $email,
            'error' => $result['error'],
            'login_method' => 'api'
        ]);
        
        // إرجاع رسالة الخطأ
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'error' => $result['error'],
            'requires_verification' => $result['requires_verification'] ?? false
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    // تسجيل الخطأ
    logError('Login API Error: ' . $e->getMessage(), [
        'file' => __FILE__,
        'line' => $e->getLine(),
        'input' => $input ?? null,
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);
    
    // إرجاع رسالة خطأ عامة
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
