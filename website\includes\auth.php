<?php
/**
 * 🔐 نظام المصادقة والتحقق
 * يتعامل مع تسجيل الدخول والخروج وإدارة الجلسات
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * تسجيل الدخول
     */
    public function login($email, $password, $rememberMe = false) {
        // التحقق من محاولات تسجيل الدخول
        if ($this->isAccountLocked($email)) {
            return [
                'success' => false,
                'error' => 'تم قفل الحساب مؤقتاً بسبب محاولات دخول متعددة فاشلة'
            ];
        }
        
        // البحث عن المستخدم
        $user = $this->getUserByEmail($email);
        if (!$user) {
            $this->recordFailedAttempt($email);
            return [
                'success' => false,
                'error' => 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
            ];
        }
        
        // التحقق من كلمة المرور
        if (!password_verify($password, $user['password_hash'])) {
            $this->recordFailedAttempt($email);
            return [
                'success' => false,
                'error' => 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
            ];
        }
        
        // التحقق من حالة الحساب
        if ($user['status'] !== 'active') {
            return [
                'success' => false,
                'error' => 'الحساب غير مفعل أو محظور'
            ];
        }
        
        // التحقق من تأكيد البريد الإلكتروني
        if (!$user['email_verified'] && getSetting('email_verification_required') === 'true') {
            return [
                'success' => false,
                'error' => 'يرجى تأكيد البريد الإلكتروني أولاً',
                'requires_verification' => true
            ];
        }
        
        // إنشاء الجلسة
        $this->createSession($user, $rememberMe);
        
        // تحديث آخر تسجيل دخول
        $this->updateLastLogin($user['id']);
        
        // مسح محاولات الدخول الفاشلة
        $this->clearFailedAttempts($email);
        
        // تسجيل نشاط تسجيل الدخول
        $this->logActivity($user['id'], 'login', [
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT']
        ]);
        
        return [
            'success' => true,
            'user' => $this->sanitizeUserData($user)
        ];
    }
    
    /**
     * تسجيل الخروج
     */
    public function logout() {
        if (isset($_SESSION['user_id'])) {
            // تسجيل نشاط تسجيل الخروج
            $this->logActivity($_SESSION['user_id'], 'logout');
            
            // حذف الجلسة من قاعدة البيانات
            $this->deleteSession(session_id());
        }
        
        // مسح جميع بيانات الجلسة
        session_unset();
        session_destroy();
        
        // حذف كوكيز "تذكرني"
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
            unset($_COOKIE['remember_token']);
        }
        
        return true;
    }
    
    /**
     * تسجيل مستخدم جديد
     */
    public function register($userData) {
        // التحقق من صحة البيانات
        $validation = $this->validateRegistrationData($userData);
        if (!$validation['valid']) {
            return [
                'success' => false,
                'errors' => $validation['errors']
            ];
        }
        
        // التحقق من عدم وجود البريد الإلكتروني
        if ($this->emailExists($userData['email'])) {
            return [
                'success' => false,
                'error' => 'البريد الإلكتروني مستخدم بالفعل'
            ];
        }
        
        // تشفير كلمة المرور
        $passwordHash = password_hash($userData['password'], PASSWORD_ARGON2ID);
        
        // إنشاء رمز تأكيد البريد الإلكتروني
        $verificationToken = bin2hex(random_bytes(32));
        
        // إعداد بيانات المستخدم
        $userInsertData = [
            'first_name' => $userData['first_name'],
            'last_name' => $userData['last_name'],
            'email' => $userData['email'],
            'password_hash' => $passwordHash,
            'email_verification_token' => $verificationToken,
            'email_verified' => getSetting('email_verification_required') === 'true' ? 0 : 1,
            'status' => 'active',
            'role' => 'user',
            'subscription_type' => 'free',
            'language' => $userData['language'] ?? DEFAULT_LANGUAGE,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        try {
            // إدراج المستخدم الجديد
            $userId = $this->db->insert('users', $userInsertData);
            
            // إرسال بريد تأكيد إذا كان مطلوباً
            if (getSetting('email_verification_required') === 'true') {
                $this->sendVerificationEmail($userData['email'], $verificationToken);
            }
            
            // تسجيل نشاط التسجيل
            $this->logActivity($userId, 'register', [
                'ip' => $_SERVER['REMOTE_ADDR'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT']
            ]);
            
            return [
                'success' => true,
                'user_id' => $userId,
                'requires_verification' => getSetting('email_verification_required') === 'true'
            ];
            
        } catch (Exception $e) {
            logError('Registration failed: ' . $e->getMessage(), $userData);
            return [
                'success' => false,
                'error' => 'حدث خطأ أثناء إنشاء الحساب'
            ];
        }
    }
    
    /**
     * تأكيد البريد الإلكتروني
     */
    public function verifyEmail($token) {
        $user = $this->db->fetchRow(
            "SELECT id, email FROM " . DB_PREFIX . "users WHERE email_verification_token = ? AND email_verified = 0",
            [$token]
        );
        
        if (!$user) {
            return [
                'success' => false,
                'error' => 'رمز التأكيد غير صحيح أو منتهي الصلاحية'
            ];
        }
        
        // تأكيد البريد الإلكتروني
        $this->db->update('users', [
            'email_verified' => 1,
            'email_verification_token' => null
        ], 'id = ?', [$user['id']]);
        
        // تسجيل النشاط
        $this->logActivity($user['id'], 'email_verified');
        
        return [
            'success' => true,
            'message' => 'تم تأكيد البريد الإلكتروني بنجاح'
        ];
    }
    
    /**
     * إعادة تعيين كلمة المرور
     */
    public function requestPasswordReset($email) {
        $user = $this->getUserByEmail($email);
        if (!$user) {
            // لا نكشف عن عدم وجود البريد الإلكتروني لأسباب أمنية
            return [
                'success' => true,
                'message' => 'إذا كان البريد الإلكتروني موجود، ستصلك رسالة إعادة تعيين كلمة المرور'
            ];
        }
        
        // إنشاء رمز إعادة التعيين
        $resetToken = bin2hex(random_bytes(32));
        $resetExpires = date('Y-m-d H:i:s', time() + 3600); // ساعة واحدة
        
        // حفظ الرمز في قاعدة البيانات
        $this->db->update('users', [
            'password_reset_token' => $resetToken,
            'password_reset_expires' => $resetExpires
        ], 'id = ?', [$user['id']]);
        
        // إرسال بريد إعادة التعيين
        $this->sendPasswordResetEmail($email, $resetToken);
        
        // تسجيل النشاط
        $this->logActivity($user['id'], 'password_reset_requested');
        
        return [
            'success' => true,
            'message' => 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني'
        ];
    }
    
    /**
     * إعادة تعيين كلمة المرور
     */
    public function resetPassword($token, $newPassword) {
        // البحث عن المستخدم بالرمز
        $user = $this->db->fetchRow(
            "SELECT id FROM " . DB_PREFIX . "users 
             WHERE password_reset_token = ? AND password_reset_expires > NOW()",
            [$token]
        );
        
        if (!$user) {
            return [
                'success' => false,
                'error' => 'رمز إعادة التعيين غير صحيح أو منتهي الصلاحية'
            ];
        }
        
        // التحقق من قوة كلمة المرور
        if (!$this->isStrongPassword($newPassword)) {
            return [
                'success' => false,
                'error' => 'كلمة المرور ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل مع أحرف كبيرة وصغيرة وأرقام'
            ];
        }
        
        // تشفير كلمة المرور الجديدة
        $passwordHash = password_hash($newPassword, PASSWORD_ARGON2ID);
        
        // تحديث كلمة المرور ومسح رمز الإعادة
        $this->db->update('users', [
            'password_hash' => $passwordHash,
            'password_reset_token' => null,
            'password_reset_expires' => null
        ], 'id = ?', [$user['id']]);
        
        // تسجيل النشاط
        $this->logActivity($user['id'], 'password_reset');
        
        return [
            'success' => true,
            'message' => 'تم تغيير كلمة المرور بنجاح'
        ];
    }
    
    /**
     * التحقق من الجلسة الحالية
     */
    public function checkSession() {
        // التحقق من وجود جلسة نشطة
        if (isset($_SESSION['user_id'])) {
            return $this->validateSession($_SESSION['user_id'], session_id());
        }
        
        // التحقق من كوكيز "تذكرني"
        if (isset($_COOKIE['remember_token'])) {
            return $this->validateRememberToken($_COOKIE['remember_token']);
        }
        
        return false;
    }
    
    /**
     * التحقق من صحة الجلسة
     */
    private function validateSession($userId, $sessionId) {
        $session = $this->db->fetchRow(
            "SELECT * FROM " . DB_PREFIX . "sessions 
             WHERE id = ? AND user_id = ? AND expires_at > NOW()",
            [$sessionId, $userId]
        );
        
        if ($session) {
            // تحديث آخر نشاط
            $this->db->update('sessions', [
                'last_activity' => date('Y-m-d H:i:s')
            ], 'id = ?', [$sessionId]);
            
            return true;
        }
        
        // مسح الجلسة غير الصحيحة
        $this->logout();
        return false;
    }
    
    /**
     * التحقق من رمز "تذكرني"
     */
    private function validateRememberToken($token) {
        $tokenData = $this->db->fetchRow(
            "SELECT user_id FROM " . DB_PREFIX . "user_tokens 
             WHERE token = ? AND type = 'remember' AND expires_at > NOW()",
            [$token]
        );
        
        if ($tokenData) {
            $user = $this->getUserById($tokenData['user_id']);
            if ($user && $user['status'] === 'active') {
                $this->createSession($user, true);
                return true;
            }
        }
        
        // حذف الرمز غير الصحيح
        setcookie('remember_token', '', time() - 3600, '/');
        return false;
    }
    
    /**
     * إنشاء جلسة جديدة
     */
    private function createSession($user, $rememberMe = false) {
        // تجديد معرف الجلسة لمنع session fixation
        session_regenerate_id(true);
        
        // تعيين بيانات الجلسة
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
        $_SESSION['subscription_type'] = $user['subscription_type'];
        $_SESSION['last_activity'] = time();
        
        // حفظ الجلسة في قاعدة البيانات
        $sessionData = [
            'id' => session_id(),
            'user_id' => $user['id'],
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'device_type' => $this->detectDeviceType(),
            'data' => json_encode($_SESSION),
            'expires_at' => date('Y-m-d H:i:s', time() + SESSION_LIFETIME),
            'last_activity' => date('Y-m-d H:i:s')
        ];
        
        $this->db->insert('sessions', $sessionData);
        
        // إنشاء رمز "تذكرني" إذا كان مطلوباً
        if ($rememberMe) {
            $this->createRememberToken($user['id']);
        }
    }
    
    /**
     * إنشاء رمز "تذكرني"
     */
    private function createRememberToken($userId) {
        $token = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', time() + (30 * 24 * 60 * 60)); // 30 يوم
        
        // حفظ الرمز في قاعدة البيانات
        $this->db->insert('user_tokens', [
            'user_id' => $userId,
            'token' => $token,
            'type' => 'remember',
            'expires_at' => $expires
        ]);
        
        // تعيين الكوكيز
        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true);
    }
    
    /**
     * التحقق من قفل الحساب
     */
    private function isAccountLocked($email) {
        $user = $this->getUserByEmail($email);
        if (!$user) {
            return false;
        }
        
        return $user['locked_until'] && strtotime($user['locked_until']) > time();
    }
    
    /**
     * تسجيل محاولة دخول فاشلة
     */
    private function recordFailedAttempt($email) {
        $user = $this->getUserByEmail($email);
        if (!$user) {
            return;
        }
        
        $attempts = $user['login_attempts'] + 1;
        $updateData = ['login_attempts' => $attempts];
        
        // قفل الحساب إذا تجاوز الحد الأقصى
        if ($attempts >= MAX_LOGIN_ATTEMPTS) {
            $updateData['locked_until'] = date('Y-m-d H:i:s', time() + LOGIN_LOCKOUT_TIME);
        }
        
        $this->db->update('users', $updateData, 'id = ?', [$user['id']]);
    }
    
    /**
     * مسح محاولات الدخول الفاشلة
     */
    private function clearFailedAttempts($email) {
        $this->db->update('users', [
            'login_attempts' => 0,
            'locked_until' => null
        ], 'email = ?', [$email]);
    }
    
    /**
     * الحصول على مستخدم بالبريد الإلكتروني
     */
    private function getUserByEmail($email) {
        return $this->db->fetchRow(
            "SELECT * FROM " . DB_PREFIX . "users WHERE email = ?",
            [$email]
        );
    }
    
    /**
     * الحصول على مستخدم بالمعرف
     */
    private function getUserById($id) {
        return $this->db->fetchRow(
            "SELECT * FROM " . DB_PREFIX . "users WHERE id = ?",
            [$id]
        );
    }
    
    /**
     * التحقق من وجود البريد الإلكتروني
     */
    private function emailExists($email) {
        $result = $this->db->fetchRow(
            "SELECT id FROM " . DB_PREFIX . "users WHERE email = ?",
            [$email]
        );
        return $result !== false;
    }
    
    /**
     * تحديث آخر تسجيل دخول
     */
    private function updateLastLogin($userId) {
        $this->db->update('users', [
            'last_login_at' => date('Y-m-d H:i:s')
        ], 'id = ?', [$userId]);
    }
    
    /**
     * حذف الجلسة
     */
    private function deleteSession($sessionId) {
        $this->db->delete('sessions', 'id = ?', [$sessionId]);
    }
    
    /**
     * تنظيف بيانات المستخدم
     */
    private function sanitizeUserData($user) {
        unset($user['password_hash']);
        unset($user['email_verification_token']);
        unset($user['password_reset_token']);
        unset($user['password_reset_expires']);
        return $user;
    }
    
    /**
     * التحقق من صحة بيانات التسجيل
     */
    private function validateRegistrationData($data) {
        $errors = [];
        
        // التحقق من الاسم الأول
        if (empty($data['first_name']) || strlen($data['first_name']) < 2) {
            $errors['first_name'] = 'الاسم الأول مطلوب ويجب أن يكون حرفين على الأقل';
        }
        
        // التحقق من الاسم الأخير
        if (empty($data['last_name']) || strlen($data['last_name']) < 2) {
            $errors['last_name'] = 'الاسم الأخير مطلوب ويجب أن يكون حرفين على الأقل';
        }
        
        // التحقق من البريد الإلكتروني
        if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'البريد الإلكتروني غير صحيح';
        }
        
        // التحقق من كلمة المرور
        if (empty($data['password']) || !$this->isStrongPassword($data['password'])) {
            $errors['password'] = 'كلمة المرور ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل مع أحرف كبيرة وصغيرة وأرقام';
        }
        
        // التحقق من تأكيد كلمة المرور
        if ($data['password'] !== $data['confirm_password']) {
            $errors['confirm_password'] = 'كلمات المرور غير متطابقة';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * التحقق من قوة كلمة المرور
     */
    private function isStrongPassword($password) {
        return strlen($password) >= 8 &&
               preg_match('/[A-Z]/', $password) &&
               preg_match('/[a-z]/', $password) &&
               preg_match('/[0-9]/', $password);
    }
    
    /**
     * اكتشاف نوع الجهاز
     */
    private function detectDeviceType() {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
            return 'mobile';
        } elseif (preg_match('/Tablet/', $userAgent)) {
            return 'tablet';
        } else {
            return 'desktop';
        }
    }
    
    /**
     * تسجيل نشاط المستخدم
     */
    private function logActivity($userId, $activity, $data = []) {
        $this->db->insert('user_activity', [
            'user_id' => $userId,
            'activity' => $activity,
            'data' => json_encode($data),
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * إرسال بريد تأكيد البريد الإلكتروني
     */
    private function sendVerificationEmail($email, $token) {
        // سيتم تنفيذ هذه الدالة في ملف البريد الإلكتروني
        // هنا نضع placeholder
        return true;
    }
    
    /**
     * إرسال بريد إعادة تعيين كلمة المرور
     */
    private function sendPasswordResetEmail($email, $token) {
        // سيتم تنفيذ هذه الدالة في ملف البريد الإلكتروني
        // هنا نضع placeholder
        return true;
    }
}

// إنشاء مثيل عام من فئة المصادقة
$auth = new Auth();
?>
