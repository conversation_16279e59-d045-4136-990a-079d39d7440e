import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

import '../../core/constants/app_constants.dart';

/// 🔍 شاشة البحث
class SearchScreen extends ConsumerStatefulWidget {
  const SearchScreen({super.key});

  @override
  ConsumerState<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends ConsumerState<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  
  bool _isSearching = false;
  List<String> _searchHistory = [];
  List<String> _suggestions = [];

  @override
  void initState() {
    super.initState();
    _loadSearchHistory();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _loadSearchHistory() {
    // تحميل تاريخ البحث من التخزين المحلي
    setState(() {
      _searchHistory = [
        'أفلام أكشن',
        'مسلسلات كورية',
        'أفلام رعب',
        'كوميديا عربية',
        'وثائقيات طبيعة',
      ];
    });
  }

  void _onSearchChanged() {
    final query = _searchController.text;
    if (query.isEmpty) {
      setState(() {
        _isSearching = false;
        _suggestions.clear();
      });
      return;
    }

    setState(() {
      _isSearching = true;
      _suggestions = _generateSuggestions(query);
    });
  }

  List<String> _generateSuggestions(String query) {
    // محاكاة اقتراحات البحث
    final allSuggestions = [
      'أفلام أكشن 2024',
      'مسلسلات كورية رومانسية',
      'أفلام رعب مترجمة',
      'كوميديا عربية جديدة',
      'وثائقيات طبيعة HD',
      'أنمي مدبلج عربي',
      'مسلسلات تركية',
      'أفلام هندية',
    ];

    return allSuggestions
        .where((suggestion) => 
            suggestion.toLowerCase().contains(query.toLowerCase()))
        .take(5)
        .toList();
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) return;

    // إضافة للتاريخ
    setState(() {
      _searchHistory.remove(query);
      _searchHistory.insert(0, query);
      if (_searchHistory.length > 10) {
        _searchHistory.removeLast();
      }
    });

    // تنفيذ البحث
    _searchFocusNode.unfocus();
    // هنا سيتم استدعاء API البحث
  }

  void _clearSearchHistory() {
    setState(() {
      _searchHistory.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: _buildSearchField(),
        automaticallyImplyLeading: false,
        elevation: 0,
        backgroundColor: AppConstants.darkColor,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildSearchField() {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: AppConstants.secondaryColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: TextField(
        controller: _searchController,
        focusNode: _searchFocusNode,
        decoration: InputDecoration(
          hintText: 'ابحث عن أفلام، مسلسلات...',
          hintStyle: const TextStyle(
            color: AppConstants.textMutedColor,
            fontSize: 14,
          ),
          prefixIcon: const Icon(
            Icons.search,
            color: AppConstants.textMutedColor,
            size: 20,
          ),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(
                    Icons.clear,
                    color: AppConstants.textMutedColor,
                    size: 20,
                  ),
                  onPressed: () {
                    _searchController.clear();
                    _searchFocusNode.unfocus();
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 10,
          ),
        ),
        style: const TextStyle(
          color: AppConstants.textColor,
          fontSize: 14,
        ),
        textInputAction: TextInputAction.search,
        onSubmitted: _performSearch,
      ),
    );
  }

  Widget _buildBody() {
    if (_isSearching && _suggestions.isNotEmpty) {
      return _buildSuggestions();
    } else if (_searchController.text.isNotEmpty) {
      return _buildSearchResults();
    } else {
      return _buildSearchHome();
    }
  }

  Widget _buildSearchHome() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // تاريخ البحث
          if (_searchHistory.isNotEmpty) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'عمليات البحث الأخيرة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: _clearSearchHistory,
                  child: const Text(
                    'مسح الكل',
                    style: TextStyle(color: AppConstants.primaryColor),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _searchHistory.map((query) {
                return GestureDetector(
                  onTap: () {
                    _searchController.text = query;
                    _performSearch(query);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: AppConstants.secondaryColor,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: AppConstants.borderColor,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.history,
                          size: 16,
                          color: AppConstants.textMutedColor,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          query,
                          style: const TextStyle(
                            color: AppConstants.textColor,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 24),
          ],

          // التصنيفات الشائعة
          Text(
            'تصفح حسب النوع',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildGenreGrid(),

          const SizedBox(height: 24),

          // الاتجاهات الشائعة
          Text(
            'الاتجاهات الشائعة',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildTrendingSearches(),
        ],
      ),
    );
  }

  Widget _buildGenreGrid() {
    final genres = [
      {'name': 'أكشن', 'icon': Icons.local_fire_department, 'color': Colors.red},
      {'name': 'كوميديا', 'icon': Icons.sentiment_very_satisfied, 'color': Colors.orange},
      {'name': 'دراما', 'icon': Icons.theater_comedy, 'color': Colors.blue},
      {'name': 'رعب', 'icon': Icons.psychology, 'color': Colors.purple},
      {'name': 'رومانسي', 'icon': Icons.favorite, 'color': Colors.pink},
      {'name': 'خيال علمي', 'icon': Icons.rocket_launch, 'color': Colors.cyan},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: genres.length,
      itemBuilder: (context, index) {
        final genre = genres[index];
        return GestureDetector(
          onTap: () {
            _searchController.text = genre['name'] as String;
            _performSearch(genre['name'] as String);
          },
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  (genre['color'] as Color).withOpacity(0.8),
                  (genre['color'] as Color).withOpacity(0.6),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  genre['icon'] as IconData,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  genre['name'] as String,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTrendingSearches() {
    final trending = [
      'أفلام 2024',
      'مسلسلات كورية',
      'أنمي جديد',
      'وثائقيات',
      'أفلام عربية',
    ];

    return Column(
      children: trending.map((search) {
        return ListTile(
          leading: const Icon(
            Icons.trending_up,
            color: AppConstants.primaryColor,
          ),
          title: Text(search),
          trailing: const Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: AppConstants.textMutedColor,
          ),
          onTap: () {
            _searchController.text = search;
            _performSearch(search);
          },
        );
      }).toList(),
    );
  }

  Widget _buildSuggestions() {
    return ListView.builder(
      itemCount: _suggestions.length,
      itemBuilder: (context, index) {
        final suggestion = _suggestions[index];
        return ListTile(
          leading: const Icon(
            Icons.search,
            color: AppConstants.textMutedColor,
          ),
          title: Text(suggestion),
          trailing: const Icon(
            Icons.north_west,
            size: 16,
            color: AppConstants.textMutedColor,
          ),
          onTap: () {
            _searchController.text = suggestion;
            _performSearch(suggestion);
          },
        );
      },
    );
  }

  Widget _buildSearchResults() {
    // محاكاة نتائج البحث
    return CustomScrollView(
      slivers: [
        SliverPadding(
          padding: const EdgeInsets.all(16),
          sliver: SliverToBoxAdapter(
            child: Text(
              'نتائج البحث عن "${_searchController.text}"',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          sliver: SliverMasonryGrid.count(
            crossAxisCount: 2,
            mainAxisSpacing: 12,
            crossAxisSpacing: 12,
            childCount: 20,
            itemBuilder: (context, index) {
              return _buildSearchResultCard(index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResultCard(int index) {
    final isMovie = index % 3 != 0;
    final height = isMovie ? 200.0 : 160.0;

    return Container(
      height: height,
      decoration: BoxDecoration(
        color: AppConstants.secondaryColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: AppConstants.borderColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: const Center(
                child: Icon(
                  Icons.movie,
                  size: 40,
                  color: AppConstants.textMutedColor,
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isMovie ? 'فيلم ${index + 1}' : 'مسلسل ${index + 1}',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(
                      Icons.star,
                      size: 12,
                      color: Colors.amber,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${(8.0 + (index % 2)).toStringAsFixed(1)}',
                      style: const TextStyle(
                        fontSize: 10,
                        color: AppConstants.textMutedColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
