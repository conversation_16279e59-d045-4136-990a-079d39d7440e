# 🚀 دليل البدء السريع - منصة البث العربية

## 🎯 **مرحباً بك في منصة البث العربية!**

هذا الدليل سيساعدك على البدء مع منصة البث العربية في أقل من 10 دقائق!

---

## ⚡ **البدء السريع (3 دقائق)**

### 🔧 **المتطلبات الأساسية:**
```bash
# تأكد من وجود هذه الأدوات:
- PHP 8.1+
- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- Composer
- Git
```

### 🚀 **التثبيت السريع:**
```bash
# 1. استنساخ المشروع
git clone https://github.com/your-username/arabic-streaming-platform.git
cd arabic-streaming-platform

# 2. البدء السريع (أمر واحد فقط!)
make quick-start

# 3. افتح المتصفح
open http://localhost:8000
```

**🎉 تهانينا! المنصة تعمل الآن!**

---

## 📋 **الخطوات التفصيلية**

### 1️⃣ **تحضير البيئة**

#### **تثبيت المتطلبات:**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install php8.1 php8.1-mysql php8.1-redis nodejs npm mysql-server redis-server

# macOS (Homebrew)
brew install php@8.1 node mysql redis

# Windows (Chocolatey)
choco install php nodejs mysql redis
```

#### **تثبيت Composer:**
```bash
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

### 2️⃣ **إعداد المشروع**

#### **استنساخ وإعداد:**
```bash
# استنساخ المشروع
git clone https://github.com/your-username/arabic-streaming-platform.git
cd arabic-streaming-platform

# تثبيت التبعيات
composer install
npm install

# نسخ ملف الإعدادات
cp .env.example .env
```

#### **إعداد قاعدة البيانات:**
```bash
# إنشاء قاعدة البيانات
mysql -u root -p -e "CREATE DATABASE streaming_platform;"

# تشغيل الهجرات
php artisan migrate --seed
```

### 3️⃣ **التكوين الأساسي**

#### **تحرير ملف .env:**
```env
# إعدادات التطبيق
APP_NAME="منصة البث العربية"
APP_URL=http://localhost:8000

# إعدادات قاعدة البيانات
DB_DATABASE=streaming_platform
DB_USERNAME=root
DB_PASSWORD=your_password

# إعدادات Redis
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
```

#### **توليد مفتاح التطبيق:**
```bash
php artisan key:generate
```

### 4️⃣ **تشغيل المنصة**

#### **تشغيل الخوادم:**
```bash
# الطريقة السريعة
make dev

# أو يدوياً
php artisan serve &
npm run dev &
redis-server &
```

#### **فتح المنصة:**
```bash
# في المتصفح
open http://localhost:8000

# أو
curl http://localhost:8000
```

---

## 🎮 **الاستخدام الأساسي**

### 👤 **إنشاء حساب مدير:**
```bash
php artisan make:admin
# اتبع التعليمات لإنشاء حساب المدير
```

### 📺 **رفع أول فيديو:**
1. سجل دخول كمدير: `http://localhost:8000/admin`
2. اذهب إلى "إدارة المحتوى"
3. اضغط "إضافة فيديو جديد"
4. املأ البيانات وارفع الفيديو
5. احفظ ونشر

### 🎨 **تخصيص المظهر:**
```bash
# تحرير ملفات CSS
nano website/public/assets/css/main.css

# إعادة بناء الأصول
npm run build
```

---

## 🔧 **الإعدادات المتقدمة**

### 🐳 **استخدام Docker:**
```bash
# بناء وتشغيل الحاويات
docker-compose up -d

# عرض الحالة
docker-compose ps

# الوصول للحاوية
docker-compose exec web bash
```

### ☁️ **النشر السحابي:**
```bash
# AWS
make aws-deploy

# Google Cloud
make gcp-deploy

# Azure
make azure-deploy
```

### 📱 **تطوير التطبيق الجوال:**
```bash
# الانتقال لمجلد التطبيق
cd mobile_app

# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق
flutter run
```

---

## 🧪 **الاختبار**

### ✅ **تشغيل الاختبارات:**
```bash
# جميع الاختبارات
make test

# اختبارات PHP فقط
composer test

# اختبارات JavaScript فقط
npm test

# اختبارات التطبيق الجوال
cd mobile_app && flutter test
```

### 🔍 **فحص الجودة:**
```bash
# تحليل الكود
make analyze

# فحص الأمان
make security-scan

# فحص الأداء
make performance-test
```

---

## 📊 **المراقبة والتحليل**

### 📈 **لوحة المراقبة:**
```bash
# تشغيل Prometheus و Grafana
make monitoring-start

# الوصول للوحات
open http://localhost:3000  # Grafana
open http://localhost:9090  # Prometheus
```

### 📋 **السجلات:**
```bash
# عرض السجلات المباشرة
tail -f website/logs/app.log

# تحليل السجلات
make logs-analysis
```

---

## 🆘 **استكشاف الأخطاء**

### ❌ **مشاكل شائعة:**

#### **خطأ في قاعدة البيانات:**
```bash
# التحقق من الاتصال
php artisan tinker
>>> DB::connection()->getPdo();

# إعادة تشغيل MySQL
sudo systemctl restart mysql
```

#### **خطأ في الصلاحيات:**
```bash
# إصلاح صلاحيات الملفات
chmod -R 755 website/
chmod -R 777 website/storage/
chmod -R 777 website/cache/
```

#### **خطأ في Redis:**
```bash
# التحقق من Redis
redis-cli ping

# إعادة تشغيل Redis
sudo systemctl restart redis
```

### 🔧 **أدوات التشخيص:**
```bash
# فحص صحة النظام
make health-check

# تشخيص شامل
make troubleshoot

# إصلاح المشاكل الشائعة
make fix-common-issues
```

---

## 📚 **الخطوات التالية**

### 🎓 **تعلم المزيد:**
1. **اقرأ [وثائق API](API_DOCUMENTATION.md)** لفهم النظام
2. **ادرس [هندسة النظام](ARCHITECTURE.md)** للتطوير المتقدم
3. **راجع [دليل المساهمة](CONTRIBUTING.md)** للمشاركة

### 🚀 **تطوير متقدم:**
1. **إعداد [الخدمات المصغرة](MICROSERVICES.md)**
2. **نشر على [Kubernetes](KUBERNETES.md)**
3. **تكامل [الذكاء الاصطناعي](AI_INTEGRATION.md)**

### 🌟 **ميزات إضافية:**
1. **إعداد [البث المباشر](LIVE_STREAMING.md)**
2. **تطوير [التطبيقات الجوالة](MOBILE_DEVELOPMENT.md)**
3. **النشر [السحابي](CLOUD_DEPLOYMENT.md)**

---

## 💬 **الدعم والمساعدة**

### 🤝 **المجتمع:**
- **GitHub Discussions**: للنقاشات العامة
- **Discord Server**: للدردشة المباشرة
- **Stack Overflow**: للأسئلة التقنية

### 📧 **التواصل:**
- **Email**: <EMAIL>
- **Twitter**: @StreamingPlatformAR
- **LinkedIn**: Arabic Streaming Platform

### 🐛 **الإبلاغ عن الأخطاء:**
1. **ابحث في Issues** الموجودة
2. **أنشئ Issue جديد** مع التفاصيل
3. **أرفق ملفات السجلات** إن أمكن

---

## 🎉 **تهانينا!**

**لقد أكملت إعداد منصة البث العربية بنجاح! 🚀**

**الآن يمكنك:**
- ✅ رفع ومشاركة الفيديوهات
- ✅ إدارة المستخدمين والمحتوى
- ✅ مراقبة الأداء والإحصائيات
- ✅ تخصيص المنصة حسب احتياجاتك

**استمتع بالاستخدام وشارك إبداعاتك مع العالم! 🌟**

---

**📝 آخر تحديث: 15 يناير 2024**
