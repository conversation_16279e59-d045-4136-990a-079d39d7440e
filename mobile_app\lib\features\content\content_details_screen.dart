import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:go_router/go_router.dart';

import '../../core/constants/app_constants.dart';

/// 🎬 شاشة تفاصيل المحتوى
class ContentDetailsScreen extends ConsumerStatefulWidget {
  final String contentId;

  const ContentDetailsScreen({
    super.key,
    required this.contentId,
  });

  @override
  ConsumerState<ContentDetailsScreen> createState() => _ContentDetailsScreenState();
}

class _ContentDetailsScreenState extends ConsumerState<ContentDetailsScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isAppBarExpanded = true;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final isExpanded = _scrollController.offset < 200;
    if (isExpanded != _isAppBarExpanded) {
      setState(() {
        _isAppBarExpanded = isExpanded;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // محاكاة بيانات المحتوى
    final content = _getMockContent();

    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // شريط التطبيق مع الصورة
          _buildSliverAppBar(content),
          
          // معلومات المحتوى
          _buildContentInfo(content),
          
          // أزرار الإجراءات
          _buildActionButtons(content),
          
          // الوصف
          _buildDescription(content),
          
          // التقييمات والمراجعات
          _buildRatingsSection(content),
          
          // الحلقات (للمسلسلات)
          if (content['type'] == 'series') _buildEpisodesSection(content),
          
          // محتوى مشابه
          _buildSimilarContent(),
          
          // التعليقات
          _buildCommentsSection(),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar(Map<String, dynamic> content) {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      backgroundColor: AppConstants.darkColor,
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            // صورة الخلفية
            CachedNetworkImage(
              imageUrl: content['banner'] ?? 'https://via.placeholder.com/400x300',
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: AppConstants.secondaryColor,
              ),
              errorWidget: (context, url, error) => Container(
                color: AppConstants.secondaryColor,
                child: const Icon(Icons.movie, size: 50),
              ),
            ),
            
            // تدرج للنص
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.7),
                  ],
                ),
              ),
            ),
            
            // معلومات أساسية
            Positioned(
              bottom: 20,
              left: 16,
              right: 16,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    content['title'],
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppConstants.primaryColor,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          content['type'] == 'movie' ? 'فيلم' : 'مسلسل',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(
                        Icons.star,
                        color: Colors.amber,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        content['rating'].toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        content['year'].toString(),
                        style: const TextStyle(
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.share),
          onPressed: () {
            // مشاركة المحتوى
          },
        ),
        IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: () {
            _showMoreOptions(context);
          },
        ),
      ],
    );
  }

  Widget _buildContentInfo(Map<String, dynamic> content) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // الملصق
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: CachedNetworkImage(
                imageUrl: content['poster'] ?? 'https://via.placeholder.com/120x180',
                width: 100,
                height: 150,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  width: 100,
                  height: 150,
                  color: AppConstants.secondaryColor,
                ),
              ),
            ),
            
            const SizedBox(width: 16),
            
            // المعلومات
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    content['title'],
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildInfoRow('النوع', content['genre']),
                  _buildInfoRow('المدة', content['duration']),
                  _buildInfoRow('اللغة', content['language']),
                  _buildInfoRow('الإنتاج', content['production']),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        color: Colors.amber,
                        size: 20,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${content['rating']}/10',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '(${content['reviews_count']} تقييم)',
                        style: const TextStyle(
                          color: AppConstants.textMutedColor,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              color: AppConstants.textMutedColor,
              fontSize: 12,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(Map<String, dynamic> content) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            // زر المشاهدة
            Expanded(
              flex: 2,
              child: ElevatedButton.icon(
                onPressed: () {
                  context.push('/player/${widget.contentId}');
                },
                icon: const Icon(Icons.play_arrow),
                label: const Text('مشاهدة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            
            const SizedBox(width: 8),
            
            // زر المفضلة
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  // إضافة/إزالة من المفضلة
                },
                icon: const Icon(Icons.favorite_border),
                label: const Text('مفضلة'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            
            const SizedBox(width: 8),
            
            // زر قائمة المشاهدة
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  // إضافة لقائمة المشاهدة
                },
                icon: const Icon(Icons.bookmark_border),
                label: const Text('لاحقاً'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescription(Map<String, dynamic> content) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'القصة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              content['description'],
              style: const TextStyle(
                height: 1.6,
                color: AppConstants.textMutedColor,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: (content['tags'] as List<String>).map((tag) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppConstants.primaryColor.withOpacity(0.3),
                    ),
                  ),
                  child: Text(
                    tag,
                    style: const TextStyle(
                      color: AppConstants.primaryColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingsSection(Map<String, dynamic> content) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppConstants.secondaryColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'التقييمات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                // التقييم العام
                Column(
                  children: [
                    Text(
                      content['rating'].toString(),
                      style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                    Row(
                      children: List.generate(5, (index) {
                        return Icon(
                          index < content['rating'] / 2
                              ? Icons.star
                              : Icons.star_border,
                          color: Colors.amber,
                          size: 16,
                        );
                      }),
                    ),
                    Text(
                      '${content['reviews_count']} تقييم',
                      style: const TextStyle(
                        color: AppConstants.textMutedColor,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(width: 24),
                
                // توزيع النجوم
                Expanded(
                  child: Column(
                    children: List.generate(5, (index) {
                      final stars = 5 - index;
                      final percentage = (stars * 20).toDouble();
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Row(
                          children: [
                            Text(
                              '$stars',
                              style: const TextStyle(fontSize: 12),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: LinearProgressIndicator(
                                value: percentage / 100,
                                backgroundColor: AppConstants.borderColor,
                                valueColor: const AlwaysStoppedAnimation<Color>(
                                  Colors.amber,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '${percentage.toInt()}%',
                              style: const TextStyle(
                                fontSize: 12,
                                color: AppConstants.textMutedColor,
                              ),
                            ),
                          ],
                        ),
                      );
                    }),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEpisodesSection(Map<String, dynamic> content) {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'الحلقات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: 10,
              itemBuilder: (context, index) {
                return _buildEpisodeCard(index + 1);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEpisodeCard(int episodeNumber) {
    return Container(
      width: 200,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        color: AppConstants.secondaryColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: AppConstants.borderColor,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.play_circle_filled,
                      size: 40,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      '45 دقيقة',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الحلقة $episodeNumber',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'عنوان الحلقة $episodeNumber',
                  style: const TextStyle(
                    fontSize: 10,
                    color: AppConstants.textMutedColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimilarContent() {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'محتوى مشابه',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(
            height: 180,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: 10,
              itemBuilder: (context, index) {
                return _buildSimilarContentCard(index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimilarContentCard(int index) {
    return Container(
      width: 120,
      margin: const EdgeInsets.only(right: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Container(
                color: AppConstants.secondaryColor,
                child: const Center(
                  child: Icon(
                    Icons.movie,
                    size: 40,
                    color: AppConstants.textMutedColor,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'محتوى مشابه ${index + 1}',
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildCommentsSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'التعليقات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            // قائمة التعليقات
            ...List.generate(3, (index) => _buildCommentCard(index)),
            const SizedBox(height: 100), // مساحة للتنقل السفلي
          ],
        ),
      ),
    );
  }

  Widget _buildCommentCard(int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppConstants.secondaryColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: AppConstants.primaryColor,
                child: Text(
                  'م${index + 1}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'مستخدم ${index + 1}',
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const Spacer(),
              Row(
                children: List.generate(5, (starIndex) {
                  return Icon(
                    starIndex < 4 ? Icons.star : Icons.star_border,
                    color: Colors.amber,
                    size: 14,
                  );
                }),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'تعليق رائع على هذا المحتوى. أعجبني كثيراً وأنصح بمشاهدته.',
            style: TextStyle(
              fontSize: 12,
              color: AppConstants.textMutedColor,
            ),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getMockContent() {
    return {
      'id': widget.contentId,
      'title': 'فيلم رائع',
      'type': 'movie',
      'rating': 8.5,
      'year': 2024,
      'genre': 'أكشن، دراما',
      'duration': '2 ساعة 15 دقيقة',
      'language': 'العربية',
      'production': 'استوديو الأفلام',
      'reviews_count': 1250,
      'description': 'قصة مثيرة عن بطل يواجه تحديات كبيرة في رحلة ملحمية مليئة بالإثارة والتشويق. يجب عليه أن يتغلب على العقبات ويحارب الأشرار لإنقاذ العالم.',
      'tags': ['أكشن', 'مغامرة', 'إثارة', 'دراما'],
      'poster': 'https://via.placeholder.com/300x450',
      'banner': 'https://via.placeholder.com/800x450',
    };
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('تحميل'),
              onTap: () {
                Navigator.pop(context);
                // تحميل المحتوى
              },
            ),
            ListTile(
              leading: const Icon(Icons.report),
              title: const Text('إبلاغ'),
              onTap: () {
                Navigator.pop(context);
                // إبلاغ عن المحتوى
              },
            ),
          ],
        ),
      ),
    );
  }
}
