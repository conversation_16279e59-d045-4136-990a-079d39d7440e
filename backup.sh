#!/bin/bash
# 💾 سكريبت النسخ الاحتياطي لمنصة البث العربية

set -e

# الإعدادات
BACKUP_DIR="./backups"
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="streaming_platform_backup_$DATE"
RETENTION_DAYS=30

# الألوان
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# دوال المساعدة
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "💾 منصة البث العربية - النسخ الاحتياطي"
    echo "=================================================="
    echo -e "${NC}"
}

# إنشاء مجلد النسخ الاحتياطي
create_backup_dir() {
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
        print_success "تم إنشاء مجلد النسخ الاحتياطي"
    fi
    
    # إنشاء مجلد النسخة الحالية
    mkdir -p "$BACKUP_DIR/$BACKUP_NAME"
}

# نسخ احتياطي لقاعدة البيانات
backup_database() {
    print_info "إنشاء نسخة احتياطية لقاعدة البيانات..."
    
    # الحصول على كلمة مرور قاعدة البيانات
    DB_PASS=$(grep DB_PASS .env | cut -d '=' -f2)
    
    # تصدير قاعدة البيانات
    docker-compose exec -T database mysqldump \
        -u streaming_user \
        -p"$DB_PASS" \
        --single-transaction \
        --routines \
        --triggers \
        streaming_platform > "$BACKUP_DIR/$BACKUP_NAME/database.sql"
    
    # ضغط ملف قاعدة البيانات
    gzip "$BACKUP_DIR/$BACKUP_NAME/database.sql"
    
    print_success "تم إنشاء نسخة احتياطية لقاعدة البيانات"
}

# نسخ احتياطي للملفات المرفوعة
backup_uploads() {
    print_info "إنشاء نسخة احتياطية للملفات المرفوعة..."
    
    if [ -d "./uploads" ]; then
        tar -czf "$BACKUP_DIR/$BACKUP_NAME/uploads.tar.gz" ./uploads/
        print_success "تم إنشاء نسخة احتياطية للملفات المرفوعة"
    else
        print_warning "مجلد الملفات المرفوعة غير موجود"
    fi
}

# نسخ احتياطي للإعدادات
backup_config() {
    print_info "إنشاء نسخة احتياطية للإعدادات..."
    
    # نسخ ملفات الإعدادات
    cp .env "$BACKUP_DIR/$BACKUP_NAME/" 2>/dev/null || true
    cp docker-compose.yml "$BACKUP_DIR/$BACKUP_NAME/" 2>/dev/null || true
    cp nginx.conf "$BACKUP_DIR/$BACKUP_NAME/" 2>/dev/null || true
    
    # نسخ إعدادات التطبيق
    if [ -f "./website/includes/config.php" ]; then
        cp ./website/includes/config.php "$BACKUP_DIR/$BACKUP_NAME/"
    fi
    
    print_success "تم إنشاء نسخة احتياطية للإعدادات"
}

# نسخ احتياطي للسجلات
backup_logs() {
    print_info "إنشاء نسخة احتياطية للسجلات..."
    
    if [ -d "./logs" ]; then
        tar -czf "$BACKUP_DIR/$BACKUP_NAME/logs.tar.gz" ./logs/
        print_success "تم إنشاء نسخة احتياطية للسجلات"
    else
        print_warning "مجلد السجلات غير موجود"
    fi
}

# نسخ احتياطي لبيانات Redis
backup_redis() {
    print_info "إنشاء نسخة احتياطية لبيانات Redis..."
    
    # حفظ بيانات Redis
    docker-compose exec -T redis redis-cli BGSAVE
    
    # انتظار انتهاء الحفظ
    while [ "$(docker-compose exec -T redis redis-cli LASTSAVE)" = "$(docker-compose exec -T redis redis-cli LASTSAVE)" ]; do
        sleep 1
    done
    
    # نسخ ملف البيانات
    docker-compose exec -T redis cat /data/dump.rdb > "$BACKUP_DIR/$BACKUP_NAME/redis_dump.rdb"
    
    print_success "تم إنشاء نسخة احتياطية لبيانات Redis"
}

# إنشاء ملف معلومات النسخة الاحتياطية
create_backup_info() {
    print_info "إنشاء ملف معلومات النسخة الاحتياطية..."
    
    cat > "$BACKUP_DIR/$BACKUP_NAME/backup_info.txt" << EOF
منصة البث العربية - معلومات النسخة الاحتياطية
================================================

تاريخ الإنشاء: $(date)
اسم النسخة: $BACKUP_NAME
الإصدار: 1.0.0

محتويات النسخة الاحتياطية:
- قاعدة البيانات: database.sql.gz
- الملفات المرفوعة: uploads.tar.gz
- السجلات: logs.tar.gz
- بيانات Redis: redis_dump.rdb
- الإعدادات: .env, config.php, docker-compose.yml

تعليمات الاستعادة:
1. فك ضغط الملفات
2. استعادة قاعدة البيانات: mysql -u user -p database < database.sql
3. نسخ الملفات المرفوعة إلى مجلد uploads
4. نسخ الإعدادات
5. إعادة تشغيل الخدمات

EOF
    
    print_success "تم إنشاء ملف معلومات النسخة الاحتياطية"
}

# ضغط النسخة الاحتياطية
compress_backup() {
    print_info "ضغط النسخة الاحتياطية..."
    
    cd "$BACKUP_DIR"
    tar -czf "$BACKUP_NAME.tar.gz" "$BACKUP_NAME/"
    rm -rf "$BACKUP_NAME/"
    cd - > /dev/null
    
    # حساب حجم النسخة الاحتياطية
    BACKUP_SIZE=$(du -h "$BACKUP_DIR/$BACKUP_NAME.tar.gz" | cut -f1)
    
    print_success "تم ضغط النسخة الاحتياطية (الحجم: $BACKUP_SIZE)"
}

# حذف النسخ الاحتياطية القديمة
cleanup_old_backups() {
    print_info "حذف النسخ الاحتياطية القديمة (أكثر من $RETENTION_DAYS يوم)..."
    
    find "$BACKUP_DIR" -name "streaming_platform_backup_*.tar.gz" -mtime +$RETENTION_DAYS -delete
    
    print_success "تم حذف النسخ الاحتياطية القديمة"
}

# رفع النسخة الاحتياطية للسحابة (اختياري)
upload_to_cloud() {
    if [ -n "$CLOUD_STORAGE_PATH" ]; then
        print_info "رفع النسخة الاحتياطية للسحابة..."
        
        # مثال لرفع إلى AWS S3
        # aws s3 cp "$BACKUP_DIR/$BACKUP_NAME.tar.gz" "$CLOUD_STORAGE_PATH/"
        
        # مثال لرفع إلى Google Drive
        # gdrive upload "$BACKUP_DIR/$BACKUP_NAME.tar.gz"
        
        print_success "تم رفع النسخة الاحتياطية للسحابة"
    fi
}

# إرسال إشعار (اختياري)
send_notification() {
    if [ -n "$NOTIFICATION_EMAIL" ]; then
        print_info "إرسال إشعار بالبريد الإلكتروني..."
        
        # إرسال بريد إلكتروني
        echo "تم إنشاء نسخة احتياطية جديدة: $BACKUP_NAME" | \
        mail -s "نسخة احتياطية - منصة البث" "$NOTIFICATION_EMAIL"
        
        print_success "تم إرسال الإشعار"
    fi
}

# استعادة النسخة الاحتياطية
restore_backup() {
    local backup_file="$1"
    
    if [ -z "$backup_file" ]; then
        print_error "يرجى تحديد ملف النسخة الاحتياطية"
        echo "الاستخدام: $0 restore <backup_file.tar.gz>"
        exit 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        print_error "ملف النسخة الاحتياطية غير موجود: $backup_file"
        exit 1
    fi
    
    print_warning "تحذير: ستتم استبدال البيانات الحالية!"
    read -p "هل تريد المتابعة؟ (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "تم إلغاء عملية الاستعادة"
        exit 0
    fi
    
    print_info "بدء استعادة النسخة الاحتياطية..."
    
    # فك ضغط النسخة الاحتياطية
    RESTORE_DIR="/tmp/restore_$(date +%s)"
    mkdir -p "$RESTORE_DIR"
    tar -xzf "$backup_file" -C "$RESTORE_DIR"
    
    # العثور على مجلد النسخة الاحتياطية
    BACKUP_FOLDER=$(find "$RESTORE_DIR" -name "streaming_platform_backup_*" -type d | head -1)
    
    if [ -z "$BACKUP_FOLDER" ]; then
        print_error "لم يتم العثور على مجلد النسخة الاحتياطية"
        exit 1
    fi
    
    # استعادة قاعدة البيانات
    if [ -f "$BACKUP_FOLDER/database.sql.gz" ]; then
        print_info "استعادة قاعدة البيانات..."
        gunzip -c "$BACKUP_FOLDER/database.sql.gz" | \
        docker-compose exec -T database mysql -u streaming_user -p"$DB_PASS" streaming_platform
        print_success "تم استعادة قاعدة البيانات"
    fi
    
    # استعادة الملفات المرفوعة
    if [ -f "$BACKUP_FOLDER/uploads.tar.gz" ]; then
        print_info "استعادة الملفات المرفوعة..."
        tar -xzf "$BACKUP_FOLDER/uploads.tar.gz" -C ./
        print_success "تم استعادة الملفات المرفوعة"
    fi
    
    # استعادة بيانات Redis
    if [ -f "$BACKUP_FOLDER/redis_dump.rdb" ]; then
        print_info "استعادة بيانات Redis..."
        docker-compose exec -T redis redis-cli FLUSHALL
        docker cp "$BACKUP_FOLDER/redis_dump.rdb" $(docker-compose ps -q redis):/data/dump.rdb
        docker-compose restart redis
        print_success "تم استعادة بيانات Redis"
    fi
    
    # تنظيف
    rm -rf "$RESTORE_DIR"
    
    print_success "تم استعادة النسخة الاحتياطية بنجاح!"
}

# عرض قائمة النسخ الاحتياطية
list_backups() {
    print_info "النسخ الاحتياطية المتاحة:"
    echo ""
    
    if [ -d "$BACKUP_DIR" ]; then
        ls -lh "$BACKUP_DIR"/*.tar.gz 2>/dev/null || print_warning "لا توجد نسخ احتياطية"
    else
        print_warning "مجلد النسخ الاحتياطية غير موجود"
    fi
}

# الدالة الرئيسية
main() {
    print_header
    
    case "${1:-backup}" in
        "backup")
            create_backup_dir
            backup_database
            backup_uploads
            backup_config
            backup_logs
            backup_redis
            create_backup_info
            compress_backup
            cleanup_old_backups
            upload_to_cloud
            send_notification
            print_success "تم إنشاء النسخة الاحتياطية بنجاح: $BACKUP_NAME.tar.gz"
            ;;
        "restore")
            restore_backup "$2"
            ;;
        "list")
            list_backups
            ;;
        "help")
            echo "الاستخدام: $0 [backup|restore|list|help]"
            echo ""
            echo "الأوامر:"
            echo "  backup           - إنشاء نسخة احتياطية (افتراضي)"
            echo "  restore <file>   - استعادة نسخة احتياطية"
            echo "  list             - عرض النسخ الاحتياطية المتاحة"
            echo "  help             - عرض هذه المساعدة"
            ;;
        *)
            print_error "أمر غير معروف: $1"
            echo "استخدم '$0 help' لعرض المساعدة"
            exit 1
            ;;
    esac
}

# تشغيل الدالة الرئيسية
main "$@"
