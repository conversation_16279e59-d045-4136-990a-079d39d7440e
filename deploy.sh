#!/bin/bash
# 🚀 سكريبت النشر التلقائي لمنصة البث العربية

set -e

# الألوان للعرض
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دوال المساعدة
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "🎬 منصة البث العربية - سكريبت النشر التلقائي"
    echo "=================================================="
    echo -e "${NC}"
}

# فحص المتطلبات
check_requirements() {
    print_info "فحص المتطلبات..."
    
    # فحص Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker غير مثبت. يرجى تثبيت Docker أولاً."
        exit 1
    fi
    
    # فحص Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose غير مثبت. يرجى تثبيت Docker Compose أولاً."
        exit 1
    fi
    
    print_success "جميع المتطلبات متوفرة"
}

# إعداد البيئة
setup_environment() {
    print_info "إعداد البيئة..."
    
    # إنشاء ملف .env إذا لم يكن موجوداً
    if [ ! -f .env ]; then
        print_info "إنشاء ملف .env..."
        cat > .env << EOF
# إعدادات قاعدة البيانات
DB_HOST=database
DB_NAME=streaming_platform
DB_USER=streaming_user
DB_PASS=streaming_password_$(date +%s)
MYSQL_ROOT_PASSWORD=root_password_$(date +%s)

# إعدادات Redis
REDIS_HOST=redis
REDIS_PORT=6379

# إعدادات التطبيق
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost

# إعدادات البريد الإلكتروني
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=
MAIL_PASSWORD=
EOF
        print_success "تم إنشاء ملف .env"
    fi
    
    # إنشاء المجلدات المطلوبة
    mkdir -p logs uploads cache backups ssl
    chmod 755 logs uploads cache backups
    
    print_success "تم إعداد البيئة"
}

# بناء الصور
build_images() {
    print_info "بناء صور Docker..."
    
    docker-compose build --no-cache
    
    print_success "تم بناء الصور بنجاح"
}

# تشغيل الخدمات
start_services() {
    print_info "تشغيل الخدمات..."
    
    # إيقاف الخدمات الموجودة
    docker-compose down
    
    # تشغيل الخدمات الجديدة
    docker-compose up -d
    
    print_success "تم تشغيل الخدمات"
}

# انتظار الخدمات
wait_for_services() {
    print_info "انتظار جاهزية الخدمات..."
    
    # انتظار قاعدة البيانات
    print_info "انتظار قاعدة البيانات..."
    while ! docker-compose exec -T database mysqladmin ping -h localhost --silent; do
        sleep 2
    done
    print_success "قاعدة البيانات جاهزة"
    
    # انتظار Redis
    print_info "انتظار Redis..."
    while ! docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; do
        sleep 2
    done
    print_success "Redis جاهز"
    
    # انتظار التطبيق
    print_info "انتظار التطبيق..."
    while ! curl -f http://localhost/test.php > /dev/null 2>&1; do
        sleep 2
    done
    print_success "التطبيق جاهز"
}

# تشغيل الاختبارات
run_tests() {
    print_info "تشغيل الاختبارات..."
    
    # اختبار الاتصال بقاعدة البيانات
    if docker-compose exec -T app php -r "
        try {
            \$pdo = new PDO('mysql:host=database;dbname=streaming_platform', 'streaming_user', getenv('DB_PASS'));
            echo 'اتصال قاعدة البيانات: نجح\n';
        } catch (Exception \$e) {
            echo 'اتصال قاعدة البيانات: فشل\n';
            exit(1);
        }
    "; then
        print_success "اختبار قاعدة البيانات نجح"
    else
        print_error "اختبار قاعدة البيانات فشل"
        exit 1
    fi
    
    # اختبار الاتصال بـ Redis
    if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
        print_success "اختبار Redis نجح"
    else
        print_error "اختبار Redis فشل"
        exit 1
    fi
    
    # اختبار التطبيق
    if curl -f http://localhost/test.php > /dev/null 2>&1; then
        print_success "اختبار التطبيق نجح"
    else
        print_error "اختبار التطبيق فشل"
        exit 1
    fi
}

# عرض معلومات النشر
show_deployment_info() {
    print_success "تم نشر منصة البث بنجاح! 🎉"
    echo ""
    echo "🔗 الروابط المهمة:"
    echo "   الموقع الرئيسي: http://localhost"
    echo "   لوحة التحكم: http://localhost/admin"
    echo "   صفحة الاختبار: http://localhost/test.php"
    echo "   phpMyAdmin: http://localhost:8081"
    echo "   Redis Commander: http://localhost:8082"
    echo "   Mailhog: http://localhost:8025"
    echo "   Grafana: http://localhost:3000"
    echo "   Prometheus: http://localhost:9090"
    echo ""
    echo "🔐 بيانات الدخول الافتراضية:"
    echo "   البريد الإلكتروني: <EMAIL>"
    echo "   كلمة المرور: password"
    echo ""
    echo "📊 مراقبة الخدمات:"
    echo "   docker-compose ps"
    echo "   docker-compose logs -f"
    echo ""
    echo "🛑 إيقاف الخدمات:"
    echo "   docker-compose down"
}

# تنظيف الموارد
cleanup() {
    print_info "تنظيف الموارد..."
    
    # حذف الصور غير المستخدمة
    docker image prune -f
    
    # حذف الشبكات غير المستخدمة
    docker network prune -f
    
    print_success "تم تنظيف الموارد"
}

# الدالة الرئيسية
main() {
    print_header
    
    # فحص المعاملات
    case "${1:-deploy}" in
        "deploy")
            check_requirements
            setup_environment
            build_images
            start_services
            wait_for_services
            run_tests
            show_deployment_info
            ;;
        "start")
            start_services
            wait_for_services
            show_deployment_info
            ;;
        "stop")
            print_info "إيقاف الخدمات..."
            docker-compose down
            print_success "تم إيقاف الخدمات"
            ;;
        "restart")
            print_info "إعادة تشغيل الخدمات..."
            docker-compose restart
            wait_for_services
            show_deployment_info
            ;;
        "logs")
            docker-compose logs -f
            ;;
        "status")
            docker-compose ps
            ;;
        "cleanup")
            cleanup
            ;;
        "help")
            echo "الاستخدام: $0 [deploy|start|stop|restart|logs|status|cleanup|help]"
            echo ""
            echo "الأوامر:"
            echo "  deploy   - نشر كامل للمنصة (افتراضي)"
            echo "  start    - تشغيل الخدمات"
            echo "  stop     - إيقاف الخدمات"
            echo "  restart  - إعادة تشغيل الخدمات"
            echo "  logs     - عرض السجلات"
            echo "  status   - عرض حالة الخدمات"
            echo "  cleanup  - تنظيف الموارد"
            echo "  help     - عرض هذه المساعدة"
            ;;
        *)
            print_error "أمر غير معروف: $1"
            echo "استخدم '$0 help' لعرض المساعدة"
            exit 1
            ;;
    esac
}

# تشغيل الدالة الرئيسية
main "$@"
