<?xml version="1.0"?>
<!-- 🔍 إعدادات Psalm للتحليل الثابت المتقدم - منصة البث العربية -->
<psalm
    errorLevel="3"
    resolveFromConfigFile="true"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="https://getpsalm.org/schema/config"
    xsi:schemaLocation="https://getpsalm.org/schema/config vendor/vimeo/psalm/config.xsd"
    findUnusedCode="true"
    findUnusedVariablesAndParams="true"
    findUnusedPsalmSuppress="true"
    checkForThrowsDocblock="true"
    checkForThrowsInGlobalScope="true"
    ensureArrayStringOffsetsExist="true"
    ensureArrayIntOffsetsExist="true"
    reportMixedIssues="true"
    strictBinaryOperands="true"
    rememberPropertyAssignmentsAfterCall="true"
    checkForThrowsDocblock="true"
    throwExceptionOnError="false"
    hideExternalErrors="false"
    cacheDirectory="var/cache/psalm"
    allowPhpStormGenerics="true"
    allowCoercionFromStringToClassConst="true"
    allowStringToStandInForClass="true"
    memoizeMethodCallResults="true"
    hoistConstants="true"
    addParamTypehints="true"
    addReturnTypehints="true"
    addVoidReturnType="true"
    runTaintAnalysis="true">

    <!-- المجلدات للتحليل -->
    <projectFiles>
        <directory name="src" />
        <directory name="includes" />
        <directory name="public" />
        <ignoreFiles>
            <directory name="vendor" />
            <directory name="cache" />
            <directory name="logs" />
            <directory name="coverage" />
            <directory name="node_modules" />
            <directory name="tests" />
            <file name="includes/config.php" />
        </ignoreFiles>
    </projectFiles>

    <!-- إعدادات PHP -->
    <php>
        <ini name="memory_limit" value="512M" />
    </php>

    <!-- المكونات الإضافية -->
    <plugins>
        <pluginClass class="Psalm\PhpUnitPlugin\Plugin"/>
        <pluginClass class="Psalm\Plugin\Symfony\Plugin"/>
        <pluginClass class="Weirdan\DoctrinePsalmPlugin\Plugin"/>
    </plugins>

    <!-- إعدادات التحليل المتقدم -->
    <issueHandlers>
        <!-- تجاهل بعض المشاكل الشائعة -->
        <LessSpecificReturnType errorLevel="info" />
        <MoreSpecificReturnType errorLevel="info" />
        <MissingClosureReturnType errorLevel="info" />
        <MissingReturnType errorLevel="info" />
        <MissingPropertyType errorLevel="info" />
        <InvalidDocblock errorLevel="info" />
        <MissingDocblockType errorLevel="info" />
        
        <!-- تجاهل مشاكل المتغيرات العامة -->
        <UndefinedGlobalVariable>
            <errorLevel type="suppress">
                <referencedVariable name="$_GET" />
                <referencedVariable name="$_POST" />
                <referencedVariable name="$_SESSION" />
                <referencedVariable name="$_COOKIE" />
                <referencedVariable name="$_SERVER" />
                <referencedVariable name="$_FILES" />
                <referencedVariable name="$_ENV" />
                <referencedVariable name="$GLOBALS" />
            </errorLevel>
        </UndefinedGlobalVariable>
        
        <!-- تجاهل مشاكل الدوال المدمجة -->
        <UndefinedFunction>
            <errorLevel type="suppress">
                <referencedFunction name="mysql_*" />
                <referencedFunction name="mysqli_*" />
                <referencedFunction name="curl_*" />
                <referencedFunction name="json_*" />
                <referencedFunction name="array_*" />
                <referencedFunction name="str_*" />
                <referencedFunction name="preg_*" />
                <referencedFunction name="file_*" />
                <referencedFunction name="hash_*" />
                <referencedFunction name="password_*" />
                <referencedFunction name="filter_*" />
                <referencedFunction name="mb_*" />
            </errorLevel>
        </UndefinedFunction>
        
        <!-- تجاهل مشاكل الكلاسات الخارجية -->
        <UndefinedClass>
            <errorLevel type="suppress">
                <referencedClass name="PDO" />
                <referencedClass name="PDOStatement" />
                <referencedClass name="Redis" />
                <referencedClass name="Memcached" />
                <referencedClass name="DateTime" />
                <referencedClass name="DateTimeImmutable" />
                <referencedClass name="Exception" />
                <referencedClass name="Throwable" />
            </errorLevel>
        </UndefinedClass>
        
        <!-- إعدادات خاصة بالمصفوفات -->
        <PossiblyUndefinedArrayOffset errorLevel="info" />
        <PossiblyNullArrayOffset errorLevel="info" />
        <MixedArrayOffset errorLevel="info" />
        <MixedArrayAccess errorLevel="info" />
        
        <!-- إعدادات خاصة بالمتغيرات المختلطة -->
        <MixedReturnStatement errorLevel="info" />
        <MixedInferredReturnType errorLevel="info" />
        <MixedReturnTypeCoercion errorLevel="info" />
        <MixedArgument errorLevel="info" />
        <MixedAssignment errorLevel="info" />
        <MixedArrayAssignment errorLevel="info" />
        <MixedPropertyAssignment errorLevel="info" />
        <MixedMethodCall errorLevel="info" />
        <MixedPropertyFetch errorLevel="info" />
        
        <!-- إعدادات خاصة بالنتائج المحتملة -->
        <PossiblyNullReference errorLevel="info" />
        <PossiblyNullArgument errorLevel="info" />
        <PossiblyNullArrayAccess errorLevel="info" />
        <PossiblyNullPropertyFetch errorLevel="info" />
        <PossiblyNullIterator errorLevel="info" />
        <PossiblyNullOperand errorLevel="info" />
        
        <!-- إعدادات خاصة بالأمان -->
        <TaintedInput>
            <errorLevel type="error" />
        </TaintedInput>
        <TaintedSql>
            <errorLevel type="error" />
        </TaintedSql>
        <TaintedHtml>
            <errorLevel type="error" />
        </TaintedHtml>
        <TaintedShell>
            <errorLevel type="error" />
        </TaintedShell>
        <TaintedInclude>
            <errorLevel type="error" />
        </TaintedInclude>
        
        <!-- إعدادات خاصة بالأداء -->
        <UnusedVariable errorLevel="info" />
        <UnusedParam errorLevel="info" />
        <UnusedProperty errorLevel="info" />
        <UnusedMethod errorLevel="info" />
        <UnusedClass errorLevel="info" />
        <PossiblyUnusedMethod errorLevel="info" />
        <PossiblyUnusedProperty errorLevel="info" />
        <PossiblyUnusedParam errorLevel="info" />
    </issueHandlers>

    <!-- إعدادات التحليل المخصص -->
    <forbiddenFunctions>
        <function name="var_dump" />
        <function name="print_r" />
        <function name="die" />
        <function name="exit" />
        <function name="eval" />
        <function name="exec" />
        <function name="shell_exec" />
        <function name="system" />
        <function name="passthru" />
        <function name="file_get_contents" />
        <function name="file_put_contents" />
        <function name="fopen" />
        <function name="fwrite" />
        <function name="fputs" />
        <function name="mysql_query" />
        <function name="mysql_connect" />
        <function name="mysqli_query" />
        <function name="pg_query" />
        <function name="sqlite_query" />
    </forbiddenFunctions>

    <!-- إعدادات الثوابت العامة -->
    <globals>
        <var name="$_GET" type="array&lt;string, mixed&gt;" />
        <var name="$_POST" type="array&lt;string, mixed&gt;" />
        <var name="$_SESSION" type="array&lt;string, mixed&gt;" />
        <var name="$_COOKIE" type="array&lt;string, mixed&gt;" />
        <var name="$_SERVER" type="array&lt;string, mixed&gt;" />
        <var name="$_FILES" type="array&lt;string, mixed&gt;" />
        <var name="$_ENV" type="array&lt;string, mixed&gt;" />
        <var name="$GLOBALS" type="array&lt;string, mixed&gt;" />
    </globals>

    <!-- إعدادات الدوال المخصصة -->
    <stubs>
        <file name="stubs/custom-functions.phpstub" />
        <file name="stubs/wordpress.phpstub" />
        <file name="stubs/legacy.phpstub" />
    </stubs>

    <!-- إعدادات التحليل التلقائي -->
    <mockClasses>
        <class name="StreamingPlatform\Tests\Mocks\MockDatabase" />
        <class name="StreamingPlatform\Tests\Mocks\MockCache" />
        <class name="StreamingPlatform\Tests\Mocks\MockMailer" />
    </mockClasses>

    <!-- إعدادات التحليل المتقدم للأمان -->
    <taintAnalysis>
        <sinks>
            <sink name="sql" />
            <sink name="html" />
            <sink name="shell" />
            <sink name="include" />
            <sink name="file" />
            <sink name="header" />
            <sink name="cookie" />
            <sink name="ldap" />
        </sinks>
        
        <sources>
            <source name="input" />
            <source name="database" />
            <source name="file" />
            <source name="network" />
        </sources>
    </taintAnalysis>

</psalm>
