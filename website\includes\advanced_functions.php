<?php
/**
 * 🛠️ دوال مساعدة متقدمة للمنصة
 * مجموعة شاملة من الدوال المتطورة والمفيدة
 */

// منع الوصول المباشر
if (!defined('STREAMING_PLATFORM')) {
    die('Access Denied');
}

// ===== دوال الأمان المتقدمة =====

/**
 * تنظيف وتأمين المدخلات
 */
function sanitizeInput($input, $type = 'string') {
    if (is_array($input)) {
        return array_map(function($item) use ($type) {
            return sanitizeInput($item, $type);
        }, $input);
    }
    
    $input = trim($input);
    
    switch ($type) {
        case 'email':
            return filter_var($input, FILTER_SANITIZE_EMAIL);
        case 'url':
            return filter_var($input, FILTER_SANITIZE_URL);
        case 'int':
            return filter_var($input, FILTER_SANITIZE_NUMBER_INT);
        case 'float':
            return filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
        case 'html':
            return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        case 'sql':
            return addslashes($input);
        default:
            return htmlspecialchars(strip_tags($input), ENT_QUOTES, 'UTF-8');
    }
}

/**
 * التحقق من صحة البيانات
 */
function validateInput($input, $rules) {
    $errors = [];
    
    foreach ($rules as $field => $rule) {
        $value = $input[$field] ?? null;
        
        if (isset($rule['required']) && $rule['required'] && empty($value)) {
            $errors[$field] = $rule['message'] ?? "الحقل $field مطلوب";
            continue;
        }
        
        if (!empty($value)) {
            if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
                $errors[$field] = "الحقل $field يجب أن يكون على الأقل {$rule['min_length']} أحرف";
            }
            
            if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
                $errors[$field] = "الحقل $field يجب أن يكون أقل من {$rule['max_length']} حرف";
            }
            
            if (isset($rule['email']) && $rule['email'] && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                $errors[$field] = "البريد الإلكتروني غير صحيح";
            }
            
            if (isset($rule['numeric']) && $rule['numeric'] && !is_numeric($value)) {
                $errors[$field] = "الحقل $field يجب أن يكون رقم";
            }
            
            if (isset($rule['pattern']) && !preg_match($rule['pattern'], $value)) {
                $errors[$field] = $rule['pattern_message'] ?? "تنسيق الحقل $field غير صحيح";
            }
        }
    }
    
    return $errors;
}

/**
 * إنشاء رمز CSRF آمن
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_tokens'])) {
        $_SESSION['csrf_tokens'] = [];
    }
    
    $token = bin2hex(random_bytes(32));
    $_SESSION['csrf_tokens'][$token] = time() + 3600;
    
    foreach ($_SESSION['csrf_tokens'] as $t => $expiry) {
        if ($expiry < time()) {
            unset($_SESSION['csrf_tokens'][$t]);
        }
    }
    
    return $token;
}

/**
 * التحقق من رمز CSRF
 */
function verifyCSRFToken($token) {
    if (!isset($_SESSION['csrf_tokens'][$token])) {
        return false;
    }
    
    if ($_SESSION['csrf_tokens'][$token] < time()) {
        unset($_SESSION['csrf_tokens'][$token]);
        return false;
    }
    
    unset($_SESSION['csrf_tokens'][$token]);
    return true;
}

/**
 * إنشاء كلمة مرور قوية
 */
function generateSecurePassword($length = 12) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    $password = '';
    
    for ($i = 0; $i < $length; $i++) {
        $password .= $chars[random_int(0, strlen($chars) - 1)];
    }
    
    return $password;
}

// ===== دوال إدارة الملفات المتقدمة =====

/**
 * رفع ملف بأمان
 */
function uploadFile($file, $destination, $allowedTypes = [], $maxSize = null) {
    $maxSize = $maxSize ?: (defined('MAX_FILE_SIZE') ? MAX_FILE_SIZE : 10 * 1024 * 1024);
    
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['success' => false, 'error' => 'لم يتم رفع أي ملف'];
    }
    
    if ($file['size'] > $maxSize) {
        return ['success' => false, 'error' => 'حجم الملف كبير جداً'];
    }
    
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!empty($allowedTypes) && !in_array($fileExtension, $allowedTypes)) {
        return ['success' => false, 'error' => 'نوع الملف غير مسموح'];
    }
    
    $fileName = uniqid() . '_' . time() . '.' . $fileExtension;
    $filePath = $destination . $fileName;
    
    if (!is_dir($destination)) {
        mkdir($destination, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        return [
            'success' => true,
            'filename' => $fileName,
            'path' => $filePath,
            'url' => str_replace($_SERVER['DOCUMENT_ROOT'], '', $filePath)
        ];
    } else {
        return ['success' => false, 'error' => 'فشل في رفع الملف'];
    }
}

/**
 * ضغط وتحسين الصور
 */
function optimizeImage($sourcePath, $destinationPath, $quality = 80, $maxWidth = 1920, $maxHeight = 1080) {
    $imageInfo = getimagesize($sourcePath);
    if (!$imageInfo) {
        return false;
    }
    
    $sourceWidth = $imageInfo[0];
    $sourceHeight = $imageInfo[1];
    $mimeType = $imageInfo['mime'];
    
    $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
    if ($ratio < 1) {
        $newWidth = intval($sourceWidth * $ratio);
        $newHeight = intval($sourceHeight * $ratio);
    } else {
        $newWidth = $sourceWidth;
        $newHeight = $sourceHeight;
    }
    
    switch ($mimeType) {
        case 'image/jpeg':
            $sourceImage = imagecreatefromjpeg($sourcePath);
            break;
        case 'image/png':
            $sourceImage = imagecreatefrompng($sourcePath);
            break;
        case 'image/gif':
            $sourceImage = imagecreatefromgif($sourcePath);
            break;
        default:
            return false;
    }
    
    $newImage = imagecreatetruecolor($newWidth, $newHeight);
    
    if ($mimeType === 'image/png') {
        imagealphablending($newImage, false);
        imagesavealpha($newImage, true);
    }
    
    imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $sourceWidth, $sourceHeight);
    
    $result = false;
    switch ($mimeType) {
        case 'image/jpeg':
            $result = imagejpeg($newImage, $destinationPath, $quality);
            break;
        case 'image/png':
            $result = imagepng($newImage, $destinationPath, intval($quality / 10));
            break;
        case 'image/gif':
            $result = imagegif($newImage, $destinationPath);
            break;
    }
    
    imagedestroy($sourceImage);
    imagedestroy($newImage);
    
    return $result;
}

// ===== دوال التخزين المؤقت =====

/**
 * حفظ البيانات في التخزين المؤقت
 */
function cacheSet($key, $data, $expiry = 3600) {
    $cacheDir = __DIR__ . '/../cache/';
    if (!is_dir($cacheDir)) {
        mkdir($cacheDir, 0755, true);
    }
    
    $cacheData = [
        'data' => $data,
        'expiry' => time() + $expiry,
        'created' => time()
    ];
    
    $cacheFile = $cacheDir . md5($key) . '.cache';
    return file_put_contents($cacheFile, serialize($cacheData)) !== false;
}

/**
 * استرجاع البيانات من التخزين المؤقت
 */
function cacheGet($key) {
    $cacheDir = __DIR__ . '/../cache/';
    $cacheFile = $cacheDir . md5($key) . '.cache';
    
    if (!file_exists($cacheFile)) {
        return null;
    }
    
    $cacheData = unserialize(file_get_contents($cacheFile));
    
    if ($cacheData['expiry'] < time()) {
        unlink($cacheFile);
        return null;
    }
    
    return $cacheData['data'];
}

/**
 * حذف البيانات من التخزين المؤقت
 */
function cacheDelete($key) {
    $cacheDir = __DIR__ . '/../cache/';
    $cacheFile = $cacheDir . md5($key) . '.cache';
    
    if (file_exists($cacheFile)) {
        return unlink($cacheFile);
    }
    
    return true;
}

// ===== دوال التحليلات والإحصائيات =====

/**
 * تسجيل نشاط المستخدم
 */
function logUserActivity($action, $data = [], $userId = null) {
    global $db;
    
    if (!$db) return false;
    
    $userId = $userId ?: (getCurrentUser()['id'] ?? null);
    
    try {
        return $db->insert('user_activities', [
            'user_id' => $userId,
            'action' => $action,
            'data' => json_encode($data),
            'ip_address' => getUserIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        error_log("خطأ في تسجيل النشاط: " . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على إحصائيات المحتوى
 */
function getContentStats($contentId) {
    global $db;
    
    if (!$db) return [];
    
    $cacheKey = "content_stats_$contentId";
    $stats = cacheGet($cacheKey);
    
    if ($stats === null) {
        try {
            $stats = [
                'views' => $db->selectOne("SELECT view_count FROM content WHERE id = ?", [$contentId])['view_count'] ?? 0,
                'ratings' => $db->selectOne("SELECT AVG(rating) as avg_rating, COUNT(*) as total_ratings FROM content_ratings WHERE content_id = ?", [$contentId]),
                'favorites' => $db->selectOne("SELECT COUNT(*) as count FROM user_favorites WHERE content_id = ?", [$contentId])['count'] ?? 0,
                'comments' => $db->selectOne("SELECT COUNT(*) as count FROM content_comments WHERE content_id = ?", [$contentId])['count'] ?? 0
            ];
            
            cacheSet($cacheKey, $stats, 300); // 5 دقائق
        } catch (Exception $e) {
            error_log("خطأ في الحصول على إحصائيات المحتوى: " . $e->getMessage());
            $stats = [];
        }
    }
    
    return $stats;
}

// ===== دوال الإشعارات =====

/**
 * إرسال إشعار للمستخدم
 */
function sendNotification($userId, $title, $message, $type = 'info', $data = []) {
    global $db;
    
    if (!$db) return false;
    
    try {
        return $db->insert('notifications', [
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'data' => json_encode($data),
            'is_read' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        error_log("خطأ في إرسال الإشعار: " . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على إشعارات المستخدم
 */
function getUserNotifications($userId, $limit = 10, $unreadOnly = false) {
    global $db;
    
    if (!$db) return [];
    
    $whereClause = "user_id = ?";
    $params = [$userId];
    
    if ($unreadOnly) {
        $whereClause .= " AND is_read = 0";
    }
    
    try {
        return $db->select(
            "SELECT * FROM notifications WHERE $whereClause ORDER BY created_at DESC LIMIT $limit",
            $params
        );
    } catch (Exception $e) {
        error_log("خطأ في الحصول على الإشعارات: " . $e->getMessage());
        return [];
    }
}

// ===== دوال مساعدة أخرى =====

/**
 * تحويل الوقت إلى تنسيق نسبي
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة';
    if ($time < 86400) return floor($time/3600) . ' ساعة';
    if ($time < 2592000) return floor($time/86400) . ' يوم';
    if ($time < 31536000) return floor($time/2592000) . ' شهر';
    
    return floor($time/31536000) . ' سنة';
}

/**
 * تنسيق حجم الملف
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, 2) . ' ' . $units[$i];
}

/**
 * إنشاء slug من النص العربي
 */
function createSlug($text) {
    $text = trim($text);
    $text = preg_replace('/[^\p{L}\p{N}\s\-_]/u', '', $text);
    $text = preg_replace('/[\s\-_]+/', '-', $text);
    $text = trim($text, '-');
    
    return strtolower($text);
}

/**
 * التحقق من صحة URL
 */
function isValidUrl($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

/**
 * الحصول على عنوان IP الحقيقي للمستخدم
 */
function getUserIP() {
    $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            $ip = $_SERVER[$key];
            if (strpos($ip, ',') !== false) {
                $ip = explode(',', $ip)[0];
            }
            $ip = trim($ip);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}
