/**
 * 🧪 اختبارات التكامل الشاملة - منصة البث العربية
 * Integration Tests for Arabic Streaming Platform
 */

const request = require('supertest');
const { expect } = require('chai');
const app = require('../website/public/api/index.php');

describe('🚀 اختبارات التكامل الشاملة', () => {
    let authToken;
    let userId;
    let videoId;

    // إعداد قبل جميع الاختبارات
    before(async () => {
        console.log('🔧 إعداد بيئة الاختبار...');
        
        // تنظيف قاعدة البيانات
        await cleanDatabase();
        
        // إنشاء بيانات اختبار أساسية
        await seedTestData();
    });

    // تنظيف بعد جميع الاختبارات
    after(async () => {
        console.log('🧹 تنظيف بيئة الاختبار...');
        await cleanDatabase();
    });

    describe('🔐 اختبارات المصادقة والتفويض', () => {
        
        it('يجب أن يسمح بتسجيل مستخدم جديد', async () => {
            const userData = {
                username: 'testuser',
                email: '<EMAIL>',
                password: 'SecurePass123!',
                full_name: 'مستخدم تجريبي'
            };

            const response = await request(app)
                .post('/api/auth/register')
                .send(userData)
                .expect(201);

            expect(response.body).to.have.property('success', true);
            expect(response.body).to.have.property('user');
            expect(response.body.user).to.have.property('id');
            
            userId = response.body.user.id;
        });

        it('يجب أن يسمح بتسجيل الدخول بالبيانات الصحيحة', async () => {
            const loginData = {
                email: '<EMAIL>',
                password: 'SecurePass123!'
            };

            const response = await request(app)
                .post('/api/auth/login')
                .send(loginData)
                .expect(200);

            expect(response.body).to.have.property('success', true);
            expect(response.body).to.have.property('token');
            expect(response.body).to.have.property('user');
            
            authToken = response.body.token;
        });

        it('يجب أن يرفض تسجيل الدخول بكلمة مرور خاطئة', async () => {
            const loginData = {
                email: '<EMAIL>',
                password: 'WrongPassword'
            };

            const response = await request(app)
                .post('/api/auth/login')
                .send(loginData)
                .expect(401);

            expect(response.body).to.have.property('success', false);
            expect(response.body).to.have.property('message');
        });

        it('يجب أن يحمي المسارات المحمية', async () => {
            const response = await request(app)
                .get('/api/user/profile')
                .expect(401);

            expect(response.body).to.have.property('success', false);
        });

        it('يجب أن يسمح بالوصول للمسارات المحمية مع التوكن', async () => {
            const response = await request(app)
                .get('/api/user/profile')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body).to.have.property('success', true);
            expect(response.body).to.have.property('user');
        });
    });

    describe('📺 اختبارات إدارة المحتوى', () => {
        
        it('يجب أن يسمح برفع فيديو جديد', async () => {
            const videoData = {
                title: 'فيديو تجريبي',
                description: 'وصف الفيديو التجريبي',
                category: 'تعليمي',
                tags: ['تجريبي', 'اختبار'],
                privacy: 'public'
            };

            const response = await request(app)
                .post('/api/content/upload')
                .set('Authorization', `Bearer ${authToken}`)
                .field('title', videoData.title)
                .field('description', videoData.description)
                .field('category', videoData.category)
                .field('tags', JSON.stringify(videoData.tags))
                .field('privacy', videoData.privacy)
                .attach('video', 'tests/fixtures/sample-video.mp4')
                .expect(201);

            expect(response.body).to.have.property('success', true);
            expect(response.body).to.have.property('video');
            expect(response.body.video).to.have.property('id');
            
            videoId = response.body.video.id;
        });

        it('يجب أن يعرض قائمة الفيديوهات العامة', async () => {
            const response = await request(app)
                .get('/api/content/videos')
                .expect(200);

            expect(response.body).to.have.property('success', true);
            expect(response.body).to.have.property('videos');
            expect(response.body.videos).to.be.an('array');
        });

        it('يجب أن يعرض تفاصيل فيديو محدد', async () => {
            const response = await request(app)
                .get(`/api/content/videos/${videoId}`)
                .expect(200);

            expect(response.body).to.have.property('success', true);
            expect(response.body).to.have.property('video');
            expect(response.body.video).to.have.property('title', 'فيديو تجريبي');
        });

        it('يجب أن يسمح بتحديث معلومات الفيديو', async () => {
            const updateData = {
                title: 'فيديو تجريبي محدث',
                description: 'وصف محدث للفيديو'
            };

            const response = await request(app)
                .put(`/api/content/videos/${videoId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .send(updateData)
                .expect(200);

            expect(response.body).to.have.property('success', true);
            expect(response.body.video).to.have.property('title', 'فيديو تجريبي محدث');
        });

        it('يجب أن يسمح بحذف الفيديو', async () => {
            const response = await request(app)
                .delete(`/api/content/videos/${videoId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body).to.have.property('success', true);
        });
    });

    describe('🔍 اختبارات البحث والفلترة', () => {
        
        before(async () => {
            // إنشاء فيديوهات للبحث
            await createTestVideos();
        });

        it('يجب أن يبحث في الفيديوهات بالعنوان', async () => {
            const response = await request(app)
                .get('/api/search/videos?q=تجريبي')
                .expect(200);

            expect(response.body).to.have.property('success', true);
            expect(response.body).to.have.property('results');
            expect(response.body.results).to.be.an('array');
        });

        it('يجب أن يفلتر الفيديوهات حسب الفئة', async () => {
            const response = await request(app)
                .get('/api/content/videos?category=تعليمي')
                .expect(200);

            expect(response.body).to.have.property('success', true);
            expect(response.body.videos).to.be.an('array');
        });

        it('يجب أن يرتب النتائج حسب التاريخ', async () => {
            const response = await request(app)
                .get('/api/content/videos?sort=date&order=desc')
                .expect(200);

            expect(response.body).to.have.property('success', true);
            expect(response.body.videos).to.be.an('array');
            
            // التحقق من الترتيب
            if (response.body.videos.length > 1) {
                const firstDate = new Date(response.body.videos[0].created_at);
                const secondDate = new Date(response.body.videos[1].created_at);
                expect(firstDate.getTime()).to.be.greaterThanOrEqual(secondDate.getTime());
            }
        });
    });

    describe('👤 اختبارات إدارة المستخدمين', () => {
        
        it('يجب أن يعرض ملف المستخدم الشخصي', async () => {
            const response = await request(app)
                .get('/api/user/profile')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body).to.have.property('success', true);
            expect(response.body.user).to.have.property('email', '<EMAIL>');
        });

        it('يجب أن يسمح بتحديث الملف الشخصي', async () => {
            const updateData = {
                full_name: 'اسم محدث',
                bio: 'نبذة شخصية محدثة'
            };

            const response = await request(app)
                .put('/api/user/profile')
                .set('Authorization', `Bearer ${authToken}`)
                .send(updateData)
                .expect(200);

            expect(response.body).to.have.property('success', true);
            expect(response.body.user).to.have.property('full_name', 'اسم محدث');
        });

        it('يجب أن يسمح بتغيير كلمة المرور', async () => {
            const passwordData = {
                current_password: 'SecurePass123!',
                new_password: 'NewSecurePass456!',
                confirm_password: 'NewSecurePass456!'
            };

            const response = await request(app)
                .put('/api/user/change-password')
                .set('Authorization', `Bearer ${authToken}`)
                .send(passwordData)
                .expect(200);

            expect(response.body).to.have.property('success', true);
        });
    });

    describe('📊 اختبارات الإحصائيات والتحليلات', () => {
        
        it('يجب أن يسجل مشاهدة الفيديو', async () => {
            const response = await request(app)
                .post(`/api/analytics/view/${videoId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body).to.have.property('success', true);
        });

        it('يجب أن يعرض إحصائيات المستخدم', async () => {
            const response = await request(app)
                .get('/api/analytics/user/stats')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body).to.have.property('success', true);
            expect(response.body).to.have.property('stats');
        });

        it('يجب أن يعرض الفيديوهات الأكثر مشاهدة', async () => {
            const response = await request(app)
                .get('/api/analytics/trending')
                .expect(200);

            expect(response.body).to.have.property('success', true);
            expect(response.body).to.have.property('videos');
        });
    });

    describe('💬 اختبارات التعليقات والتفاعل', () => {
        
        it('يجب أن يسمح بإضافة تعليق', async () => {
            const commentData = {
                content: 'تعليق تجريبي رائع!',
                video_id: videoId
            };

            const response = await request(app)
                .post('/api/comments')
                .set('Authorization', `Bearer ${authToken}`)
                .send(commentData)
                .expect(201);

            expect(response.body).to.have.property('success', true);
            expect(response.body).to.have.property('comment');
        });

        it('يجب أن يعرض تعليقات الفيديو', async () => {
            const response = await request(app)
                .get(`/api/comments/video/${videoId}`)
                .expect(200);

            expect(response.body).to.have.property('success', true);
            expect(response.body).to.have.property('comments');
        });

        it('يجب أن يسمح بالإعجاب بالفيديو', async () => {
            const response = await request(app)
                .post(`/api/interactions/like/${videoId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body).to.have.property('success', true);
        });
    });

    describe('🔔 اختبارات الإشعارات', () => {
        
        it('يجب أن يعرض إشعارات المستخدم', async () => {
            const response = await request(app)
                .get('/api/notifications')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body).to.have.property('success', true);
            expect(response.body).to.have.property('notifications');
        });

        it('يجب أن يسمح بتحديد الإشعار كمقروء', async () => {
            // إنشاء إشعار أولاً
            const notification = await createTestNotification(userId);
            
            const response = await request(app)
                .put(`/api/notifications/${notification.id}/read`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body).to.have.property('success', true);
        });
    });

    describe('⚡ اختبارات الأداء', () => {
        
        it('يجب أن يستجيب API في وقت معقول', async () => {
            const startTime = Date.now();
            
            await request(app)
                .get('/api/content/videos')
                .expect(200);
            
            const responseTime = Date.now() - startTime;
            expect(responseTime).to.be.lessThan(1000); // أقل من ثانية
        });

        it('يجب أن يتعامل مع طلبات متعددة متزامنة', async () => {
            const promises = [];
            
            for (let i = 0; i < 10; i++) {
                promises.push(
                    request(app)
                        .get('/api/content/videos')
                        .expect(200)
                );
            }
            
            const responses = await Promise.all(promises);
            expect(responses).to.have.length(10);
        });
    });

    describe('🛡️ اختبارات الأمان', () => {
        
        it('يجب أن يحمي من SQL Injection', async () => {
            const maliciousInput = "'; DROP TABLE users; --";
            
            const response = await request(app)
                .get(`/api/search/videos?q=${encodeURIComponent(maliciousInput)}`)
                .expect(200);

            expect(response.body).to.have.property('success', true);
        });

        it('يجب أن يحمي من XSS', async () => {
            const xssPayload = '<script>alert("XSS")</script>';
            
            const response = await request(app)
                .post('/api/comments')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    content: xssPayload,
                    video_id: videoId
                })
                .expect(400);

            expect(response.body).to.have.property('success', false);
        });

        it('يجب أن يطبق Rate Limiting', async () => {
            const promises = [];
            
            // إرسال طلبات كثيرة بسرعة
            for (let i = 0; i < 100; i++) {
                promises.push(
                    request(app)
                        .post('/api/auth/login')
                        .send({
                            email: '<EMAIL>',
                            password: 'wrong'
                        })
                );
            }
            
            const responses = await Promise.all(promises);
            const rateLimitedResponses = responses.filter(r => r.status === 429);
            
            expect(rateLimitedResponses.length).to.be.greaterThan(0);
        });
    });
});

// ===== وظائف مساعدة =====

async function cleanDatabase() {
    // تنظيف قاعدة البيانات للاختبار
    // يتم تنفيذها حسب نوع قاعدة البيانات المستخدمة
}

async function seedTestData() {
    // إنشاء بيانات اختبار أساسية
}

async function createTestVideos() {
    // إنشاء فيديوهات للاختبار
}

async function createTestNotification(userId) {
    // إنشاء إشعار تجريبي
    return { id: 1, user_id: userId, message: 'إشعار تجريبي' };
}

module.exports = {
    cleanDatabase,
    seedTestData,
    createTestVideos,
    createTestNotification
};
