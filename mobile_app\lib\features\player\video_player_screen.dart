import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:video_player/video_player.dart';
import 'package:wakelock/wakelock.dart';
import 'package:go_router/go_router.dart';

import '../../core/constants/app_constants.dart';
import '../../core/models/content_model.dart';
import '../../core/providers/content_provider.dart';
import '../../core/widgets/loading_widget.dart';

/// 🎬 شاشة مشغل الفيديو
class VideoPlayerScreen extends ConsumerStatefulWidget {
  final int contentId;
  final int? episodeId;

  const VideoPlayerScreen({
    super.key,
    required this.contentId,
    this.episodeId,
  });

  @override
  ConsumerState<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends ConsumerState<VideoPlayerScreen>
    with TickerProviderStateMixin {
  VideoPlayerController? _videoController;
  late AnimationController _controlsAnimationController;
  late Animation<double> _controlsAnimation;
  
  bool _isControlsVisible = true;
  bool _isPlaying = false;
  bool _isFullscreen = false;
  bool _isLoading = true;
  bool _hasError = false;
  bool _isBuffering = false;
  
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  double _volume = 1.0;
  double _playbackSpeed = 1.0;
  
  ContentModel? _content;
  EpisodeModel? _currentEpisode;
  List<EpisodeModel> _episodes = [];
  
  // Progress tracking
  DateTime _watchStartTime = DateTime.now();
  Duration _lastProgressUpdate = Duration.zero;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadContent();
    _enableWakelock();
    _setFullscreenMode();
  }

  @override
  void dispose() {
    _videoController?.dispose();
    _controlsAnimationController.dispose();
    _disableWakelock();
    _exitFullscreenMode();
    super.dispose();
  }

  void _initializeAnimations() {
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _controlsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    ));

    _controlsAnimationController.forward();
  }

  Future<void> _loadContent() async {
    try {
      final content = await ref.read(contentProvider.notifier)
          .getContentDetails(widget.contentId);
      
      if (content != null) {
        setState(() {
          _content = content;
        });

        if (content.type == ContentType.series) {
          await _loadEpisodes();
        } else {
          await _initializeVideo(content.videoUrl);
        }
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _isLoading = false;
      });
    }
  }

  Future<void> _loadEpisodes() async {
    if (_content?.seasons?.isNotEmpty == true) {
      final firstSeason = _content!.seasons!.first;
      if (firstSeason.episodes?.isNotEmpty == true) {
        setState(() {
          _episodes = firstSeason.episodes!;
        });

        // تحديد الحلقة الحالية
        if (widget.episodeId != null) {
          _currentEpisode = _episodes.firstWhere(
            (ep) => ep.id == widget.episodeId,
            orElse: () => _episodes.first,
          );
        } else {
          _currentEpisode = _episodes.first;
        }

        await _initializeVideo(_currentEpisode!.videoUrl);
      }
    }
  }

  Future<void> _initializeVideo(String? videoUrl) async {
    if (videoUrl == null || videoUrl.isEmpty) {
      setState(() {
        _hasError = true;
        _isLoading = false;
      });
      return;
    }

    try {
      _videoController = VideoPlayerController.network(videoUrl);
      
      await _videoController!.initialize();
      
      setState(() {
        _totalDuration = _videoController!.value.duration;
        _isLoading = false;
      });

      // إعداد المستمعين
      _videoController!.addListener(_videoListener);
      
      // تحميل آخر موضع مشاهدة
      await _loadWatchProgress();
      
      // بدء تتبع التقدم
      _startProgressTracking();

    } catch (e) {
      setState(() {
        _hasError = true;
        _isLoading = false;
      });
    }
  }

  void _videoListener() {
    if (_videoController == null) return;

    final value = _videoController!.value;
    
    setState(() {
      _currentPosition = value.position;
      _isPlaying = value.isPlaying;
      _isBuffering = value.isBuffering;
    });

    // إخفاء عناصر التحكم تلقائياً أثناء التشغيل
    if (value.isPlaying && _isControlsVisible) {
      _hideControlsAfterDelay();
    }
  }

  Future<void> _loadWatchProgress() async {
    // TODO: تحميل آخر موضع مشاهدة من الخادم
    // يمكن تنفيذ هذا لاحقاً
  }

  void _startProgressTracking() {
    // تحديث التقدم كل 30 ثانية
    Future.delayed(const Duration(seconds: 30), () {
      if (mounted && _videoController != null) {
        _updateWatchProgress();
        _startProgressTracking();
      }
    });
  }

  Future<void> _updateWatchProgress() async {
    if (_videoController == null || _content == null) return;

    final currentTime = _videoController!.value.position;
    final totalTime = _videoController!.value.duration;

    if (currentTime.inSeconds > 0 && totalTime.inSeconds > 0) {
      await ref.read(contentProvider.notifier).updateWatchProgress(
        _content!.id,
        currentTime.inSeconds,
        totalTime.inSeconds,
      );
    }
  }

  void _enableWakelock() {
    Wakelock.enable();
  }

  void _disableWakelock() {
    Wakelock.disable();
  }

  void _setFullscreenMode() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  void _exitFullscreenMode() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: _buildPlayerBody(),
    );
  }

  Widget _buildPlayerBody() {
    if (_isLoading) {
      return const Center(
        child: LoadingWidget(message: 'جاري تحميل الفيديو...'),
      );
    }

    if (_hasError) {
      return _buildErrorWidget();
    }

    if (_videoController == null || !_videoController!.value.isInitialized) {
      return const Center(
        child: LoadingWidget(message: 'جاري تهيئة المشغل...'),
      );
    }

    return GestureDetector(
      onTap: _toggleControls,
      child: Stack(
        children: [
          // Video Player
          Center(
            child: AspectRatio(
              aspectRatio: _videoController!.value.aspectRatio,
              child: VideoPlayer(_videoController!),
            ),
          ),

          // Loading Overlay
          if (_isBuffering)
            const Center(
              child: CircularProgressIndicator(
                color: AppConstants.primaryColor,
              ),
            ),

          // Controls Overlay
          AnimatedBuilder(
            animation: _controlsAnimation,
            builder: (context, child) {
              return Opacity(
                opacity: _isControlsVisible ? _controlsAnimation.value : 0.0,
                child: _buildControlsOverlay(),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppConstants.errorColor,
          ),
          
          const SizedBox(height: AppConstants.spacingLG),
          
          const Text(
            'خطأ في تحميل الفيديو',
            style: TextStyle(
              color: Colors.white,
              fontSize: AppConstants.fontSizeXL,
              fontWeight: AppConstants.fontWeightBold,
            ),
          ),
          
          const SizedBox(height: AppConstants.spacingMD),
          
          const Text(
            'تعذر تحميل الفيديو. يرجى المحاولة مرة أخرى.',
            style: TextStyle(
              color: Colors.white70,
              fontSize: AppConstants.fontSizeMD,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppConstants.spacingXL),
          
          ElevatedButton.icon(
            onPressed: _retryVideo,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlsOverlay() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.7),
            Colors.transparent,
            Colors.transparent,
            Colors.black.withOpacity(0.7),
          ],
          stops: const [0.0, 0.3, 0.7, 1.0],
        ),
      ),
      child: Column(
        children: [
          // Top Controls
          _buildTopControls(),
          
          // Center Controls
          Expanded(
            child: _buildCenterControls(),
          ),
          
          // Bottom Controls
          _buildBottomControls(),
        ],
      ),
    );
  }

  Widget _buildTopControls() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingLG),
        child: Row(
          children: [
            // Back Button
            IconButton(
              onPressed: () => context.pop(),
              icon: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 28,
              ),
            ),
            
            const SizedBox(width: AppConstants.spacingMD),
            
            // Title
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _content?.title ?? '',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: AppConstants.fontSizeLG,
                      fontWeight: AppConstants.fontWeightBold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  if (_currentEpisode != null)
                    Text(
                      'الحلقة ${_currentEpisode!.episodeNumber}: ${_currentEpisode!.title}',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: AppConstants.fontSizeMD,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
            
            // Episodes Button (for series)
            if (_content?.type == ContentType.series && _episodes.isNotEmpty)
              IconButton(
                onPressed: _showEpisodesBottomSheet,
                icon: const Icon(
                  Icons.list,
                  color: Colors.white,
                  size: 28,
                ),
              ),
            
            // Settings Button
            IconButton(
              onPressed: _showSettingsBottomSheet,
              icon: const Icon(
                Icons.settings,
                color: Colors.white,
                size: 28,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCenterControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Previous Episode
        if (_currentEpisode != null && _canGoPreviousEpisode())
          IconButton(
            onPressed: _playPreviousEpisode,
            icon: const Icon(
              Icons.skip_previous,
              color: Colors.white,
              size: 48,
            ),
          ),
        
        // Rewind
        IconButton(
          onPressed: _rewind,
          icon: const Icon(
            Icons.replay_10,
            color: Colors.white,
            size: 48,
          ),
        ),
        
        // Play/Pause
        IconButton(
          onPressed: _togglePlayPause,
          icon: Icon(
            _isPlaying ? Icons.pause : Icons.play_arrow,
            color: Colors.white,
            size: 64,
          ),
        ),
        
        // Fast Forward
        IconButton(
          onPressed: _fastForward,
          icon: const Icon(
            Icons.forward_10,
            color: Colors.white,
            size: 48,
          ),
        ),
        
        // Next Episode
        if (_currentEpisode != null && _canGoNextEpisode())
          IconButton(
            onPressed: _playNextEpisode,
            icon: const Icon(
              Icons.skip_next,
              color: Colors.white,
              size: 48,
            ),
          ),
      ],
    );
  }

  Widget _buildBottomControls() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingLG),
        child: Column(
          children: [
            // Progress Bar
            Row(
              children: [
                Text(
                  _formatDuration(_currentPosition),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: AppConstants.fontSizeSM,
                  ),
                ),
                
                const SizedBox(width: AppConstants.spacingMD),
                
                Expanded(
                  child: SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: AppConstants.primaryColor,
                      inactiveTrackColor: Colors.white30,
                      thumbColor: AppConstants.primaryColor,
                      overlayColor: AppConstants.primaryColor.withOpacity(0.3),
                      trackHeight: 4,
                      thumbShape: const RoundSliderThumbShape(
                        enabledThumbRadius: 8,
                      ),
                    ),
                    child: Slider(
                      value: _currentPosition.inMilliseconds.toDouble(),
                      max: _totalDuration.inMilliseconds.toDouble(),
                      onChanged: _onSeek,
                    ),
                  ),
                ),
                
                const SizedBox(width: AppConstants.spacingMD),
                
                Text(
                  _formatDuration(_totalDuration),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: AppConstants.fontSizeSM,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacingMD),
            
            // Bottom Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Volume
                Row(
                  children: [
                    IconButton(
                      onPressed: _toggleMute,
                      icon: Icon(
                        _volume > 0 ? Icons.volume_up : Icons.volume_off,
                        color: Colors.white,
                      ),
                    ),
                    
                    SizedBox(
                      width: 100,
                      child: SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                          activeTrackColor: Colors.white,
                          inactiveTrackColor: Colors.white30,
                          thumbColor: Colors.white,
                          trackHeight: 2,
                          thumbShape: const RoundSliderThumbShape(
                            enabledThumbRadius: 6,
                          ),
                        ),
                        child: Slider(
                          value: _volume,
                          onChanged: _onVolumeChanged,
                        ),
                      ),
                    ),
                  ],
                ),
                
                // Playback Speed
                TextButton(
                  onPressed: _showSpeedOptions,
                  child: Text(
                    '${_playbackSpeed}x',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: AppConstants.fontWeightMedium,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _toggleControls() {
    setState(() {
      _isControlsVisible = !_isControlsVisible;
    });

    if (_isControlsVisible) {
      _controlsAnimationController.forward();
      _hideControlsAfterDelay();
    } else {
      _controlsAnimationController.reverse();
    }
  }

  void _hideControlsAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _isPlaying && _isControlsVisible) {
        setState(() {
          _isControlsVisible = false;
        });
        _controlsAnimationController.reverse();
      }
    });
  }

  void _togglePlayPause() {
    if (_videoController == null) return;

    if (_isPlaying) {
      _videoController!.pause();
    } else {
      _videoController!.play();
    }
  }

  void _rewind() {
    if (_videoController == null) return;
    
    final newPosition = _currentPosition - const Duration(seconds: 10);
    _videoController!.seekTo(newPosition < Duration.zero ? Duration.zero : newPosition);
  }

  void _fastForward() {
    if (_videoController == null) return;
    
    final newPosition = _currentPosition + const Duration(seconds: 10);
    _videoController!.seekTo(newPosition > _totalDuration ? _totalDuration : newPosition);
  }

  void _onSeek(double value) {
    if (_videoController == null) return;
    
    final position = Duration(milliseconds: value.toInt());
    _videoController!.seekTo(position);
  }

  void _toggleMute() {
    setState(() {
      if (_volume > 0) {
        _volume = 0;
      } else {
        _volume = 1.0;
      }
    });
    _videoController?.setVolume(_volume);
  }

  void _onVolumeChanged(double value) {
    setState(() {
      _volume = value;
    });
    _videoController?.setVolume(_volume);
  }

  bool _canGoPreviousEpisode() {
    if (_currentEpisode == null || _episodes.isEmpty) return false;
    
    final currentIndex = _episodes.indexWhere((ep) => ep.id == _currentEpisode!.id);
    return currentIndex > 0;
  }

  bool _canGoNextEpisode() {
    if (_currentEpisode == null || _episodes.isEmpty) return false;
    
    final currentIndex = _episodes.indexWhere((ep) => ep.id == _currentEpisode!.id);
    return currentIndex < _episodes.length - 1;
  }

  void _playPreviousEpisode() {
    if (!_canGoPreviousEpisode()) return;
    
    final currentIndex = _episodes.indexWhere((ep) => ep.id == _currentEpisode!.id);
    final previousEpisode = _episodes[currentIndex - 1];
    
    _playEpisode(previousEpisode);
  }

  void _playNextEpisode() {
    if (!_canGoNextEpisode()) return;
    
    final currentIndex = _episodes.indexWhere((ep) => ep.id == _currentEpisode!.id);
    final nextEpisode = _episodes[currentIndex + 1];
    
    _playEpisode(nextEpisode);
  }

  Future<void> _playEpisode(EpisodeModel episode) async {
    setState(() {
      _currentEpisode = episode;
      _isLoading = true;
    });

    await _videoController?.dispose();
    await _initializeVideo(episode.videoUrl);
  }

  void _showEpisodesBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black87,
      isScrollControlled: true,
      builder: (context) => EpisodesBottomSheet(
        episodes: _episodes,
        currentEpisode: _currentEpisode,
        onEpisodeSelected: _playEpisode,
      ),
    );
  }

  void _showSettingsBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black87,
      builder: (context) => PlayerSettingsBottomSheet(
        currentSpeed: _playbackSpeed,
        onSpeedChanged: _onSpeedChanged,
      ),
    );
  }

  void _showSpeedOptions() {
    _showSettingsBottomSheet();
  }

  void _onSpeedChanged(double speed) {
    setState(() {
      _playbackSpeed = speed;
    });
    _videoController?.setPlaybackSpeed(speed);
  }

  void _retryVideo() {
    setState(() {
      _hasError = false;
      _isLoading = true;
    });
    
    _loadContent();
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }
}

/// 📋 ورقة الحلقات السفلية
class EpisodesBottomSheet extends StatelessWidget {
  final List<EpisodeModel> episodes;
  final EpisodeModel? currentEpisode;
  final Function(EpisodeModel) onEpisodeSelected;

  const EpisodesBottomSheet({
    super.key,
    required this.episodes,
    required this.currentEpisode,
    required this.onEpisodeSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      padding: const EdgeInsets.all(AppConstants.spacingLG),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white54,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          const SizedBox(height: AppConstants.spacingLG),
          
          // Title
          const Text(
            'الحلقات',
            style: TextStyle(
              color: Colors.white,
              fontSize: AppConstants.fontSizeXL,
              fontWeight: AppConstants.fontWeightBold,
            ),
          ),
          
          const SizedBox(height: AppConstants.spacingLG),
          
          // Episodes List
          Expanded(
            child: ListView.builder(
              itemCount: episodes.length,
              itemBuilder: (context, index) {
                final episode = episodes[index];
                final isCurrentEpisode = episode.id == currentEpisode?.id;
                
                return ListTile(
                  leading: Container(
                    width: 60,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white12,
                      borderRadius: BorderRadius.circular(AppConstants.radiusSM),
                    ),
                    child: episode.thumbnail != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(AppConstants.radiusSM),
                            child: Image.network(
                              episode.thumbnail!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return const Icon(
                                  Icons.play_circle_outline,
                                  color: Colors.white54,
                                );
                              },
                            ),
                          )
                        : const Icon(
                            Icons.play_circle_outline,
                            color: Colors.white54,
                          ),
                  ),
                  
                  title: Text(
                    'الحلقة ${episode.episodeNumber}: ${episode.title}',
                    style: TextStyle(
                      color: isCurrentEpisode ? AppConstants.primaryColor : Colors.white,
                      fontWeight: isCurrentEpisode 
                          ? AppConstants.fontWeightBold 
                          : AppConstants.fontWeightMedium,
                    ),
                  ),
                  
                  subtitle: Text(
                    episode.formattedDuration,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: AppConstants.fontSizeSM,
                    ),
                  ),
                  
                  trailing: isCurrentEpisode
                      ? const Icon(
                          Icons.play_arrow,
                          color: AppConstants.primaryColor,
                        )
                      : null,
                  
                  onTap: () {
                    Navigator.pop(context);
                    onEpisodeSelected(episode);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

/// ⚙️ ورقة إعدادات المشغل السفلية
class PlayerSettingsBottomSheet extends StatelessWidget {
  final double currentSpeed;
  final Function(double) onSpeedChanged;

  const PlayerSettingsBottomSheet({
    super.key,
    required this.currentSpeed,
    required this.onSpeedChanged,
  });

  @override
  Widget build(BuildContext context) {
    final speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

    return Container(
      padding: const EdgeInsets.all(AppConstants.spacingLG),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white54,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          const SizedBox(height: AppConstants.spacingLG),
          
          // Title
          const Text(
            'سرعة التشغيل',
            style: TextStyle(
              color: Colors.white,
              fontSize: AppConstants.fontSizeXL,
              fontWeight: AppConstants.fontWeightBold,
            ),
          ),
          
          const SizedBox(height: AppConstants.spacingLG),
          
          // Speed Options
          ...speeds.map((speed) {
            final isSelected = speed == currentSpeed;
            
            return ListTile(
              title: Text(
                '${speed}x',
                style: TextStyle(
                  color: isSelected ? AppConstants.primaryColor : Colors.white,
                  fontWeight: isSelected 
                      ? AppConstants.fontWeightBold 
                      : AppConstants.fontWeightMedium,
                ),
              ),
              
              trailing: isSelected
                  ? const Icon(
                      Icons.check,
                      color: AppConstants.primaryColor,
                    )
                  : null,
              
              onTap: () {
                Navigator.pop(context);
                onSpeedChanged(speed);
              },
            );
          }).toList(),
          
          const SizedBox(height: AppConstants.spacingLG),
        ],
      ),
    );
  }
}
