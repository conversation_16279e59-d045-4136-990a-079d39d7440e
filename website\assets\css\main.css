/* 🎨 ملف CSS الرئيسي لمنصة البث الشاملة */

/* ==========================================
   🔧 المتغيرات والألوان الأساسية
   ========================================== */
:root {
    --primary-color: #e50914;
    --secondary-color: #221f1f;
    --dark-color: #141414;
    --light-color: #f5f5f5;
    --text-color: #ffffff;
    --text-muted: #b3b3b3;
    --border-color: #333333;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    
    /* خطوط */
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-base: 16px;
    --font-weight-normal: 400;
    --font-weight-bold: 600;
    
    /* مسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 3rem;
    
    /* انتقالات */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* ظلال */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    /* حدود دائرية */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 1rem;
}

/* ==========================================
   🌐 الإعدادات العامة
   ========================================== */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--dark-color);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* تحسين النصوص العربية */
body[dir="rtl"] {
    text-align: right;
}

/* ==========================================
   🧭 شريط التنقل
   ========================================== */
.navbar {
    background: linear-gradient(135deg, var(--dark-color) 0%, var(--secondary-color) 100%);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition-normal);
    z-index: 1030;
}

.navbar-brand img {
    transition: var(--transition-fast);
}

.navbar-brand:hover img {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    color: var(--text-color) !important;
    font-weight: var(--font-weight-normal);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
    position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
    color: var(--primary-color) !important;
    background-color: rgba(229, 9, 20, 0.1);
}

.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition-fast);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 80%;
}

/* قائمة منسدلة */
.dropdown-menu {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    animation: fadeInDown 0.3s ease-out;
}

.dropdown-item {
    color: var(--text-color);
    transition: var(--transition-fast);
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: var(--primary-color);
    color: var(--text-color);
}

/* ==========================================
   🔍 شريط البحث
   ========================================== */
.navbar .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.navbar .form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
    color: var(--text-color);
}

.navbar .form-control::placeholder {
    color: var(--text-muted);
}

/* ==========================================
   📱 المحتوى الرئيسي
   ========================================== */
.main-content {
    margin-top: 76px; /* ارتفاع شريط التنقل */
    min-height: calc(100vh - 76px);
}

/* ==========================================
   🎬 بطاقات المحتوى
   ========================================== */
.content-card {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    transition: var(--transition-normal);
    position: relative;
    cursor: pointer;
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.content-card img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: var(--transition-normal);
}

.content-card:hover img {
    transform: scale(1.05);
}

.content-card-body {
    padding: var(--spacing-md);
}

.content-card-title {
    font-size: 1.1rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    color: var(--text-color);
}

.content-card-text {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-sm);
}

.content-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.content-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.content-rating .fa-star {
    color: #ffc107;
}

/* شارات النوع */
.content-type-badge {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background-color: var(--primary-color);
    color: var(--text-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
}

/* شارة VIP */
.vip-badge {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #000;
}

/* ==========================================
   🎯 الأزرار
   ========================================== */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: var(--font-weight-bold);
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #b8070f;
    border-color: #b8070f;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* تأثير الموجة للأزرار */
.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:active::before {
    width: 300px;
    height: 300px;
}

/* ==========================================
   📝 النماذج
   ========================================== */
.form-control {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.form-control:focus {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
    color: var(--text-color);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-label {
    color: var(--text-color);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
}

/* ==========================================
   🔔 التنبيهات
   ========================================== */
.alert {
    border-radius: var(--border-radius-md);
    border: none;
    font-weight: var(--font-weight-bold);
    animation: slideInDown 0.5s ease-out;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* ==========================================
   ⏳ مؤشر التحميل
   ========================================== */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(20, 20, 20, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.loading-spinner.show {
    opacity: 1;
    visibility: visible;
}

/* ==========================================
   🦶 التذييل
   ========================================== */
footer {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--dark-color) 100%);
    border-top: 1px solid var(--border-color);
}

footer h5,
footer h6 {
    color: var(--text-color);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-md);
}

footer a {
    color: var(--text-muted);
    text-decoration: none;
    transition: var(--transition-fast);
}

footer a:hover {
    color: var(--primary-color);
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: var(--transition-fast);
}

.social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

/* ==========================================
   📱 الاستجابة للشاشات المختلفة
   ========================================== */
@media (max-width: 768px) {
    .main-content {
        margin-top: 60px;
    }
    
    .content-card img {
        height: 250px;
    }
    
    .navbar .form-control {
        margin-bottom: var(--spacing-sm);
    }
}

@media (max-width: 576px) {
    .content-card img {
        height: 200px;
    }
    
    .content-card-body {
        padding: var(--spacing-sm);
    }
}

/* ==========================================
   🎭 الرسوم المتحركة
   ========================================== */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* فئات مساعدة للرسوم المتحركة */
.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.fade-in-down {
    animation: fadeInDown 0.5s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* ==========================================
   🎨 تحسينات إضافية
   ========================================== */
/* تحسين التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: #b8070f;
}

/* تحسين التركيز */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* إخفاء العناصر بصرياً مع الحفاظ على إمكانية الوصول */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
